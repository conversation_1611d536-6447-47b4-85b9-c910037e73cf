from src.utils.classes import Response as MiddleResponse
from src.modules.estado_financiero.models import EstadoFinanciero
from src.modules.estado_financiero.serializers import EstadoFinancieroSerializer
from src.modules.agrupadores.models import Agrupador
from src.modules.cuentas.models import Cuenta
from src.modules.ef_cuentas.models import EFCuenta
from src.modules.total_agrupador.models import TotalAgrupador
from django.core.exceptions import ObjectDoesNotExist
import pandas as pd
from datetime import datetime
from decimal import Decimal
import re
import os

class EstadoFinancieroController:
    def __init__(self):
        pass

    def crear_simulacion(self, data):
        """
        Crea una simulación de estado financiero basada en un estado financiero de referencia.

        Args:
            data: Diccionario con los datos para crear la simulación
                - id_estado_financiero_ref: ID del estado financiero de referencia
                - id_usuario: ID del usuario que crea la simulación
                - agrupadores: Diccionario con los valores de los agrupadores
                - ratios: Diccionario con los valores de los ratios

        Returns:
            MiddleResponse: Objeto de respuesta con el resultado de la operación
        """
        from src.modules.ratios.models import Ratio
        from src.modules.ratios_ef.models import RatioEF
        try:
            # 1. Obtener el estado financiero de referencia
            try:
                ef_referencia = EstadoFinanciero.objects.get(int_idEstadoFinanciero=data.get('id_estado_financiero_ref'))
            except ObjectDoesNotExist:
                return MiddleResponse("Estado financiero de referencia no encontrado", None, False)

            # 2. Contar cuántas simulaciones existen para este estado financiero
            num_simulaciones = EstadoFinanciero.objects.filter(
                int_referenciaSimulacion=ef_referencia,
                int_tipoRegistro=1
            ).count()

            # 3. Crear el nombre para la simulación
            nombre_simulacion = f"{ef_referencia.str_nombre} (Simulación {num_simulaciones + 1})"

            # 4. Crear el nuevo estado financiero (simulación)
            simulacion = EstadoFinanciero(
                str_nombre=nombre_simulacion,
                dt_fechaRegistro=datetime.now(),
                int_idEmpresa=ef_referencia.int_idEmpresa,
                int_idUsuarios_id=data.get('id_usuario'),
                int_tipoRegistro=1,  # 1: simulación
                int_referenciaSimulacion=ef_referencia,
                int_tipoPeriodo=ef_referencia.int_tipoPeriodo,
                int_tipoEstadoFinanciero=1,  # Siempre será tipo 1 (Estado de Situación Financiera)
                dt_fechaInicioPeriodo=ef_referencia.dt_fechaInicioPeriodo,
                dt_fechaFinPeriodo=ef_referencia.dt_fechaFinPeriodo
            )
            simulacion.save()

            # 5. Guardar los agrupadores
            agrupadores_guardados = []
            agrupadores_no_guardados = []

            if 'agrupadores' in data:
                for nombre_agrupador, valor in data['agrupadores'].items():
                    # Buscar el agrupador según el nombre
                    agrupador = None

                    # Para "patrimonio" buscar el agrupador patrimonio
                    if nombre_agrupador.lower() == 'patrimonio':
                        agrupador = Agrupador.objects.filter(str_nombre__iexact='patrimonio').first()

                    # Para "activo total" buscar si existe un agrupador activo total, si no crear uno
                    elif nombre_agrupador.lower() == 'activo total':
                        agrupador = Agrupador.objects.filter(str_nombre__iexact='activo total').first()
                        if not agrupador:
                            agrupador = Agrupador.objects.create(
                                str_nombre='ACTIVO TOTAL',
                                str_nombre_subagrupador=None
                            )

                    # Para "utilidad neta" buscar el agrupador ER-UN
                    elif nombre_agrupador.lower() == 'utilidad neta':
                        agrupador = Agrupador.objects.filter(str_nombre__iexact='ER-UN').first()

                    # Para "ventas" buscar el agrupador ventas netas
                    elif nombre_agrupador.lower() == 'ventas':
                        agrupador = Agrupador.objects.filter(str_nombre__iexact='ventas netas').first()

                    # Si se encontró el agrupador, guardar el valor
                    if agrupador:
                        TotalAgrupador.objects.create(
                            int_idEstadoFinanciero=simulacion,
                            int_idAgrupador=agrupador,
                            db_resultadoAgrupador=valor
                        )
                        agrupadores_guardados.append(nombre_agrupador)
                    else:
                        agrupadores_no_guardados.append(nombre_agrupador)

            # 6. Guardar los ratios
            ratios_guardados = []
            ratios_no_guardados = []

            if 'ratios' in data:
                for nombre_ratio, valor in data['ratios'].items():
                    # Mapear los nombres de los ratios a los nombres en la base de datos
                    nombre_ratio_db = None

                    if nombre_ratio.lower() == 'margen neto':
                        nombre_ratio_db = 'Margen Neto'
                    elif nombre_ratio.lower() == 'rotacion':
                        nombre_ratio_db = 'Rotación'
                    elif nombre_ratio.lower() == 'apalancamiento':
                        nombre_ratio_db = 'Apalancamiento'
                    elif nombre_ratio.lower() == 'roe':
                        nombre_ratio_db = 'ROE'
                    elif nombre_ratio.lower() == 'roa':
                        nombre_ratio_db = 'ROA'

                    if nombre_ratio_db:
                        # Buscar el ratio por nombre
                        ratio = Ratio.objects.filter(
                            str_descripcion=nombre_ratio_db,
                            int_idEmpresa=ef_referencia.int_idEmpresa
                        ).first()

                        if ratio:
                            RatioEF.objects.create(
                                int_idEstadoFinanciero=simulacion,
                                int_idRatios=ratio,
                                db_valorRatio=valor
                            )
                            ratios_guardados.append(nombre_ratio)
                        else:
                            ratios_no_guardados.append(nombre_ratio)
                    else:
                        ratios_no_guardados.append(nombre_ratio)

            # 7. Preparar mensaje de respuesta
            mensaje = f"Simulación '{nombre_simulacion}' creada exitosamente."

            if agrupadores_guardados:
                mensaje += f" Agrupadores guardados: {', '.join(agrupadores_guardados)}."
            if agrupadores_no_guardados:
                mensaje += f" Agrupadores no encontrados: {', '.join(agrupadores_no_guardados)}."

            if ratios_guardados:
                mensaje += f" Ratios guardados: {', '.join(ratios_guardados)}."
            if ratios_no_guardados:
                mensaje += f" Ratios no encontrados: {', '.join(ratios_no_guardados)}."

            return MiddleResponse(mensaje, {
                'int_idEstadoFinanciero': simulacion.int_idEstadoFinanciero,
                'str_nombre': simulacion.str_nombre,
                'agrupadores_guardados': agrupadores_guardados,
                'ratios_guardados': ratios_guardados
            }, True)

        except Exception as e:
            return MiddleResponse(f"Error al crear la simulación: {str(e)}", None, False)

    def obtener_simulaciones_por_estado_financiero(self, id_estado_financiero):
        """
        Obtiene todas las simulaciones que tienen como estado financiero de referencia al ID proporcionado.

        Args:
            id_estado_financiero (int): ID del estado financiero de referencia

        Returns:
            MiddleResponse: Objeto de respuesta con las simulaciones encontradas
        """
        try:
            # Verificar que el estado financiero existe
            try:
                estado_financiero = EstadoFinanciero.objects.get(int_idEstadoFinanciero=id_estado_financiero)
            except ObjectDoesNotExist:
                return MiddleResponse(
                    message=f"No se encontró el estado financiero con ID {id_estado_financiero}",
                    data=None,
                    state=False
                )

            # Obtener todas las simulaciones que referencian a este estado financiero
            simulaciones = EstadoFinanciero.objects.filter(
                int_referenciaSimulacion=estado_financiero,
                int_tipoRegistro=1  # Solo simulaciones
            ).order_by('-dt_fechaRegistro')  # Ordenar por fecha de registro descendente

            if not simulaciones:
                return MiddleResponse(
                    message=f"No se encontraron simulaciones para el estado financiero con ID {id_estado_financiero}",
                    data=[],
                    state=True
                )

            # Serializar las simulaciones
            simulaciones_data = []
            for simulacion in simulaciones:
                simulaciones_data.append({
                    'int_idEstadoFinanciero': simulacion.int_idEstadoFinanciero,
                    'str_nombre': simulacion.str_nombre,
                    'dt_fechaRegistro': simulacion.dt_fechaRegistro.strftime('%d-%m-%Y %H:%M:%S'),
                    'int_idEmpresa': simulacion.int_idEmpresa.int_idEmpresa,
                    'int_idUsuarios': simulacion.int_idUsuarios.int_idUsuarios,
                    'str_nombreUsuario': f"{simulacion.int_idUsuarios.str_Nombres} {simulacion.int_idUsuarios.str_Apellidos}",
                    'dt_fechaFinPeriodo': simulacion.dt_fechaFinPeriodo.strftime('%d-%m-%Y') if simulacion.dt_fechaFinPeriodo else None,
                })

            return MiddleResponse(
                message=f"Se encontraron {len(simulaciones_data)} simulaciones para el estado financiero con ID {id_estado_financiero}",
                data=simulaciones_data,
                state=True
            )

        except Exception as e:
            return MiddleResponse(
                message=f"Error al obtener simulaciones: {str(e)}",
                data=None,
                state=False
            )

    def obtener_periodos_por_empresa(self, id_empresa, tipo_periodo):
        """
        Obtiene todos los periodos contables para una empresa específica, agrupando los estados financieros por periodo.
        Solo obtiene los periodos contables, no las simulaciones.

        Args:
            id_empresa (int): ID de la empresa
            tipo_periodo (int): ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)

        Returns:
            Response: Objeto Response con los datos de los periodos y sus estados financieros
        """
        try:
            # Mapear el tipo de periodo a los valores en la base de datos
            tipo_periodo_map = {
                1: "Anual",
                2: "Trimestral",
                3: "Mensual",
                4: "Semanal",
                5: "Diario"
            }

            # Validar el tipo de periodo
            if tipo_periodo not in tipo_periodo_map.keys():
                return MiddleResponse(
                    message=f"El tipo de periodo {tipo_periodo} no es válido. "
                    f"Los tipos de periodo válidos son: {', '.join(tipo_periodo_map.values())}",
                    state=False
                )

            # Obtener las fechas únicas de todos los periodos para esta empresa y tipo de periodo
            fechas_periodos = EstadoFinanciero.objects.filter(
                int_idEmpresa=id_empresa,
                int_tipoPeriodo=tipo_periodo,
                int_tipoRegistro=0  # Solo periodos contables, no simulaciones
            ).values_list('dt_fechaFinPeriodo', flat=True).distinct().order_by('-dt_fechaFinPeriodo')

            if not fechas_periodos:
                return MiddleResponse(
                    message=f"No se encontraron estados financieros para la empresa con ID {id_empresa} y tipo de periodo {tipo_periodo}",
                    state=False
                )

            resultado = []

            # Procesar cada periodo (fecha única)
            for fecha_periodo in fechas_periodos:
                # Formatear la fecha para mostrar como periodo contable
                periodo_contable = self._formatear_periodo_contable(fecha_periodo, tipo_periodo)

                # Buscar todos los estados financieros para este periodo
                estados_financieros_periodo = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                    dt_fechaFinPeriodo=fecha_periodo
                )

                # Lista para almacenar los estados financieros de este periodo
                estados_financieros_data = []

                # Procesar cada estado financiero del periodo
                for ef in estados_financieros_periodo:
                    # Crear el objeto de datos para este estado financiero
                    ef_data = {
                        "int_idEstadoFinanciero": ef.int_idEstadoFinanciero,
                        "int_tipoEstadoFinanciero": ef.int_tipoEstadoFinanciero,
                        "int_tipoPeriodo": ef.int_tipoPeriodo,
                        "dt_fechaRegistro": ef.dt_fechaRegistro,
                        "dt_fechaInicioPeriodo": ef.dt_fechaInicioPeriodo,
                        "dt_fechaFinPeriodo": ef.dt_fechaFinPeriodo
                    }
                    estados_financieros_data.append(ef_data)

                # Agregar este periodo al resultado si tiene estados financieros
                if estados_financieros_data:
                    periodo_data = {
                        "periodo_contable": periodo_contable,
                        "estados_financieros": estados_financieros_data
                    }
                    resultado.append(periodo_data)

            return MiddleResponse(
                message="Datos de periodos obtenidos exitosamente",
                data=resultado,
                state=True
            )

        except Exception as e:
            return MiddleResponse(
                message=f"Error al obtener los periodos: {str(e)}",
                state=False
            )

    def _formatear_periodo_contable(self, fecha_fin, tipo_periodo):
        """
        Formatea una fecha de fin de periodo para mostrar como periodo contable.

        Args:
            fecha_fin (datetime.date): Fecha de fin del periodo
            tipo_periodo (int): ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)

        Returns:
            str: Periodo contable formateado
        """
        # Formatear según el tipo de periodo
        if tipo_periodo == 1:  # Anual
            return fecha_fin.strftime("%Y")
        elif tipo_periodo == 2:  # Trimestral
            trimestre = (fecha_fin.month - 1) // 3 + 1
            return f"Q{trimestre} {fecha_fin.strftime('%Y')}"
        elif tipo_periodo == 3:  # Mensual
            return fecha_fin.strftime("%B %Y")
        elif tipo_periodo == 4:  # Semanal
            # Calcular la fecha de inicio (7 días antes)
            fecha_inicio = fecha_fin - pd.Timedelta(days=6)
            return f"{fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}"
        elif tipo_periodo == 5:  # Diario
            return fecha_fin.strftime("%d/%m/%Y")
        else:
            return f"Periodo {fecha_fin.strftime('%d/%m/%Y')}"

    def _convertir_valor_monetario(self, valor):
        """
        Convierte un valor monetario a Decimal, manejando diferentes formatos:
        - Números sin separadores: 1234.56
        - Números con separadores de miles: 1,234.56 o 1.234,56
        - Números con símbolos de moneda: $1,234.56

        Args:
            valor: El valor monetario a convertir (string, int, float)

        Returns:
            Decimal: El valor convertido a Decimal
        """
        if valor is None:
            return Decimal('0')

        # Si ya es un número (int o float), convertirlo directamente
        if isinstance(valor, (int, float)):
            return Decimal(str(valor))

        # Convertir a string si no lo es
        valor_str = str(valor).strip()

        # Eliminar símbolos de moneda y espacios
        valor_str = re.sub(r'[$€£¥]|\s', '', valor_str)

        # Detectar el formato del número
        if ',' in valor_str and '.' in valor_str:
            # Determinar cuál es el separador decimal
            ultimo_punto = valor_str.rindex('.')
            ultimo_coma = valor_str.rindex(',')

            if ultimo_punto > ultimo_coma:
                # Formato: 1,234.56 (coma para miles, punto para decimales)
                valor_str = valor_str.replace(',', '')
            else:
                # Formato: 1.234,56 (punto para miles, coma para decimales)
                valor_str = valor_str.replace('.', '')
                valor_str = valor_str.replace(',', '.')
        elif ',' in valor_str:
            # Podría ser 1,234 (sin decimales) o 1,23 (con decimales)
            # Asumimos que si hay una coma y está seguida por exactamente 2 dígitos, es decimal
            match = re.search(r',\d{2}$', valor_str)
            if match:
                valor_str = valor_str.replace(',', '.')
            else:
                valor_str = valor_str.replace(',', '')

        # Intentar convertir a Decimal
        try:
            return Decimal(valor_str)
        except:
            # Si falla, intentar eliminar todos los caracteres no numéricos excepto el punto decimal
            valor_limpio = re.sub(r'[^\d.-]', '', valor_str)
            try:
                return Decimal(valor_limpio)
            except:
                # Si aún falla, retornar 0
                return Decimal('0')

    def procesar_archivo_excel(self, archivo, data):
        """
        Procesa un archivo Excel con la estructura de la plantilla de estados financieros.

        Args:
            archivo: El archivo Excel subido
            data: Diccionario con los datos del body (int_idEmpresa, int_idUsuarios, ef_nombre)

        Returns:
            MiddleResponse: Objeto de respuesta con el resultado del procesamiento
        """
        try:
            # Validar que el archivo sea un Excel
            nombre_archivo = archivo.name
            extension = os.path.splitext(nombre_archivo)[1].lower()
            if extension not in ['.xlsx', '.xls']:
                return MiddleResponse("El archivo debe ser un Excel (.xlsx o .xls)", None, False)

            # Cargar el archivo Excel con pandas para obtener los valores calculados de las fórmulas
            try:
                # Usar pandas para leer el archivo Excel, manteniendo los valores originales
                # Importante: header=None para asegurar que no se salte la primera fila
                df = pd.read_excel(
                    archivo,
                    engine='openpyxl',
                    keep_default_na=False,
                    header=None  # Esto es crucial para no perder la primera fila
                )
                # Convertir valores vacíos a strings vacíos para facilitar la búsqueda
                df = df.fillna('')
                print(f"Dimensiones del DataFrame: {df.shape}")

                # Imprimir las primeras filas para verificar que se están leyendo correctamente
                print("Primeras filas del DataFrame:")
                for i in range(min(5, len(df))):
                    print(f"Fila {i}: {df.iloc[i].tolist()}")

                # Verificar que el archivo tenga la estructura esperada
                if len(df) < 4:  # Al menos debe tener las 4 etiquetas principales
                    return MiddleResponse("El archivo Excel no tiene suficientes filas para contener la estructura esperada", None, False)

                # Verificar que al menos una de las primeras filas contenga la etiqueta [Tipo Estado Financiero]
                tipo_ef_encontrado = False
                for i in range(min(5, len(df))):
                    if df.iloc[i, 0] == "[Tipo Estado Financiero]":
                        tipo_ef_encontrado = True
                        break

                if not tipo_ef_encontrado:
                    print("ADVERTENCIA: No se encontró la etiqueta [Tipo Estado Financiero] en las primeras filas")
            except Exception as e:
                return MiddleResponse(f"Error al leer el archivo Excel: {str(e)}", None, False)

            # Inicializar variables para almacenar los datos extraídos
            tipo_estado_financiero = None
            tipo_periodo = None
            fecha_inicio_periodo = None
            fecha_fin_periodo = None

            # Diccionarios para almacenar los datos de agrupadores, subagrupadores y cuentas
            agrupadores = {}
            cuentas = []

            # Variables para controlar el estado del procesamiento
            agrupador_actual = None
            subagrupador_actual = None

            # Convertir el DataFrame a una lista de listas para facilitar el procesamiento
            # Esto nos permite acceder a las celdas por índice de fila y columna
            excel_data = df.values.tolist()

            # Asegurar que todas las filas tengan al menos 3 columnas (A, B, C)
            for i in range(len(excel_data)):
                while len(excel_data[i]) < 3:
                    excel_data[i].append('')

            # 1. Buscar las etiquetas principales
            # Imprimir las primeras filas para depuración
            print(f"Primeras 10 filas del Excel después de la conversión:")
            for i in range(min(10, len(excel_data))):
                print(f"Fila {i}: {excel_data[i]}")

            for row_idx, row_data in enumerate(excel_data):
                if row_idx >= len(excel_data):
                    break

                # Obtener el valor de la primera columna (A)
                cell_value = row_data[0] if len(row_data) > 0 else ""

                # Imprimir para depuración
                print(f"Procesando fila {row_idx}, valor: '{cell_value}'")

                # Si encontramos un agrupador, detenemos la búsqueda de etiquetas principales
                if cell_value == "[# Agrupador]":
                    print(f"Encontrado [# Agrupador] en fila {row_idx}")
                    break

                # Buscar las etiquetas principales
                # 1: Estado de Situación Financiera, 2: Estado de Resultados
                if cell_value == "[Tipo Estado Financiero]":
                    valor = row_data[1] if len(row_data) > 1 else None
                    print(f"Encontrado [Tipo Estado Financiero], valor: '{valor}'")
                    if valor is not None and valor != "":
                        try:
                            tipo_estado_financiero = int(valor)
                            print(f"Tipo Estado Financiero convertido a: {tipo_estado_financiero}")
                        except ValueError:
                            print(f"Error al convertir [Tipo Estado Financiero]: '{valor}' no es un número válido")
                            return MiddleResponse(f"El valor de [Tipo Estado Financiero] debe ser un número (1 o 2)", None, False)

                # 1: Anual, 2: Trimestral, 3: Mensual
                elif cell_value == "[Tipo Periodo]":
                    valor = row_data[1] if len(row_data) > 1 else None
                    print(f"Encontrado [Tipo Periodo], valor: '{valor}'")
                    if valor is not None and valor != "":
                        try:
                            tipo_periodo = int(valor)
                            print(f"Tipo Periodo convertido a: {tipo_periodo}")
                        except ValueError:
                            print(f"Error al convertir [Tipo Periodo]: '{valor}' no es un número válido")
                            return MiddleResponse(f"El valor de [Tipo Periodo] debe ser un número (1, 2 o 3)", None, False)

                # Formato de fecha: DD/MM/YYYY
                elif cell_value == "[Fecha Inicio Periodo]":
                    valor = row_data[1] if len(row_data) > 1 else None
                    print(f"Encontrado [Fecha Inicio Periodo], valor: '{valor}', tipo: {type(valor)}")
                    if valor is not None and valor != "":
                        if isinstance(valor, datetime):
                            print(f"Valor ya es datetime: {valor}")
                            fecha_inicio_periodo = valor
                        else:
                            try:
                                # Si pandas ya lo convirtió a datetime
                                if pd.isna(valor):
                                    print(f"Valor es NaN, continuando")
                                    continue

                                if isinstance(valor, pd.Timestamp):
                                    print(f"Valor es Timestamp: {valor}")
                                    fecha_inicio_periodo = valor.to_pydatetime()
                                    print(f"Convertido a datetime: {fecha_inicio_periodo}")
                                else:
                                    print(f"Intentando convertir string a fecha: {valor}")
                                    # Intentar convertir el string a fecha
                                    # Limpiar el valor y manejar diferentes formatos de separadores
                                    valor_str = str(valor).strip()

                                    # Detectar el separador (puede ser / o -)
                                    if '/' in valor_str:
                                        fecha_parts = valor_str.split('/')
                                    elif '-' in valor_str:
                                        fecha_parts = valor_str.split('-')
                                    else:
                                        fecha_parts = []

                                    if len(fecha_parts) == 3:
                                        # Determinar el formato (DD/MM/YYYY o MM/DD/YYYY)
                                        # Asumimos que si el primer número es <= 12, podría ser mes o día
                                        # Si el segundo número es > 12, entonces el formato es DD/MM/YYYY
                                        try:
                                            num1, num2, num3 = int(fecha_parts[0]), int(fecha_parts[1]), int(fecha_parts[2])

                                            # Si el segundo número es > 12, asumimos formato MM/DD/YYYY
                                            if num2 > 12:
                                                month, day, year = num1, num2, num3
                                            # Si el primer número es > 12, asumimos formato DD/MM/YYYY
                                            elif num1 > 12:
                                                day, month, year = num1, num2, num3
                                            # Si ambos son <= 12, asumimos formato DD/MM/YYYY (estándar en muchos países)
                                            else:
                                                day, month, year = num1, num2, num3

                                            # Asegurar que el año tenga 4 dígitos
                                            if year < 100:
                                                year += 2000

                                            fecha_inicio_periodo = datetime(year, month, day)
                                        except ValueError:
                                            return MiddleResponse(f"Fecha inválida en [Fecha Inicio Periodo]: {valor_str}", None, False)
                                    else:
                                        return MiddleResponse(f"El formato de [Fecha Inicio Periodo] debe ser DD/MM/YYYY o similar", None, False)
                            except Exception as e:
                                return MiddleResponse(f"Error al procesar [Fecha Inicio Periodo]: {str(e)}", None, False)

                # Formato de fecha: DD/MM/YYYY
                elif cell_value == "[Fecha Fin Periodo]":
                    valor = row_data[1] if len(row_data) > 1 else None
                    print(f"Encontrado [Fecha Fin Periodo], valor: '{valor}', tipo: {type(valor)}")
                    if valor is not None and valor != "":
                        if isinstance(valor, datetime):
                            print(f"Valor ya es datetime: {valor}")
                            fecha_fin_periodo = valor
                        else:
                            try:
                                # Si pandas ya lo convirtió a datetime
                                if pd.isna(valor):
                                    print(f"Valor es NaN, continuando")
                                    continue

                                if isinstance(valor, pd.Timestamp):
                                    print(f"Valor es Timestamp: {valor}")
                                    fecha_fin_periodo = valor.to_pydatetime()
                                    print(f"Convertido a datetime: {fecha_fin_periodo}")
                                else:
                                    print(f"Intentando convertir string a fecha: {valor}")
                                    # Intentar convertir el string a fecha
                                    # Limpiar el valor y manejar diferentes formatos de separadores
                                    valor_str = str(valor).strip()

                                    # Detectar el separador (puede ser / o -)
                                    if '/' in valor_str:
                                        fecha_parts = valor_str.split('/')
                                    elif '-' in valor_str:
                                        fecha_parts = valor_str.split('-')
                                    else:
                                        fecha_parts = []

                                    if len(fecha_parts) == 3:
                                        # Determinar el formato (DD/MM/YYYY o MM/DD/YYYY)
                                        # Asumimos que si el primer número es <= 12, podría ser mes o día
                                        # Si el segundo número es > 12, entonces el formato es DD/MM/YYYY
                                        try:
                                            num1, num2, num3 = int(fecha_parts[0]), int(fecha_parts[1]), int(fecha_parts[2])

                                            # Si el segundo número es > 12, asumimos formato MM/DD/YYYY
                                            if num2 > 12:
                                                month, day, year = num1, num2, num3
                                            # Si el primer número es > 12, asumimos formato DD/MM/YYYY
                                            elif num1 > 12:
                                                day, month, year = num1, num2, num3
                                            # Si ambos son <= 12, asumimos formato DD/MM/YYYY (estándar en muchos países)
                                            else:
                                                day, month, year = num1, num2, num3

                                            # Asegurar que el año tenga 4 dígitos
                                            if year < 100:
                                                year += 2000

                                            fecha_fin_periodo = datetime(year, month, day)
                                        except ValueError:
                                            return MiddleResponse(f"Fecha inválida en [Fecha Fin Periodo]: {valor_str}", None, False)
                                    else:
                                        return MiddleResponse(f"El formato de [Fecha Fin Periodo] debe ser DD/MM/YYYY o similar", None, False)
                            except Exception as e:
                                return MiddleResponse(f"Error al procesar [Fecha Fin Periodo]: {str(e)}", None, False)

            # Verificar que se hayan encontrado todas las etiquetas principales
            if tipo_estado_financiero is None:
                return MiddleResponse("No se encontró la etiqueta [Tipo Estado Financiero]", None, False)
            if tipo_periodo is None:
                return MiddleResponse("No se encontró la etiqueta [Tipo Periodo]", None, False)
            if fecha_inicio_periodo is None:
                return MiddleResponse("No se encontró la etiqueta [Fecha Inicio Periodo]", None, False)
            if fecha_fin_periodo is None:
                return MiddleResponse("No se encontró la etiqueta [Fecha Fin Periodo]", None, False)

            # 2. Procesar el resto del archivo
            row_idx = 0
            while row_idx < len(excel_data):
                if row_idx >= len(excel_data):
                    break

                row_data = excel_data[row_idx]
                cell_value = row_data[0] if len(row_data) > 0 else ""

                # Procesar agrupadores
                if cell_value == "[# Agrupador]":
                    print(f"Procesando agrupador en fila {row_idx}")
                    # Buscar el nombre del agrupador en la siguiente fila
                    row_idx += 1
                    if row_idx >= len(excel_data):
                        print("Fin del archivo después de [# Agrupador]")
                        break

                    row_data = excel_data[row_idx]
                    nombre_agrupador = row_data[0] if len(row_data) > 0 else None
                    print(f"Nombre del agrupador: '{nombre_agrupador}'")

                    if nombre_agrupador is None or nombre_agrupador == "":
                        print("Error: Falta el nombre del agrupador")
                        return MiddleResponse("Falta el nombre del agrupador después de la etiqueta [# Agrupador]", None, False)

                    agrupador_actual = nombre_agrupador
                    subagrupador_actual = None
                    print(f"Agrupador actual establecido a: '{agrupador_actual}'")

                    # Inicializar el agrupador en el diccionario
                    if agrupador_actual not in agrupadores:
                        agrupadores[agrupador_actual] = {
                            'subagrupadores': {},
                            'cuentas': [],
                            'total': None
                        }
                        print(f"Inicializado nuevo agrupador: '{agrupador_actual}'")
                    else:
                        print(f"Agrupador ya existente: '{agrupador_actual}'")

                # Procesar subagrupadores
                elif cell_value == "[## Subagrupador]":
                    print(f"Procesando subagrupador en fila {row_idx}")
                    if agrupador_actual is None:
                        print("Error: Subagrupador sin agrupador padre")
                        return MiddleResponse("Se encontró un subagrupador sin un agrupador padre", None, False)

                    # Buscar el nombre del subagrupador en la siguiente fila
                    row_idx += 1
                    if row_idx >= len(excel_data):
                        print("Fin del archivo después de [## Subagrupador]")
                        break

                    row_data = excel_data[row_idx]
                    nombre_subagrupador = row_data[0] if len(row_data) > 0 else None
                    print(f"Nombre del subagrupador: '{nombre_subagrupador}'")

                    if nombre_subagrupador is None or nombre_subagrupador == "":
                        print("Error: Falta el nombre del subagrupador")
                        return MiddleResponse("Falta el nombre del subagrupador después de la etiqueta [## Subagrupador]", None, False)

                    subagrupador_actual = nombre_subagrupador
                    print(f"Subagrupador actual establecido a: '{subagrupador_actual}' dentro del agrupador '{agrupador_actual}'")

                    # Inicializar el subagrupador en el diccionario
                    if subagrupador_actual not in agrupadores[agrupador_actual]['subagrupadores']:
                        agrupadores[agrupador_actual]['subagrupadores'][subagrupador_actual] = {
                            'cuentas': [],
                            'total': None
                        }
                        print(f"Inicializado nuevo subagrupador: '{subagrupador_actual}' en agrupador '{agrupador_actual}'")
                    else:
                        print(f"Subagrupador ya existente: '{subagrupador_actual}' en agrupador '{agrupador_actual}'")

                # Procesar cuentas
                elif cell_value == "[Nombre Cuenta]":
                    print(f"Procesando cuentas en fila {row_idx}")
                    if agrupador_actual is None:
                        print("Error: Cuenta sin agrupador padre")
                        return MiddleResponse("Se encontró una cuenta sin un agrupador padre", None, False)

                    # Verificar que la siguiente fila tenga las cabeceras [Codigo Cuenta] y [Valor Monetario]
                    print(f"Cabeceras de cuenta: '{row_data[1]}', '{row_data[2]}'")
                    if row_data[1] == "[Codigo Cuenta]" and row_data[2] == "[Valor Monetario]":
                        print("Cabeceras de cuenta encontradas correctamente")
                        # Avanzar a la siguiente fila después de las cabeceras
                        row_idx += 1

                        # Procesar todas las filas de cuentas hasta encontrar otra etiqueta
                        while row_idx < len(excel_data):
                            row_data = excel_data[row_idx]
                            nombre_cuenta = row_data[0] if len(row_data) > 0 else None
                            codigo_cuenta = row_data[1] if len(row_data) > 1 else None
                            valor_cuenta = row_data[2] if len(row_data) > 2 else None

                            # Si encontramos una etiqueta, salimos del bucle
                            if nombre_cuenta and isinstance(nombre_cuenta, str) and (nombre_cuenta.startswith('[') and nombre_cuenta.endswith(']')):
                                row_idx -= 1  # Retroceder para procesar esta etiqueta en la siguiente iteración
                                break

                            # Si tenemos datos válidos, procesamos la cuenta
                            print(f"Procesando fila de cuenta {row_idx}: '{nombre_cuenta}', '{codigo_cuenta}', '{valor_cuenta}'")
                            if nombre_cuenta and nombre_cuenta != "" and codigo_cuenta is not None and codigo_cuenta != "" and valor_cuenta is not None:
                                # Validar el código de cuenta (5 dígitos)
                                if not re.match(r'^\d{5}$', str(codigo_cuenta)):
                                    print(f"Error: Código de cuenta inválido: '{codigo_cuenta}'")
                                    return MiddleResponse(f"El código de cuenta '{codigo_cuenta}' debe tener 5 dígitos", None, False)

                                # Convertir el valor a decimal usando la función de conversión
                                try:
                                    valor_decimal = self._convertir_valor_monetario(valor_cuenta)
                                    print(f"Valor monetario convertido: {valor_decimal}")
                                except Exception as e:
                                    print(f"Error al convertir valor monetario: {str(e)}")
                                    return MiddleResponse(f"Error al convertir el valor '{valor_cuenta}' de la cuenta '{nombre_cuenta}': {str(e)}", None, False)

                                # Crear objeto de cuenta
                                cuenta = {
                                    'nombre': nombre_cuenta,
                                    'codigo': str(codigo_cuenta),
                                    'valor': valor_decimal,
                                    'agrupador': agrupador_actual,
                                    'subagrupador': subagrupador_actual
                                }

                                # Agregar la cuenta al diccionario correspondiente
                                if subagrupador_actual:
                                    agrupadores[agrupador_actual]['subagrupadores'][subagrupador_actual]['cuentas'].append(cuenta)
                                else:
                                    agrupadores[agrupador_actual]['cuentas'].append(cuenta)

                                # Agregar la cuenta a la lista general
                                cuentas.append(cuenta)

                            row_idx += 1
                            if row_idx >= len(excel_data):
                                break
                    else:
                        # Si no encontramos las cabeceras esperadas, avanzamos a la siguiente fila
                        row_idx += 1

                # Procesar totales de subagrupador
                elif cell_value == "[## Total]":
                    print(f"Procesando total de subagrupador en fila {row_idx}")
                    if agrupador_actual is None or subagrupador_actual is None:
                        print("Error: Total de subagrupador sin subagrupador padre")
                        return MiddleResponse("Se encontró un total de subagrupador sin un subagrupador padre", None, False)

                    valor_total = row_data[2] if len(row_data) > 2 else None
                    print(f"Valor total del subagrupador: '{valor_total}'")

                    # Si no hay valor o está vacío, calcularlo sumando las cuentas
                    if valor_total is None or valor_total == "":
                        # Verificar que haya cuentas para sumar
                        if agrupadores[agrupador_actual]['subagrupadores'][subagrupador_actual]['cuentas']:
                            cuentas_subagrupador = agrupadores[agrupador_actual]['subagrupadores'][subagrupador_actual]['cuentas']
                            print(f"Calculando total de {len(cuentas_subagrupador)} cuentas en el subagrupador")
                            valor_total = sum(cuenta['valor'] for cuenta in cuentas_subagrupador)
                            print(f"Total calculado: {valor_total}")
                        else:
                            print("No hay cuentas en el subagrupador, estableciendo total a 0")
                            valor_total = Decimal('0')
                    else:
                        try:
                            valor_total = self._convertir_valor_monetario(valor_total)
                            print(f"Total convertido: {valor_total}")
                        except Exception as e:
                            print(f"Error al convertir valor total: {str(e)}")
                            return MiddleResponse(f"Error al convertir el valor total '{valor_total}' del subagrupador '{subagrupador_actual}': {str(e)}", None, False)

                    agrupadores[agrupador_actual]['subagrupadores'][subagrupador_actual]['total'] = valor_total
                    print(f"Total del subagrupador '{subagrupador_actual}' establecido a {valor_total}")
                    subagrupador_actual = None  # Cerramos el subagrupador actual
                    print("Subagrupador actual cerrado")

                # Procesar totales de agrupador
                elif cell_value == "[# Total - Agrupador]":
                    print(f"Procesando total de agrupador en fila {row_idx}")
                    if agrupador_actual is None:
                        print("Error: Total de agrupador sin agrupador padre")
                        return MiddleResponse("Se encontró un total de agrupador sin un agrupador padre", None, False)

                    valor_total = row_data[2] if len(row_data) > 2 else None
                    print(f"Valor total del agrupador: '{valor_total}'")

                    # Si no hay valor o está vacío
                    if valor_total is None or valor_total == "":
                        if not agrupadores[agrupador_actual]['subagrupadores']:
                            # Si no hay subagrupadores, calculamos el total sumando las cuentas directas
                            if agrupadores[agrupador_actual]['cuentas']:
                                cuentas_agrupador = agrupadores[agrupador_actual]['cuentas']
                                print(f"Calculando total de {len(cuentas_agrupador)} cuentas directas en el agrupador")
                                valor_total = sum(cuenta['valor'] for cuenta in cuentas_agrupador)
                                print(f"Total calculado: {valor_total}")
                            else:
                                print("No hay cuentas directas en el agrupador, estableciendo total a 0")
                                valor_total = Decimal('0')
                        else:
                            # Si hay subagrupadores, calculamos el total sumando los totales de los subagrupadores
                            subagrupadores = agrupadores[agrupador_actual]['subagrupadores']
                            print(f"Calculando total de {len(subagrupadores)} subagrupadores")
                            valor_total = sum(
                                subagrupador_data['total']
                                for subagrupador_data in subagrupadores.values()
                                if subagrupador_data['total'] is not None
                            )
                            print(f"Total calculado de subagrupadores: {valor_total}")
                    else:
                        try:
                            valor_total = self._convertir_valor_monetario(valor_total)
                            print(f"Total convertido: {valor_total}")
                        except Exception as e:
                            print(f"Error al convertir valor total: {str(e)}")
                            return MiddleResponse(f"Error al convertir el valor total '{valor_total}' del agrupador '{agrupador_actual}': {str(e)}", None, False)

                    agrupadores[agrupador_actual]['total'] = valor_total
                    print(f"Total del agrupador '{agrupador_actual}' establecido a {valor_total}")
                    agrupador_actual = None  # Cerramos el agrupador actual
                    print("Agrupador actual cerrado")

                # Procesar Agrupadores especiales de Estado de Resultados
                # En la columna B puede haber estos codigos: ER-UB, ER-UO, ER-UAIR, ER-UN
                elif len(row_data) > 1 and row_data[1] in ["ER-UB", "ER-UO", "ER-UAIR", "ER-UN"]:
                    # Por ejemplo: Estas filas guardarlas en la tabla Agrupadores y TotalesAgrupadores
                    # [Nombre Cuenta]	                        [Codigo Cuenta]	[Valor Monetario]
                    # Utilidad Bruta	                        ER-UB	        3,912,210.00
                    # Utilidad Operativa                        ER-UO	        1,449,628.00
                    # Utilidad antes de impuesto a la renta	    ER-UAIR	        1,740,654.00
                    # Utilidad neta	                            ER-UN	        1,213,163.00

                    print(f"Procesando agrupador especial de Estado de Resultados en fila {row_idx}")
                    nombre_cuenta = row_data[0]
                    codigo_cuenta = row_data[1]  # Se va a guardar como str_nombre en la tabla Agrupador
                    valor_cuenta = row_data[2]   # Se va a guardar como db_resultadoAgrupador en la tabla TotalAgrupador

                    # Verificar que el valor no esté vacío sino calcularlo sumando todas las cuentas registradas hasta este momento
                    if valor_cuenta is None or valor_cuenta == "":
                        valor_cuenta = sum(cuenta['valor'] for cuenta in cuentas)
                        print(f"Total calculado: {valor_cuenta}")
                    else: # Intentar convertirlo a decimal si sale error, entonces calcularlo nosotros
                        try:
                            valor_cuenta = self._convertir_valor_monetario(valor_cuenta)
                            print(f"Valor convertido: {valor_cuenta}")
                        except Exception as e:
                            print(f"Error al convertir valor: {str(e)}")
                            valor_cuenta = sum(cuenta['valor'] for cuenta in cuentas)
                            print(f"Total calculado: {valor_cuenta}")

                    # Inicializar el agrupador en el diccionario
                    if codigo_cuenta not in agrupadores:
                        agrupadores[codigo_cuenta] = {
                            'subagrupadores': {},
                            'cuentas': [],
                            'total': valor_cuenta
                        }
                        print(f"Inicializado nuevo agrupador: '{codigo_cuenta}'")
                    else:
                        print(f"Agrupador ya existente: '{codigo_cuenta}'")

                    print(f"Total del agrupador '{codigo_cuenta}' establecido a {valor_cuenta}")
                    print("Agrupador actual cerrado")

                row_idx += 1

            # Imprimir resumen de los datos extraídos
            print("\n=== RESUMEN DE DATOS EXTRAÍDOS ===")
            print(f"Tipo Estado Financiero: {tipo_estado_financiero}")
            print(f"Tipo Periodo: {tipo_periodo}")
            print(f"Fecha Inicio Periodo: {fecha_inicio_periodo}")
            print(f"Fecha Fin Periodo: {fecha_fin_periodo}")
            print(f"Número de agrupadores: {len(agrupadores)}")

            for nombre_agrupador, datos_agrupador in agrupadores.items():
                print(f"\nAgrupador: {nombre_agrupador}")
                print(f"  Cuentas directas: {len(datos_agrupador['cuentas'])}")
                print(f"  Subagrupadores: {len(datos_agrupador['subagrupadores'])}")
                print(f"  Total: {datos_agrupador['total']}")

                for nombre_subagrupador, datos_subagrupador in datos_agrupador['subagrupadores'].items():
                    print(f"  Subagrupador: {nombre_subagrupador}")
                    print(f"    Cuentas: {len(datos_subagrupador['cuentas'])}")
                    print(f"    Total: {datos_subagrupador['total']}")

            print("=== FIN DEL RESUMEN ===\n")

            # 3. Guardar los datos en la base de datos
            # Crear el estado financiero
            ef_nombre = data.get('ef_nombre')
            if not ef_nombre:
                # Si no se proporciona un nombre, usar el tipo de estado financiero
                if tipo_estado_financiero == 1:
                    ef_nombre = "Estado de Situación Financiera"
                elif tipo_estado_financiero == 2:
                    ef_nombre = "Estado de Resultados"

            # Verificar si ya existe un estado financiero con el mismo tipo de EEFF, misma empresa, tipo de periodo y fecha de fin
            estado_financiero_existente = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero=tipo_estado_financiero,
                int_idEmpresa_id=data.get('int_idEmpresa'),
                int_tipoPeriodo=tipo_periodo,
                dt_fechaFinPeriodo=fecha_fin_periodo
            ).first()
            if estado_financiero_existente:
                return MiddleResponse(f"Ya existe un estado financiero para esta empresa, con el mismo tipo de periodo y fecha de fin", EstadoFinancieroSerializer(estado_financiero_existente).data, False)

            estado_financiero = EstadoFinanciero(
                str_nombre=ef_nombre,
                dt_fechaRegistro=datetime.now(),
                int_idEmpresa_id=data.get('int_idEmpresa'),
                int_idUsuarios_id=data.get('int_idUsuarios'),
                int_tipoRegistro=0,  # 0: periodo contable real, 1: simulación
                int_referenciaSimulacion=None,  # No es una simulación
                int_tipoPeriodo=tipo_periodo, # 1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario
                int_tipoEstadoFinanciero=tipo_estado_financiero, # 1: Estado de Situación Financiera, 2: Estado de Resultados
                dt_fechaInicioPeriodo=fecha_inicio_periodo,
                dt_fechaFinPeriodo=fecha_fin_periodo
            )
            estado_financiero.save()

            # Crear agrupadores, cuentas y totales
            for nombre_agrupador, datos_agrupador in agrupadores.items():
                # Procesar subagrupadores
                for nombre_subagrupador, datos_subagrupador in datos_agrupador['subagrupadores'].items():
                    # Crear o buscar el agrupador con subagrupador
                    agrupador, _ = Agrupador.objects.get_or_create(
                        str_nombre=nombre_agrupador.lower(),
                        str_nombre_subagrupador=nombre_subagrupador.lower()
                    )

                    # Crear las cuentas del subagrupador
                    for cuenta_data in datos_subagrupador['cuentas']:
                        cuenta, _ = Cuenta.objects.get_or_create(
                            str_nombre=cuenta_data['nombre'].lower(),
                            str_codigo=cuenta_data['codigo'],
                            int_idAgrupador=agrupador,
                            int_idEmpresa_id=data.get('int_idEmpresa')
                        )

                        # Crear el valor de la cuenta
                        EFCuenta.objects.create(
                            int_idEstadoFinanciero=estado_financiero,
                            db_valor=cuenta_data['valor'],
                            int_idCuenta=cuenta
                        )

                    # Crear el total del subagrupador
                    if datos_subagrupador['total'] is not None:
                        TotalAgrupador.objects.create(
                            int_idEstadoFinanciero=estado_financiero,
                            int_idAgrupador=agrupador,
                            db_resultadoAgrupador=datos_subagrupador['total']
                        )

                # Procesar cuentas directas del agrupador (sin subagrupador)
                if datos_agrupador['cuentas']:
                    # Crear o buscar el agrupador sin subagrupador
                    agrupador, _ = Agrupador.objects.get_or_create(
                        str_nombre=nombre_agrupador.lower(),
                        str_nombre_subagrupador=None
                    )

                    # Crear las cuentas del agrupador
                    for cuenta_data in datos_agrupador['cuentas']:
                        cuenta, _ = Cuenta.objects.get_or_create(
                            str_nombre=cuenta_data['nombre'].lower(),
                            str_codigo=cuenta_data['codigo'],
                            int_idAgrupador=agrupador,
                            int_idEmpresa_id=data.get('int_idEmpresa')
                        )

                        # Crear el valor de la cuenta
                        EFCuenta.objects.create(
                            int_idEstadoFinanciero=estado_financiero,
                            db_valor=cuenta_data['valor'],
                            int_idCuenta=cuenta
                        )

                    # Crear el total del agrupador
                    if datos_agrupador['total'] is not None:
                        TotalAgrupador.objects.create(
                            int_idEstadoFinanciero=estado_financiero,
                            int_idAgrupador=agrupador,
                            db_resultadoAgrupador=datos_agrupador['total']
                        )

                # Procesar agrupadores especiales de Estado de Resultados
                if tipo_estado_financiero == 2:
                    agrupadores_especiales_list = ['ER-UB', 'ER-UO', 'ER-UAIR', 'ER-UN']
                    if nombre_agrupador in agrupadores_especiales_list:
                        # Extraer los agrupadores especiales del diccionario
                        agrupadores_especiales = {}
                        agrupadores_especiales[nombre_agrupador] = agrupadores.get(nombre_agrupador, {}).get('total')
                        # agrupadores_especiales['ER-UB'] = agrupadores.get('ER-UB', {}).get('total')
                        # agrupadores_especiales['ER-UO'] = agrupadores.get('ER-UO', {}).get('total')
                        # agrupadores_especiales['ER-UAIR'] = agrupadores.get('ER-UAIR', {}).get('total')
                        # agrupadores_especiales['ER-UN'] = agrupadores.get('ER-UN', {}).get('total')

                        # Procesar los agrupadores especiales
                        for codigo_cuenta, valor_cuenta in agrupadores_especiales.items():
                            # Crear o buscar el agrupador especial
                            agrupador, _ = Agrupador.objects.get_or_create(
                                str_nombre=codigo_cuenta.upper(), # Guardarlo en mayúscula
                                str_nombre_subagrupador=None
                            )

                            # Crear el total del agrupador especial
                            if valor_cuenta is not None:
                                TotalAgrupador.objects.create(
                                    int_idEstadoFinanciero=estado_financiero,
                                    int_idAgrupador=agrupador,
                                    db_resultadoAgrupador=valor_cuenta
                                )


            # 4. Calcular ratios financieros
            resultado = self.calcular_ratios(estado_financiero.int_idEstadoFinanciero)
            print("Ratios calculados:", resultado.data)
            print(resultado.message)

            return MiddleResponse("Archivo procesado correctamente", {
                'int_idEstadoFinanciero': estado_financiero.int_idEstadoFinanciero,
                'str_nombre': estado_financiero.str_nombre,
                'ratios': resultado.data
            }, True)

        except Exception as e:
            return MiddleResponse(f"Error al procesar el archivo: {str(e)}", None, False)

    # Metodo Privado para crear registro de la definicion de Ratios si no hay en la tabla
    def _crear_definiciones_ratios(self, int_idEmpresa, str_idSuscripcion=None):
        """
        Crea las definiciones de ratios financieros si no existen en la base de datos.

        Args:
            int_idEmpresa: ID de la empresa a la que pertenecen los ratios
            str_idSuscripcion: ID de la suscripción (opcional)

        Returns:
            MiddleResponse: Objeto de respuesta con el resultado de la operación
        """
        from src.modules.ratios.models import Ratio

        # Definición de los ratios a crear
        ratios_definiciones = [
            # Ratios de Liquidez
            {
                'str_descripcion': 'Ratio de Liquidez',
                'str_formula': 'Activo Corriente/Pasivo Corriente',
            },
            {
                'str_descripcion': 'Prueba Ácida',
                'str_formula': '(Activo Corriente - Inventarios)/Pasivo Corriente',
            },
            {
                'str_descripcion': 'Capital de Trabajo',
                'str_formula': 'Activo Corriente - Pasivo Corriente',
            },
            # Ratios de Rentabilidad
            {
                'str_descripcion': 'EBITDA',
                'str_formula': 'Utilidad Operativa + Depreciación (y amortización de existir)',
            },
            {
                'str_descripcion': 'Margen Neto',
                'str_formula': 'Utilidad Neta/Ventas',
            },
            {
                'str_descripcion': 'Rotación',
                'str_formula': 'Ventas/Activo Total',
            },
            {
                'str_descripcion': 'Apalancamiento',
                'str_formula': 'Activo Total/Patrimonio',
            },
            {
                'str_descripcion': 'ROE',
                'str_formula': 'Utilidad Neta/Patrimonio',
            },
            {
                'str_descripcion': 'ROA',
                'str_formula': 'Utilidad Neta/Activo Total',
            },
        ]

        ratios_creados = 0

        try:
            # Verificar y crear cada ratio si no existe
            for ratio_def in ratios_definiciones:
                # Verificar si ya existe un ratio con esta descripción para esta empresa
                ratio_existente = Ratio.objects.filter(
                    str_descripcion=ratio_def['str_descripcion'],
                    int_idEmpresa_id=int_idEmpresa
                ).first()

                if not ratio_existente:
                    # Crear el ratio
                    Ratio.objects.create(
                        str_descripcion=ratio_def['str_descripcion'],
                        str_formula=ratio_def['str_formula'],
                        int_idEmpresa_id=int_idEmpresa,
                        str_idSuscripcion=str_idSuscripcion
                    )
                    ratios_creados += 1

            return MiddleResponse(f"Se crearon {ratios_creados} definiciones de ratios", None, True)
        except Exception as e:
            return MiddleResponse(f"Error al crear definiciones de ratios: {str(e)}", None, False)

    # Método para obtener el valor de una cuenta por nombre
    def _obtener_valor_cuenta(self, estado_financiero_id, nombre_cuenta):
        """
        Obtiene el valor de una cuenta o agrupador por su nombre para un estado financiero específico.
        Primero busca en la tabla de agrupadores, excepto para inventarios, depreciación y amortización
        que se buscan en la tabla de cuentas.

        Args:
            estado_financiero_id: ID del estado financiero
            nombre_cuenta: Nombre de la cuenta o agrupador a buscar

        Returns:
            Decimal: Valor de la cuenta/agrupador o 0 si no se encuentra
        """
        from src.modules.ef_cuentas.models import EFCuenta
        from src.modules.cuentas.models import Cuenta
        from src.modules.agrupadores.models import Agrupador
        from src.modules.total_agrupador.models import TotalAgrupador
        from src.modules.estado_financiero.models import EstadoFinanciero
        from decimal import Decimal

        try:
            print(f"Obteniendo valor de cuenta: {nombre_cuenta}")
            # Obtener la empresa del estado financiero
            empresa = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id).int_idEmpresa

            # Convertir el nombre a minúsculas para comparaciones case-insensitive
            nombre_cuenta_lower = nombre_cuenta.lower()

            # Lista de cuentas específicas que deben buscarse en la tabla de cuentas
            cuentas_especificas = ['inventarios', 'depreciación', 'amortización']

            # Lista de Agrupadores especiales
            agrupadores_especiales = ['ER-UB', 'ER-UO', 'ER-UAIR', 'ER-UN']

            # Si es una cuenta específica, buscar en la tabla de cuentas
            if nombre_cuenta_lower in cuentas_especificas:
                # Buscar la cuenta por nombre (case insensitive) y empresa
                cuenta = Cuenta.objects.filter(str_nombre__iexact=nombre_cuenta_lower, int_idEmpresa=empresa).first()

                if not cuenta:
                    return None

                # Buscar el valor de la cuenta para este estado financiero
                ef_cuenta = EFCuenta.objects.filter(
                    int_idEstadoFinanciero_id=estado_financiero_id,
                    int_idCuenta=cuenta
                ).first()

                if ef_cuenta:
                    return ef_cuenta.db_valor
                else:
                    return None
            elif nombre_cuenta_lower == 'activo total':
                # buscar subagrupadores activo corriente y activo no corriente
                activo_corriente = Agrupador.objects.filter(str_nombre_subagrupador__iexact='activo corriente').first()
                activo_no_corriente = Agrupador.objects.filter(str_nombre_subagrupador__iexact='activo no corriente').first()

                # Sumar los totales de los subagrupadores
                total_activo_corriente = TotalAgrupador.objects.filter(
                    int_idEstadoFinanciero_id=estado_financiero_id,
                    int_idAgrupador=activo_corriente
                ).first()

                total_activo_no_corriente = TotalAgrupador.objects.filter(
                    int_idEstadoFinanciero_id=estado_financiero_id,
                    int_idAgrupador=activo_no_corriente
                ).first()

                if total_activo_corriente and total_activo_no_corriente:
                    return total_activo_corriente.db_resultadoAgrupador + total_activo_no_corriente.db_resultadoAgrupador
                else:
                    return None
            elif nombre_cuenta_lower.upper() in agrupadores_especiales:
                # Buscar el total del agrupador especial para este estado financiero
                agrupador = Agrupador.objects.filter(str_nombre=nombre_cuenta_lower.upper()).first()

                if agrupador:
                    total_agrupador = TotalAgrupador.objects.filter(
                        int_idEstadoFinanciero_id=estado_financiero_id,
                        int_idAgrupador=agrupador
                    ).first()

                    if total_agrupador:
                        return total_agrupador.db_resultadoAgrupador
                    else:
                        return None
                else:
                    return None

            else:
                # Para el resto, buscar en la tabla de agrupadores
                # Primero intentar con el nombre exacto como agrupador principal
                agrupador = Agrupador.objects.filter(str_nombre__iexact=nombre_cuenta_lower).first()

                # Si no se encuentra, intentar como subagrupador
                if not agrupador:
                    # Buscar en cualquier agrupador que tenga este nombre como subagrupador
                    agrupador = Agrupador.objects.filter(str_nombre_subagrupador__iexact=nombre_cuenta_lower).first()

                if agrupador:
                    # Buscar el total del agrupador para este estado financiero
                    total_agrupador = TotalAgrupador.objects.filter(
                        int_idEstadoFinanciero_id=estado_financiero_id,
                        int_idAgrupador=agrupador
                    ).first()

                    if total_agrupador:
                        return total_agrupador.db_resultadoAgrupador
                    else:
                        return None
                else:
                    # Si no se encuentra como agrupador, intentar buscar en la tabla de cuentas como fallback
                    cuenta = Cuenta.objects.filter(str_nombre__iexact=nombre_cuenta_lower, int_idEmpresa=empresa).first()

                    if not cuenta:
                        return None

                    # Buscar el valor de la cuenta para este estado financiero
                    ef_cuenta = EFCuenta.objects.filter(
                        int_idEstadoFinanciero_id=estado_financiero_id,
                        int_idCuenta=cuenta
                    ).first()

                    if ef_cuenta:
                        return ef_cuenta.db_valor
                    else:
                        return None
        except Exception as e:
            print(f"Error al obtener valor de cuenta/agrupador {nombre_cuenta}: {str(e)}")
            return None

    # Método para guardar un ratio calculado
    def _guardar_ratio(self, estado_financiero_id, nombre_ratio, valor_ratio):
        """
        Guarda un ratio calculado en la base de datos con 4 decimales de precisión.

        Args:
            estado_financiero_id: ID del estado financiero
            nombre_ratio: Nombre del ratio
            valor_ratio: Valor calculado del ratio

        Returns:
            bool: True si se guardó correctamente, False en caso contrario
        """
        from src.modules.ratios.models import Ratio
        from src.modules.ratios_ef.models import RatioEF
        from src.modules.estado_financiero.models import EstadoFinanciero
        from decimal import Decimal, ROUND_HALF_UP

        try:
            # Redondear el valor del ratio a 4 decimales
            if valor_ratio is not None:
                valor_ratio = Decimal(str(valor_ratio)).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
            # Obtener el estado financiero
            estado_financiero = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id)

            # Buscar el ratio por nombre
            ratio = Ratio.objects.filter(
                str_descripcion=nombre_ratio,
                int_idEmpresa=estado_financiero.int_idEmpresa
            ).first()

            if not ratio:
                # Si no existe el ratio, crearlo primero
                self._crear_definiciones_ratios(estado_financiero.int_idEmpresa_id)
                ratio = Ratio.objects.filter(
                    str_descripcion=nombre_ratio,
                    int_idEmpresa=estado_financiero.int_idEmpresa
                ).first()

                if not ratio:
                    return False

            # Verificar si ya existe un registro para este ratio y estado financiero
            ratio_ef = RatioEF.objects.filter(
                int_idEstadoFinanciero=estado_financiero,
                int_idRatios=ratio
            ).first()

            if ratio_ef:
                # Actualizar el valor
                ratio_ef.db_valorRatio = valor_ratio
                ratio_ef.save()
            else:
                # Verificar si existe un registro con su estado financiero complementario
                estado_financiero_com = EstadoFinanciero.objects.filter(
                    int_tipoEstadoFinanciero= 1 if estado_financiero.int_tipoEstadoFinanciero == 2 else 2,
                    int_idEmpresa=estado_financiero.int_idEmpresa,
                    int_tipoPeriodo=estado_financiero.int_tipoPeriodo,
                    dt_fechaFinPeriodo=estado_financiero.dt_fechaFinPeriodo
                ).first()
                if estado_financiero_com:
                    ratio_ef = RatioEF.objects.filter(
                        int_idEstadoFinanciero=estado_financiero_com,
                        int_idRatios=ratio
                    ).first()
                    if ratio_ef:
                        # Actualizar el valor
                        ratio_ef.db_valorRatio = valor_ratio
                        ratio_ef.save()
                    else:
                        # Crear un nuevo registro
                        RatioEF.objects.create(
                            int_idEstadoFinanciero=estado_financiero_com,
                            int_idRatios=ratio,
                            db_valorRatio=valor_ratio
                        )
                else:
                    # Crear un nuevo registro
                    RatioEF.objects.create(
                        int_idEstadoFinanciero=estado_financiero,
                        int_idRatios=ratio,
                        db_valorRatio=valor_ratio
                    )

            return True
        except Exception as e:
            print(f"Error al guardar ratio {nombre_ratio}: {str(e)}")
            return False

    # Métodos para calcular ratios de Liquidez
    def _calcular_ratio_liquidez(self, estado_financiero_id):
        """
        Calcula el ratio de liquidez: Activo Corriente/Pasivo Corriente

        Args:
            estado_financiero_id: ID del estado financiero

        Returns:
            tuple: (Decimal valor calculado del ratio, bool éxito, str mensaje de error)
        """
        from decimal import Decimal
        print("Función calcular ratio de liquidez...")
        # Verificar si existen las variables necesarias
        estado_financiero_main = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id)
        activo_corriente = None
        pasivo_corriente = None
        # Estado de Situación Financiera
        if estado_financiero_main.int_tipoEstadoFinanciero == 1:
            activo_corriente = self._obtener_valor_cuenta(estado_financiero_id, 'activo corriente')
            pasivo_corriente = self._obtener_valor_cuenta(estado_financiero_id, 'pasivo corriente')
        # Si no existe, buscar al estado financiero complementario: Estado de Situación Financiera
        elif estado_financiero_main.int_tipoEstadoFinanciero == 2:
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 1,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                activo_corriente = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'activo corriente')
                pasivo_corriente = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'pasivo corriente')

        # Verificar si las variables son cero o no existen
        variables_faltantes = []
        if activo_corriente is None:
            variables_faltantes.append("Activo Corriente")
        if pasivo_corriente is None:
            variables_faltantes.append("Pasivo Corriente")
        print(f'Ratio de Liquidez: Activo Corriente: {activo_corriente}, Pasivo Corriente: {pasivo_corriente}')
        # Si falta alguna variable, no calcular el ratio
        if variables_faltantes:
            mensaje_error = f"No se encontraron las siguientes variables: {', '.join(variables_faltantes)}"
            return None, False, mensaje_error

        # Calcular el ratio
        try:
            ratio = (activo_corriente / pasivo_corriente) * 100

            # Guardar el ratio calculado
            self._guardar_ratio(estado_financiero_id, 'Ratio de Liquidez', ratio)

            return ratio, True, ""
        except Exception as e:
            return None, False, f"Error al calcular: {str(e)}"

    def _calcular_prueba_acida(self, estado_financiero_id):
        """
        Calcula la prueba ácida: (Activo Corriente - Inventarios)/Pasivo Corriente

        Args:
            estado_financiero_id: ID del estado financiero

        Returns:
            tuple: (Decimal valor calculado del ratio, bool éxito, str mensaje de error)
        """
        from decimal import Decimal

        # Verificar si existen las variables necesarias
        estado_financiero_main = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id)
        activo_corriente = None
        inventarios = None
        pasivo_corriente = None

        # Estado de Situación Financiera
        if estado_financiero_main.int_tipoEstadoFinanciero == 1:
            activo_corriente = self._obtener_valor_cuenta(estado_financiero_id, 'activo corriente')
            inventarios = self._obtener_valor_cuenta(estado_financiero_id, 'inventarios')
            pasivo_corriente = self._obtener_valor_cuenta(estado_financiero_id, 'pasivo corriente')
        # Si no existe, buscar al estado financiero complementario: Estado de Situación Financiera
        elif estado_financiero_main.int_tipoEstadoFinanciero == 2:
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 1,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                activo_corriente = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'activo corriente')
                inventarios = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'inventarios')
                pasivo_corriente = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'pasivo corriente')

        # Verificar si las variables son cero o no existen
        variables_faltantes = []
        if activo_corriente is None:
            variables_faltantes.append("Activo Corriente")
        if pasivo_corriente is None:
            variables_faltantes.append("Pasivo Corriente")
        if inventarios is None:
            variables_faltantes.append("Inventarios")

        # Si falta alguna variable, no calcular el ratio
        if variables_faltantes:
            mensaje_error = f"No se encontraron las siguientes variables: {', '.join(variables_faltantes)}"
            return None, False, mensaje_error

        # Calcular el ratio
        try:
            ratio = ((activo_corriente - inventarios) / pasivo_corriente) * 100

            # Guardar el ratio calculado
            self._guardar_ratio(estado_financiero_id, 'Prueba Ácida', ratio)

            return ratio, True, ""
        except Exception as e:
            return None, False, f"Error al calcular: {str(e)}"

    def _calcular_capital_trabajo(self, estado_financiero_id):
        """
        Calcula el capital de trabajo: Activo Corriente - Pasivo Corriente

        Args:
            estado_financiero_id: ID del estado financiero

        Returns:
            tuple: (Decimal valor calculado del ratio, bool éxito, str mensaje de error)
        """
        from decimal import Decimal

        # Verificar si existen las variables necesarias
        estado_financiero_main = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id)
        activo_corriente = None
        pasivo_corriente = None
        # Estado de Situación Financiera
        if estado_financiero_main.int_tipoEstadoFinanciero == 1:
            activo_corriente = self._obtener_valor_cuenta(estado_financiero_id, 'activo corriente')
            pasivo_corriente = self._obtener_valor_cuenta(estado_financiero_id, 'pasivo corriente')
        # Si no existe, buscar al estado financiero complementario: Estado de Situación Financiera
        elif estado_financiero_main.int_tipoEstadoFinanciero == 2:
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 1,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                activo_corriente = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'activo corriente')
                pasivo_corriente = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'pasivo corriente')

        # Verificar si las variables son cero o no existen
        variables_faltantes = []
        if activo_corriente is None:
            variables_faltantes.append("Activo Corriente")
        if pasivo_corriente is None:
            variables_faltantes.append("Pasivo Corriente")

        # Si falta alguna variable, no calcular el ratio
        if variables_faltantes:
            mensaje_error = f"No se encontraron las siguientes variables: {', '.join(variables_faltantes)}"
            return None, False, mensaje_error

        # Calcular el ratio
        try:
            ratio = activo_corriente - pasivo_corriente

            # Guardar el ratio calculado
            self._guardar_ratio(estado_financiero_id, 'Capital de Trabajo', ratio)

            return ratio, True, ""
        except Exception as e:
            return None, False, f"Error al calcular: {str(e)}"

    # Métodos para calcular ratios de Rentabilidad
    def _calcular_ebitda(self, estado_financiero_id):
        """
        Calcula el EBITDA: Utilidad Operativa + Depreciación (y amortización de existir)

        Args:
            estado_financiero_id: ID del estado financiero

        Returns:
            tuple: (Decimal valor calculado del ratio, bool éxito, str mensaje de error)
        """
        from decimal import Decimal

        # Verificar si existen las variables necesarias
        estado_financiero_main = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id)
        utilidad_operativa = None
        depreciacion = None
        amortizacion = None
        # Estado de Resultados
        if estado_financiero_main.int_tipoEstadoFinanciero == 2:
            utilidad_operativa = self._obtener_valor_cuenta(estado_financiero_id, 'ER-UO')
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 1,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                depreciacion = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'depreciación')
                amortizacion = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'amortización')
        # Si no existe, buscar al estado financiero complementario: Estado de Resultados
        elif estado_financiero_main.int_tipoEstadoFinanciero == 1:
            depreciacion = self._obtener_valor_cuenta(estado_financiero_id, 'depreciación')
            amortizacion = self._obtener_valor_cuenta(estado_financiero_id, 'amortización')
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 2,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                utilidad_operativa = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'ER-UO')

        # Verificar si las variables son cero o no existen
        variables_faltantes = []
        if utilidad_operativa is None:
            variables_faltantes.append("Utilidad Operativa")
        if depreciacion is None:
            variables_faltantes.append("Depreciación")
        # Amortización pueden ser cero, son válidos
        if amortizacion is None:
            amortizacion = Decimal(0)

        # Si falta la variable principal, no calcular el ratio
        if variables_faltantes:
            mensaje_error = f"No se encontraron las siguientes variables: {', '.join(variables_faltantes)}"
            return None, False, mensaje_error

        # Calcular el ratio
        try:
            ratio = utilidad_operativa + depreciacion + amortizacion

            # Guardar el ratio calculado
            self._guardar_ratio(estado_financiero_id, 'EBITDA', ratio)

            return ratio, True, ""
        except Exception as e:
            return None, False, f"Error al calcular: {str(e)}"

    def _calcular_margen_neto(self, estado_financiero_id):
        """
        Calcula el margen neto: Utilidad Neta/Ventas

        Args:
            estado_financiero_id: ID del estado financiero

        Returns:
            tuple: (Decimal valor calculado del ratio, bool éxito, str mensaje de error)
        """
        from decimal import Decimal

        # Verificar si existen las variables necesarias
        estado_financiero_main = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id)
        utilidad_neta = None
        ventas = None
        # Estado de Resultados
        if estado_financiero_main.int_tipoEstadoFinanciero == 2:
            utilidad_neta = self._obtener_valor_cuenta(estado_financiero_id, 'ER-UN')
            ventas = self._obtener_valor_cuenta(estado_financiero_id, 'ventas netas')
        # Si no existe, buscar al estado financiero complementario: Estado de Resultados
        elif estado_financiero_main.int_tipoEstadoFinanciero == 1:
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 2,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                utilidad_neta = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'ER-UN')
                ventas = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'ventas netas')

        # Verificar si las variables son cero o no existen
        variables_faltantes = []
        if utilidad_neta is None:
            variables_faltantes.append("Utilidad Neta")
        if ventas is None:
            variables_faltantes.append("Ventas")

        # Si falta alguna variable, no calcular el ratio
        if variables_faltantes:
            mensaje_error = f"No se encontraron las siguientes variables: {', '.join(variables_faltantes)}"
            return None, False, mensaje_error

        # Calcular el ratio
        try:
            ratio = (utilidad_neta / ventas) * 100

            # Guardar el ratio calculado
            self._guardar_ratio(estado_financiero_id, 'Margen Neto', ratio)

            return ratio, True, ""
        except Exception as e:
            return None, False, f"Error al calcular: {str(e)}"

    def _calcular_rotacion(self, estado_financiero_id):
        """
        Calcula la rotación: Ventas/Activo Total

        Args:
            estado_financiero_id: ID del estado financiero

        Returns:
            tuple: (Decimal valor calculado del ratio, bool éxito, str mensaje de error)
        """
        from decimal import Decimal

        # Verificar si existen las variables necesarias
        estado_financiero_main = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id)
        ventas = None
        activo_total = None
        # Estado de Resultados
        if estado_financiero_main.int_tipoEstadoFinanciero == 2:
            ventas = self._obtener_valor_cuenta(estado_financiero_id, 'ventas netas')
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 1,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                activo_total = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'activo total')
        # Si no existe, buscar al estado financiero complementario: Estado de Resultados
        elif estado_financiero_main.int_tipoEstadoFinanciero == 1:
            activo_total = self._obtener_valor_cuenta(estado_financiero_id, 'activo total')
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 2,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                ventas = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'ventas netas')

        # Verificar si las variables son cero o no existen
        variables_faltantes = []
        if ventas is None:
            variables_faltantes.append("Ventas")
        if activo_total is None:
            variables_faltantes.append("Activo Total")

        # Si falta alguna variable, no calcular el ratio
        if variables_faltantes:
            mensaje_error = f"No se encontraron las siguientes variables: {', '.join(variables_faltantes)}"
            return None, False, mensaje_error

        # Calcular el ratio
        try:
            ratio = (ventas / activo_total) * 100

            # Guardar el ratio calculado
            self._guardar_ratio(estado_financiero_id, 'Rotación', ratio)

            return ratio, True, ""
        except Exception as e:
            return None, False, f"Error al calcular: {str(e)}"

    def _calcular_apalancamiento(self, estado_financiero_id):
        """
        Calcula el apalancamiento: Activo Total/Patrimonio

        Args:
            estado_financiero_id: ID del estado financiero

        Returns:
            tuple: (Decimal valor calculado del ratio, bool éxito, str mensaje de error)
        """
        from decimal import Decimal

        # Verificar si existen las variables necesarias
        estado_financiero_main = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id)
        activo_total = None
        patrimonio = None
        # Estado de Situación Financiera
        if estado_financiero_main.int_tipoEstadoFinanciero == 1:
            activo_total = self._obtener_valor_cuenta(estado_financiero_id, 'activo total')
            patrimonio = self._obtener_valor_cuenta(estado_financiero_id, 'patrimonio')
        # Si no existe, buscar al estado financiero complementario: Estado de Situación Financiera
        elif estado_financiero_main.int_tipoEstadoFinanciero == 2:
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 1,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                activo_total = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'activo total')
                patrimonio = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'patrimonio')

        # Verificar si las variables son cero o no existen
        variables_faltantes = []
        if activo_total is None:
            variables_faltantes.append("Activo Total")
        if patrimonio is None:
            variables_faltantes.append("Patrimonio")

        # Si falta alguna variable, no calcular el ratio
        if variables_faltantes:
            mensaje_error = f"No se encontraron las siguientes variables: {', '.join(variables_faltantes)}"
            return None, False, mensaje_error

        # Calcular el ratio
        try:
            ratio = (activo_total / patrimonio) * 100

            # Guardar el ratio calculado
            self._guardar_ratio(estado_financiero_id, 'Apalancamiento', ratio)

            return ratio, True, ""
        except Exception as e:
            return None, False, f"Error al calcular: {str(e)}"

    def _calcular_roe(self, estado_financiero_id):
        """
        Calcula el ROE: Utilidad Neta/Patrimonio

        Args:
            estado_financiero_id: ID del estado financiero

        Returns:
            tuple: (Decimal valor calculado del ratio, bool éxito, str mensaje de error)
        """
        from decimal import Decimal

        # Verificar si existen las variables necesarias
        estado_financiero_main = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id)
        utilidad_neta = None
        patrimonio = None
        # Estado de Resultados
        if estado_financiero_main.int_tipoEstadoFinanciero == 2:
            utilidad_neta = self._obtener_valor_cuenta(estado_financiero_id, 'ER-UN')
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 1,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                patrimonio = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'patrimonio')
        # Si no existe, buscar al estado financiero complementario: Estado de Resultados
        elif estado_financiero_main.int_tipoEstadoFinanciero == 1:
            patrimonio = self._obtener_valor_cuenta(estado_financiero_id, 'patrimonio')
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 2,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                utilidad_neta = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'ER-UN')

        # Verificar si las variables son cero o no existen
        variables_faltantes = []
        if utilidad_neta is None:
            variables_faltantes.append("Utilidad Neta")
        if patrimonio is None:
            variables_faltantes.append("Patrimonio")

        # Si falta alguna variable, no calcular el ratio
        if variables_faltantes:
            mensaje_error = f"No se encontraron las siguientes variables: {', '.join(variables_faltantes)}"
            return None, False, mensaje_error

        # Calcular el ratio
        try:
            ratio = (utilidad_neta / patrimonio) * 100

            # Guardar el ratio calculado
            self._guardar_ratio(estado_financiero_id, 'ROE', ratio)

            return ratio, True, ""
        except Exception as e:
            return None, False, f"Error al calcular: {str(e)}"

    def _calcular_roa(self, estado_financiero_id):
        """
        Calcula el ROA: Utilidad Neta/Activo Total

        Args:
            estado_financiero_id: ID del estado financiero

        Returns:
            tuple: (Decimal valor calculado del ratio, bool éxito, str mensaje de error)
        """
        from decimal import Decimal

        # Verificar si existen las variables necesarias
        estado_financiero_main = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id)
        utilidad_neta = None
        activo_total = None
        # Estado de Resultados
        if estado_financiero_main.int_tipoEstadoFinanciero == 2:
            utilidad_neta = self._obtener_valor_cuenta(estado_financiero_id, 'ER-UN')
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 1,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                activo_total = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'activo total')
        # Si no existe, buscar al estado financiero complementario: Estado de Resultados
        elif estado_financiero_main.int_tipoEstadoFinanciero == 1:
            activo_total = self._obtener_valor_cuenta(estado_financiero_id, 'activo total')
            estado_financiero_com = EstadoFinanciero.objects.filter(
                int_tipoEstadoFinanciero= 2,
                int_idEmpresa=estado_financiero_main.int_idEmpresa,
                int_tipoPeriodo=estado_financiero_main.int_tipoPeriodo,
                dt_fechaFinPeriodo=estado_financiero_main.dt_fechaFinPeriodo
            ).first()
            if estado_financiero_com:
                utilidad_neta = self._obtener_valor_cuenta(estado_financiero_com.int_idEstadoFinanciero, 'ER-UN')

        # Verificar si las variables son cero o no existen
        variables_faltantes = []
        if utilidad_neta is None:
            variables_faltantes.append("Utilidad Neta")
        if activo_total is None:
            variables_faltantes.append("Activo Total")

        # Si falta alguna variable, no calcular el ratio
        if variables_faltantes:
            mensaje_error = f"No se encontraron las siguientes variables: {', '.join(variables_faltantes)}"
            return None, False, mensaje_error

        # Calcular el ratio
        try:
            ratio = (utilidad_neta / activo_total) * 100

            # Guardar el ratio calculado
            self._guardar_ratio(estado_financiero_id, 'ROA', ratio)

            return ratio, True, ""
        except Exception as e:
            return None, False, f"Error al calcular: {str(e)}"

    # Método público para calcular todos los ratios de un estado financiero
    def calcular_ratios(self, estado_financiero_id):
        """
        Calcula todos los ratios financieros para un estado financiero específico y su Estado financiero complemetario.
        Es decir, al pasarle el ID de un estado financiero, se busca otro estado financiero de la misma empresa, del mismo tipo,
        del mismo tipo de period y que las fechas de fin del periodo sean iguales.
        Algunos ratios requieren de dos estados financieros para calcularse, como: rotación, ROE y ROA.
        El Estado Financiero tipo 1 (Balance General) tiene: Activo Corriente, Pasivo Corriente, Activo Total, Patrimonio y otros
        El Estado Financiero tipo 2 (Estado de Resultados) tiene: Utilidad Neta, Ventas, Utilidad Operativa, Depreciación, Amortización
        Solo registra en la base de datos los ratios que se pudieron calcular correctamente.

        Args:
            estado_financiero_id: ID del estado financiero

        Returns:
            MiddleResponse: Objeto de respuesta con el resultado de la operación
        """
        try:
            # Obtener el estado financiero
            from src.modules.estado_financiero.models import EstadoFinanciero

            try:
                estado_financiero = EstadoFinanciero.objects.get(int_idEstadoFinanciero=estado_financiero_id)
            except EstadoFinanciero.DoesNotExist:
                return MiddleResponse("Estado financiero no encontrado", None, False)

            # Crear definiciones de ratios si no existen
            self._crear_definiciones_ratios(estado_financiero.int_idEmpresa_id)

            # Diccionarios para almacenar resultados y errores
            resultados = {
                'liquidez': {},
                'rentabilidad': {}
            }

            ratios_calculados = []
            ratios_no_calculados = []

            # Calcular ratios de liquidez
            print("Calculando ratio de liquidez...")
            ratio_liquidez, exito_ratio_liquidez, error_ratio_liquidez = self._calcular_ratio_liquidez(estado_financiero_id)
            if exito_ratio_liquidez:
                resultados['liquidez']['ratio_liquidez'] = float(ratio_liquidez)
                ratios_calculados.append("Ratio de Liquidez")
            else:
                ratios_no_calculados.append(f"Ratio de Liquidez: {error_ratio_liquidez}")
            print("Calculando prueba ácida...")
            prueba_acida, exito_prueba_acida, error_prueba_acida = self._calcular_prueba_acida(estado_financiero_id)
            if exito_prueba_acida:
                resultados['liquidez']['prueba_acida'] = float(prueba_acida)
                ratios_calculados.append("Prueba Ácida")
            else:
                ratios_no_calculados.append(f"Prueba Ácida: {error_prueba_acida}")
            print("Calculando capital de trabajo...")
            capital_trabajo, exito_capital_trabajo, error_capital_trabajo = self._calcular_capital_trabajo(estado_financiero_id)
            if exito_capital_trabajo:
                resultados['liquidez']['capital_trabajo'] = float(capital_trabajo)
                ratios_calculados.append("Capital de Trabajo")
            else:
                ratios_no_calculados.append(f"Capital de Trabajo: {error_capital_trabajo}")

            # Calcular ratios de rentabilidad
            ebitda, exito_ebitda, error_ebitda = self._calcular_ebitda(estado_financiero_id)
            if exito_ebitda:
                resultados['rentabilidad']['ebitda'] = float(ebitda)
                ratios_calculados.append("EBITDA")
            else:
                ratios_no_calculados.append(f"EBITDA: {error_ebitda}")

            margen_neto, exito_margen_neto, error_margen_neto = self._calcular_margen_neto(estado_financiero_id)
            if exito_margen_neto:
                resultados['rentabilidad']['margen_neto'] = float(margen_neto)
                ratios_calculados.append("Margen Neto")
            else:
                ratios_no_calculados.append(f"Margen Neto: {error_margen_neto}")

            rotacion, exito_rotacion, error_rotacion = self._calcular_rotacion(estado_financiero_id)
            if exito_rotacion:
                resultados['rentabilidad']['rotacion'] = float(rotacion)
                ratios_calculados.append("Rotación")
            else:
                ratios_no_calculados.append(f"Rotación: {error_rotacion}")

            apalancamiento, exito_apalancamiento, error_apalancamiento = self._calcular_apalancamiento(estado_financiero_id)
            if exito_apalancamiento:
                resultados['rentabilidad']['apalancamiento'] = float(apalancamiento)
                ratios_calculados.append("Apalancamiento")
            else:
                ratios_no_calculados.append(f"Apalancamiento: {error_apalancamiento}")

            roe, exito_roe, error_roe = self._calcular_roe(estado_financiero_id)
            if exito_roe:
                resultados['rentabilidad']['roe'] = float(roe)
                ratios_calculados.append("ROE")
            else:
                ratios_no_calculados.append(f"ROE: {error_roe}")

            roa, exito_roa, error_roa = self._calcular_roa(estado_financiero_id)
            if exito_roa:
                resultados['rentabilidad']['roa'] = float(roa)
                ratios_calculados.append("ROA")
            else:
                ratios_no_calculados.append(f"ROA: {error_roa}")

            # Preparar mensaje de respuesta
            mensaje = ""
            if ratios_calculados:
                mensaje += f"Se calcularon correctamente los siguientes ratios: {', '.join(ratios_calculados)}. "
            else:
                mensaje += "No se pudo calcular ningún ratio. "

            if ratios_no_calculados:
                mensaje += f"No se pudieron calcular los siguientes ratios: {'; '.join(ratios_no_calculados)}"

            # Incluir información de errores en la respuesta
            respuesta = {
                'resultados': resultados,
                'ratios_calculados': ratios_calculados,
                'ratios_no_calculados': ratios_no_calculados
            }

            return MiddleResponse(mensaje, respuesta, True)
        except Exception as e:
            return MiddleResponse(f"Error al calcular ratios: {str(e)}", None, False)