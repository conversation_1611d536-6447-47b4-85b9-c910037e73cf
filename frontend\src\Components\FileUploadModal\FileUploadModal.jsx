import React, { useState, useRef } from "react";
import Modal from "../Modal/Modal";
import IconoUpload from "../../assets/SVG/IconoUpload";
import IconoExcel from "../../assets/SVG/IconoExcel";
import FinancieraService from "../../Services/FinancieraService";
import Swal from "sweetalert2";

const FileUploadModal = ({ isOpen, onClose, onUploadSuccess }) => {
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState(null);
  const fileInputRef = useRef(null);

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    const excelFiles = files.filter(file =>
      file.type === "application/vnd.ms-excel" ||
      file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.name.endsWith('.xls') ||
      file.name.endsWith('.xlsx')
    );

    if (excelFiles.length === 0) {
      Swal.fire({
        title: 'Formato no válido',
        text: 'Solo se permiten archivos Excel (.xls, .xlsx)',
        icon: 'warning',
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#1F263E'
      });
      return;
    }

    handleFiles(excelFiles);
  };

  const handleFileInputChange = (e) => {
    const files = Array.from(e.target.files);
    const excelFiles = files.filter(file =>
      file.type === "application/vnd.ms-excel" ||
      file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.name.endsWith('.xls') ||
      file.name.endsWith('.xlsx')
    );

    if (excelFiles.length === 0) {
      Swal.fire({
        title: 'Formato no válido',
        text: 'Solo se permiten archivos Excel (.xls, .xlsx)',
        icon: 'warning',
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#1F263E'
      });
      return;
    }

    handleFiles(excelFiles);

    // Limpiar el input para permitir seleccionar los mismos archivos nuevamente
    e.target.value = '';
  };

  const handleFiles = (files) => {
    // Verificar que no se excedan los 10 archivos
    const totalFiles = uploadedFiles.length + files.length;
    if (totalFiles > 10) {
      Swal.fire({
        title: 'Límite de archivos excedido',
        text: `No se pueden subir más de 10 archivos. Actualmente tienes ${uploadedFiles.length} archivos. Puedes agregar ${10 - uploadedFiles.length} más.`,
        icon: 'warning',
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#1F263E'
      });
      return;
    }

    // Verificar que no haya archivos duplicados
    const existingFileNames = uploadedFiles.map(f => f.name);
    const duplicateFiles = files.filter(file => existingFileNames.includes(file.name));

    if (duplicateFiles.length > 0) {
      Swal.fire({
        title: 'Archivos duplicados',
        text: `Los siguientes archivos ya están en la lista: ${duplicateFiles.map(f => f.name).join(', ')}`,
        icon: 'info',
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#1F263E'
      });
      return;
    }

    const newFiles = files.map(file => ({
      name: file.name,
      size: formatFileSize(file.size),
      file: file, // Guardar el objeto File real
      status: 'ready' // ready, uploading, success, error
    }));

    setUploadedFiles([...uploadedFiles, ...newFiles]);
    setUploadError(null); // Limpiar errores previos
  };

  const formatFileSize = (bytes) => {
    if (bytes < 1024) return bytes + "B";
    else if (bytes < 1048576) return (bytes / 1024).toFixed(0) + "KB";
    else return (bytes / 1048576).toFixed(1) + "MB";
  };

  const removeFile = (fileName) => {
    setUploadedFiles(uploadedFiles.filter(file => file.name !== fileName));
  };

  const handleClickUpload = () => {
    fileInputRef.current.click();
  };

  const handleSave = async () => {
    if (uploadedFiles.length === 0) {
      Swal.fire({
        title: 'Sin archivos',
        text: 'No hay archivos para subir',
        icon: 'info',
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#1F263E'
      });
      return;
    }

    setIsUploading(true);
    setUploadError(null);

    try {
      // Obtener solo los objetos File reales
      const filesToUpload = uploadedFiles.map(fileData => fileData.file);

      const result = await FinancieraService.uploadEstadosFinancierosExcel(filesToUpload);

      if (result.successCount > 0) {
        // Si todos los archivos se subieron exitosamente
        if (result.failureCount === 0) {
          Swal.fire({
            title: '¡Éxito!',
            text: `Se subieron exitosamente ${result.successCount} archivo${result.successCount > 1 ? 's' : ''}.`,
            icon: 'success',
            confirmButtonText: 'Continuar',
            confirmButtonColor: '#31C969'
          }).then(() => {
            setUploadedFiles([]);
            // Llamar al callback para refrescar los datos
            if (onUploadSuccess) {
              onUploadSuccess();
            }
            onClose();
          });
        } else {
          // Algunos archivos fallaron
          const errorDetails = result.failed.map(f =>
            `<div style="margin-bottom: 15px; padding: 12px; background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%); border-left: 4px solid #e53e3e; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <svg style="width: 16px; height: 16px; margin-right: 8px; fill: #e53e3e;" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
                <strong style="color: #742a2a; font-size: 14px;">${f.fileName}</strong>
              </div>
              <div style="color: #553c3c; font-size: 13px; line-height: 1.4; padding-left: 24px;">
                ${f.error.replace(/\n/g, '<br>')}
              </div>
            </div>`
          ).join('');

          Swal.fire({
            title: '<span style="color: #f59e0b;">⚠️ Subida parcial</span>',
            html: `<div style="text-align: left; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
              <div style="background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%); padding: 16px; border-radius: 12px; margin-bottom: 20px; border: 1px solid #fde68a;">
                <div style="display: flex; align-items: center; margin-bottom: 12px;">
                  <div style="width: 32px; height: 32px; background: #fef3c7; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                    <svg style="width: 18px; height: 18px; fill: #d97706;" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                  <div>
                    <h3 style="margin: 0; color: #92400e; font-size: 16px; font-weight: 600;">Algunos archivos no se pudieron subir</h3>
                    <p style="margin: 4px 0 0 0; color: #78350f; font-size: 14px;">Revisa los detalles de los archivos que fallaron</p>
                  </div>
                </div>

                <div style="display: flex; gap: 20px; margin-top: 12px;">
                  <div style="display: flex; align-items: center; background: #dcfce7; padding: 8px 12px; border-radius: 8px; border: 1px solid #bbf7d0;">
                    <svg style="width: 16px; height: 16px; margin-right: 6px; fill: #16a34a;" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    <span style="color: #15803d; font-weight: 600; font-size: 14px;">Exitosos: ${result.successCount}</span>
                  </div>
                  <div style="display: flex; align-items: center; background: #fee2e2; padding: 8px 12px; border-radius: 8px; border: 1px solid #fecaca;">
                    <svg style="width: 16px; height: 16px; margin-right: 6px; fill: #dc2626;" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                    </svg>
                    <span style="color: #dc2626; font-weight: 600; font-size: 14px;">Fallidos: ${result.failureCount}</span>
                  </div>
                </div>
              </div>

              <div style="max-height: 300px; overflow-y: auto; padding-right: 8px;">
                <h4 style="color: #374151; font-size: 15px; font-weight: 600; margin: 0 0 16px 0; display: flex; align-items: center;">
                  <svg style="width: 16px; height: 16px; margin-right: 8px; fill: #6b7280;" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                  </svg>
                  Archivos que fallaron:
                </h4>
                ${errorDetails}
              </div>
            </div>`,
            icon: false,
            confirmButtonText: '✓ Entendido',
            confirmButtonColor: '#f59e0b',
            width: '650px',
            padding: '20px',
            customClass: {
              popup: 'swal-warning-popup',
              confirmButton: 'swal-warning-button'
            },
            didOpen: () => {
              // Agregar estilos CSS personalizados
              const style = document.createElement('style');
              style.textContent = `
                .swal-warning-popup {
                  border-radius: 16px !important;
                  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
                }
                .swal-warning-button {
                  border-radius: 8px !important;
                  font-weight: 600 !important;
                  padding: 12px 24px !important;
                  transition: all 0.2s ease !important;
                }
                .swal-warning-button:hover {
                  background-color: #d97706 !important;
                  transform: translateY(-1px) !important;
                  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4) !important;
                }
              `;
              document.head.appendChild(style);
            }
          });

          // Llamar al callback para refrescar los datos ya que al menos un archivo se subió exitosamente
          if (onUploadSuccess) {
            onUploadSuccess();
          }

          // Mantener solo los archivos que fallaron
          const failedFiles = uploadedFiles.filter(fileData =>
            result.failed.some(failed => failed.fileName === fileData.name)
          );
          setUploadedFiles(failedFiles);
        }
      } else {
        // Todos los archivos fallaron
        const errorDetails = result.failed.map(f =>
          `<div style="margin-bottom: 15px; padding: 12px; background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%); border-left: 4px solid #e53e3e; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
              <svg style="width: 16px; height: 16px; margin-right: 8px; fill: #e53e3e;" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
              </svg>
              <strong style="color: #742a2a; font-size: 14px;">${f.fileName}</strong>
            </div>
            <div style="color: #553c3c; font-size: 13px; line-height: 1.4; padding-left: 24px;">
              ${f.error.replace(/\n/g, '<br>')}
            </div>
          </div>`
        ).join('');

        Swal.fire({
          title: '<span style="color: #e53e3e;">❌ Error en la subida</span>',
          html: `<div style="text-align: left; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
            <div style="background: linear-gradient(135deg, #fff1f0 0%, #ffe4e1 100%); padding: 16px; border-radius: 12px; margin-bottom: 20px; border: 1px solid #fecaca;">
              <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <div style="width: 32px; height: 32px; background: #fee2e2; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">
                  <svg style="width: 18px; height: 18px; fill: #dc2626;" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div>
                  <h3 style="margin: 0; color: #991b1b; font-size: 16px; font-weight: 600;">No se pudieron subir los archivos</h3>
                  <p style="margin: 4px 0 0 0; color: #7f1d1d; font-size: 14px;">Revisa los detalles de cada error a continuación</p>
                </div>
              </div>
            </div>

            <div style="max-height: 300px; overflow-y: auto; padding-right: 8px;">
              <h4 style="color: #374151; font-size: 15px; font-weight: 600; margin: 0 0 16px 0; display: flex; align-items: center;">
                <svg style="width: 16px; height: 16px; margin-right: 8px; fill: #6b7280;" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
                Detalles de errores:
              </h4>
              ${errorDetails}
            </div>
          </div>`,
          icon: false,
          confirmButtonText: '✓ Entendido',
          confirmButtonColor: '#dc2626',
          width: '650px',
          padding: '20px',
          customClass: {
            popup: 'swal-error-popup',
            confirmButton: 'swal-error-button'
          },
          didOpen: () => {
            // Agregar estilos CSS personalizados
            const style = document.createElement('style');
            style.textContent = `
              .swal-error-popup {
                border-radius: 16px !important;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
              }
              .swal-error-button {
                border-radius: 8px !important;
                font-weight: 600 !important;
                padding: 12px 24px !important;
                transition: all 0.2s ease !important;
              }
              .swal-error-button:hover {
                background-color: #b91c1c !important;
                transform: translateY(-1px) !important;
                box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4) !important;
              }
            `;
            document.head.appendChild(style);
          }
        });
      }


    } catch (error) {
      console.error("Error uploading files:", error);
      setUploadError(error.message || "Error al subir los archivos");

      Swal.fire({
        title: 'Error al subir archivos',
        html: `<div style="text-align: left;">
          <p style="margin-bottom: 10px;">Ha ocurrido un error durante la subida de archivos:</p>
          <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; white-space: pre-line; max-height: 200px; overflow-y: auto; border-left: 4px solid #dc3545;">
            ${error.message}
          </div>
        </div>`,
        icon: 'error',
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#dc3545',
        width: '500px'
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} >
      <div className="p-6 flex flex-row gap-8" >
        {/* Sección izquierda - Cargador de archivos */}
        <div className="w-1/2 border-1 border-gray-200 p-5 rounded-lg">
          <div className="flex flex-col">
            <div className="flex items-center gap-2 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" />
              </svg>
              <span className="font-medium">Cargador de Archivos</span>
            </div>
            <span className="text-sm text-gray-500 mb-4">Arrastra y suelta archivos o haz clic para seleccionarlos</span>
          </div>

          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center h-48 cursor-pointer"
            onClick={handleClickUpload}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <div className="text-[#31C969] mb-4">
               <IconoUpload size={40} color={"#31C969"} />
            </div>
            <p className="text-sm text-gray-500 text-center">Haz clic para subir o arrastra y suelta</p>
            <input
              type="file"
              className="hidden"
              ref={fileInputRef}
              onChange={handleFileInputChange}
              multiple
              accept=".xls,.xlsx,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            />
          </div>
        </div>

        {/* Sección derecha - Archivos subidos */}
        <div className="w-1/2 border-1 border-gray-200 p-5 rounded-lg">
          <h3 className="font-medium mb-4">Subidos</h3>

          <div className="space-y-4">
            {uploadedFiles.map((file, index) => (
              <div key={index} className="p-3">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-green-500">
                      <IconoExcel size={20} color={"#31C969"} />
                    </span>
                    <span className="text-sm">{file.name}</span>
                  </div>
                  <button
                    className="text-gray-500 hover:text-gray-700"
                    onClick={() => removeFile(file.name)}
                    disabled={isUploading}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" />
                    </svg>
                  </button>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-gray-500">{file.size}</span>
                  <span className={`text-xs px-2 py-1 rounded ${
                    file.status === 'ready' ? 'bg-blue-100 text-blue-800' :
                    file.status === 'uploading' ? 'bg-yellow-100 text-yellow-800' :
                    file.status === 'success' ? 'bg-green-100 text-green-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {file.status === 'ready' ? 'Listo' :
                     file.status === 'uploading' ? 'Subiendo...' :
                     file.status === 'success' ? 'Exitoso' :
                     'Error'}
                  </span>
                </div>
              </div>
            ))}
            {uploadedFiles.length === 0 && (
              <div className="text-center text-gray-500 py-8">
                No hay archivos seleccionados
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Botones de acción */}
      <div className="flex justify-end gap-2 p-4 ">
        {uploadError && (
          <div className="flex-1 text-red-600 text-sm mr-4">
            {uploadError}
          </div>
        )}
        <button
          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          onClick={onClose}
          disabled={isUploading}
        >
          Cancelar
        </button>
        <button
          className={`px-4 py-2 rounded-lg transition-colors flex items-center gap-2 ${
            isUploading || uploadedFiles.length === 0
              ? 'bg-gray-400 text-white cursor-not-allowed'
              : 'bg-[#1F263E] text-white hover:bg-[#3A4562]'
          }`}
          onClick={handleSave}
          disabled={isUploading || uploadedFiles.length === 0}
        >
          {isUploading ? (
            <>
              <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Subiendo...
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              Guardar ({uploadedFiles.length})
            </>
          )}
        </button>
      </div>
    </Modal>
  );
};

export default FileUploadModal;
