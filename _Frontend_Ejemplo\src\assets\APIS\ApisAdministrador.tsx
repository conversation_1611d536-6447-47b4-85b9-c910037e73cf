const BASE_URL = import.meta.env.VITE_BASE_URL;
const BASE_URL8000 = import.meta.env.VITE_SEGURIDAD_URL;
const BASE_PROCESOS = import.meta.env.VITE_BASE_URL_PROCESOS;

type Suscripcion = string | number | undefined;
type IdUsuario = string | number;

type IdAplicacion = string | number;

const API_GESTOR = {
  ObtenerEmpresas: (
    idAplicacion: IdAplicacion,
    Suscriptor: Suscripcion
  ): string =>
    `${BASE_URL8000}empresas/asignadas/aplicacion/${idAplicacion}/suscriptor/${Suscriptor}/`,
  ObtenerUsuarios: (suscripcion: Suscripcion): string =>
    `${BASE_URL8000}usuarios/suscripcion/${suscripcion}/`,
  ObtenerUsuariosProyectus: (): string =>
    `${BASE_URL}usuarios/`,

  // Proyectos
  ProyectosByUsuario: (usuario_id: Suscripcion): string =>
    `${BASE_URL}proyectos/usuario/${usuario_id}/`,
  ProyectosFilterByUsuario: (): string =>
    `${BASE_URL}proyectos/filter/`,
  ProyectosBuscarByUsuario: (nombre_origen: Suscripcion): string =>
    `${BASE_URL}proyectos/buscar/${nombre_origen}/`,
  CrearProyecto: (): string =>
    `${BASE_URL}proyectos/`,
  ProyectoEstadisticas: (proyecto_id: Suscripcion): string =>
    `${BASE_URL}proyectos/estadisticas/${proyecto_id}/`,

  // Tareas
  TareasByProyecto: (proyecto_id: Suscripcion): string =>
    `${BASE_URL}tareas/proyecto/${proyecto_id}/`,
  TareasByUsuario: (usuario_id: Suscripcion): string =>
    `${BASE_URL}tareas/usuario/${usuario_id}/`,
  TareasFilter: (): string =>
    `${BASE_URL}tareas/filter/`,
  CrearTarea: (): string =>
    `${BASE_URL}tareas/`,
  ActualizarTarea: (tarea_id: Suscripcion): string =>
    `${BASE_URL}tareas/${tarea_id}/`,
  TerminarTarea: (tarea_id: Suscripcion): string =>
    `${BASE_URL}tareas/terminar/${tarea_id}/`,

  // Evidencias
  SubirEvidencia: (): string =>
    `${BASE_URL}evidencias/`,
  ObtenerEvidenciasByTarea: (tarea_id: Suscripcion): string =>
    `${BASE_URL}evidencias/tarea/${tarea_id}/`,
  DescargarEvidencia: (evidencia_id: Suscripcion): string =>
    `${BASE_URL}evidencias/download/${evidencia_id}/`,
};

export default API_GESTOR;
