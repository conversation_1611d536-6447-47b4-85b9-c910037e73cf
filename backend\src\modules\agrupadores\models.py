from django.db import models

class Agrupador(models.Model):
    class Meta:
        db_table = 'tm_agrupadores'
        managed = True
        verbose_name = 'agrupador'
        verbose_name_plural = 'agrupadores'

    int_idAgrupador = models.AutoField(primary_key=True)
    str_nombre = models.CharField(max_length=255) # Activo
    str_nombre_subagrupador = models.CharField(max_length=255, null=True) # Activo Corriente, Activo No Corriente

    def __str__(self):
        return self.str_nombre if self.str_nombre_subagrupador is None else f"{self.str_nombre} - {self.str_nombre_subagrupador}"