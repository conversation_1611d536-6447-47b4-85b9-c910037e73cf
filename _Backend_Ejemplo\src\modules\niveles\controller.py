from src.utils.classes import Response
from .models import Nivel


class NivelController:
    def __init__(self): ...

    def get_by_control_id(self, control_id: int):

        try:
            niveles = Nivel.objects.filter(int_idControl_id=control_id).all()
            if not niveles:
                return Response("No se encontró el nivel",state=False)
            return Response(data=niveles, state=True)

        except Exception as e:
            return Response(str(e), e)
