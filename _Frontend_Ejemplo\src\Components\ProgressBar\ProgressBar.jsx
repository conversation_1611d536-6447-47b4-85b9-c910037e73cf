import React from 'react';

const ProgressBar = ({ percentage, color }) => {
  const getBackgroundColor = () => {
    switch(color) {
      case 'red':
        return 'bg-[#FF1E1E14]';
      case 'yellow':
        return 'bg-[#FFFDCF]';
      case 'green':
        return 'bg-[#33FF0020]';
      default:
        return 'bg-gray-100';
    }
  };

  const getFillColor = () => {
    switch(color) {
      case 'red':
        return 'bg-[#FF1E1E]';
      case 'yellow':
        return 'bg-[#FFE065]';
      case 'green':
        return 'bg-[#47D691]';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="flex items-center  w-full">
      <div className={`
        w-[6.75rem] h-[1rem] rounded-full ${getBackgroundColor()}
      `}>
        <div 
          className={`
            h-full rounded-full ${getFillColor()}
          `}
          style={{ width: `${percentage}%` }}
        ></div>
      </div>
      <span className="text-sm text-[#272727] poppins-font min-w-[2.5rem] text-[0.875rem] text-right">
        {percentage}%
      </span>
    </div>
  );
};

export default ProgressBar
// const ProgressBars = () => {
//   return (
//     <div className="space-y-4 w-64">
//       <ProgressBar percentage={0} color="blue" />
//       <ProgressBar percentage={70} color="yellow" />
//       <ProgressBar percentage={70} color="yellow" />
//       <ProgressBar percentage={80} color="green" />
//     </div>
//   );
// };

// export default ProgressBars;