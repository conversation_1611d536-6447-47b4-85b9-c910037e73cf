from django.db import models

from src.modules.empresas.models import Empresa
from src.modules.frameworks.models import Framework
from src.modules.usuarios.models import Usuario


# Create your models here.
class Evaluacion(models.Model):

    class Meta:
        db_table = "tr_evaluaciones"
        managed = True
        verbose_name = "evaluacion"
        verbose_name_plural = "evaluaciones"

    int_idEvaluacion = models.AutoField(primary_key=True)
    str_nombre = models.CharField(max_length=255, blank=True)
    dt_fechaInicio = models.DateTimeField()
    dt_fechaFin = models.DateTimeField(null=True)
    str_resultado = models.TextField(null=True, blank=True)
    bool_estado = models.BooleanField(default=False)
    bool_tobe = models.BooleanField(null=True, default=False)
    bool_asis = models.BooleanField(null=True, default=False)
    str_descripcion = models.TextField()
    str_nota = models.CharField(max_length=3, blank=True, null=True)
    int_idUsuarios = models.ForeignKey(
        Usuario,
        on_delete=models.CASCADE,
        db_column="int_idUsuarios",
        related_name="evaluaciones_responsable",
    )
    int_idEmpresa = models.ForeignKey(
        Empresa,
        on_delete=models.CASCADE,
        db_column="int_idEmpresa",
        related_name="evaluaciones_empresa",
    )
    int_idFramework = models.ForeignKey(
        Framework,
        on_delete=models.CASCADE,
        db_column="int_idFramework",
        related_name="evaluaciones_framework",
    )
