import { Navigate, Route, Routes } from "react-router-dom";
import { RoutesPrivate } from "./ProtectedRoute";
import Inicio from "../Pages/Administrador/Inicio";
import Template from "../Components/Template/Template";
import DetalleEF from "../Pages/Administrador/DetalleEF/DetalleEF";
import Simulaciones from "../Pages/Administrador/Simulaciones/Simulaciones";
import Comparativa from "../Pages/Administrador/Comparativa/Comparativa";

const PrivateRoutes = ({ perfil }) => {
  return (
    <>
        <Template perfil={perfil}>
          <Routes>
            <>
              <Route path="/*" element={<Navigate to="/Financiera/Inicio" />} />
              <Route path={RoutesPrivate.INICIO} element={<Inicio />} />
              <Route path={RoutesPrivate.DETALLEEF} element={<DetalleEF />} />
              <Route path={RoutesPrivate.SIMULACIONES} element={<Simulaciones />} />
              <Route path={RoutesPrivate.COMPARATIVA} element={<Comparativa />} />

            </>
          </Routes>
        </Template>
  
    </>
  );
};

export { PrivateRoutes };
