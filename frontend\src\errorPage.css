.error-page-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 5rem); /* Resta la altura del header */
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e9f2 100%);
  padding: 2rem;
}

.error-content {
  max-width: 800px;
  width: 100%;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 10px 30px rgba(44, 76, 179, 0.15);
  padding: 3rem;
  text-align: center;
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.error-icon {
  margin-bottom: 1.5rem;
}

.error-title {
  font-size: 2.5rem;
  color: #2C4CB3;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.error-description {
  font-size: 1.2rem;
  color: #4B4B4B;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.error-details {
  background-color: #f8f9fa;
  border-radius: 0.8rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  text-align: left;
}

.error-details h3 {
  color: #2C4CB3;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.error-code {
  background-color: #fff;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
}

.error-code code {
  color: #e63946;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.btn-volver, .btn-reintentar {
  padding: 0.8rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-width: 180px;
}

.btn-volver {
  background-color: #2C4CB3;
  color: white;
  border: none;
}

.btn-volver:hover {
  background-color: #1a3a9f;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(44, 76, 179, 0.3);
}

.btn-reintentar {
  background-color: white;
  color: #2C4CB3;
  border: 2px solid #2C4CB3;
}

.btn-reintentar:hover {
  background-color: #f0f4ff;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(44, 76, 179, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .error-content {
    padding: 2rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-actions {
    flex-direction: column;
    gap: 1rem;
  }
  
  .btn-volver, .btn-reintentar {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .error-page-container {
    padding: 1rem;
  }
  
  .error-content {
    padding: 1.5rem;
  }
  
  .error-title {
    font-size: 1.8rem;
  }
  
  .error-description {
    font-size: 1rem;
  }
}