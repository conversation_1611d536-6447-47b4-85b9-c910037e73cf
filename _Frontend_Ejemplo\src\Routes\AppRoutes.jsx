import { FC, useEffect, useState } from "react"; 
import { Routes, Route, BrowserRouter, Navigate } from "react-router-dom";
import { PrivateRoutes } from "./PrivateRoutes";
import Cookies from "js-cookie";
import axios from "axios";
import { encrypt, decrypt, validateToken, logout, checkSesion } from "../Services/TokenService";
import API_GESTOR from "../assets/APIS/ApisAdministrador";
  

const AppRoutes = () => {
  const [verificado, setVerificado] = useState(false);
  const [rol, setRol] = useState("Administrador");
  const [ipAddress, setIpAddress] = useState("");

  const getCookieOrParam = (param, cookieName) => {
    const params = new URLSearchParams(window.location.search);
    const value =
      params.get(param) ||
      (Cookies.get(cookieName) ? decrypt(Cookies.get(cookieName)) : null);
    if (value) {
      Cookies.set(cookieName, encrypt(value));
    }
    return value;
  };
  const baseUrl =  import.meta.env.VITE_BASE_URLCONTRATOS;
  const baseSeguridad = import.meta.env.VITE_SEGURIDAD_URL;

  const idAplicacion = getCookieOrParam("idAplicacion", "idAplicacionProyectus");
  const suscriptor = getCookieOrParam("Suscriptor", "suscriptorProyectus");
  const correoUser = getCookieOrParam("correoUser", "correoUserProyectus");
  const sesion = getCookieOrParam("sesion", "sesionProyectus");
  const suscripcion = getCookieOrParam("Suscripcion", "suscripcionProyectus");
  // const app = getCookieOrParam("app", "appProyectus");
  // console.log("AppRoutes sesion:",sesion)
  useEffect(() => {
    const fetchIpAddress = async () => {
      try {
        const response = await fetch("https://api.ipify.org?format=json");
        const data = await response.json();
        setIpAddress(data.ip);
      } catch (error) {
        console.error("Error al obtener la IP:", error);
      }
    };
    fetchIpAddress();
  }, []);

  useEffect(() => {
    const VerificacionSeguridad = async () => {
      try {
        // if (localStorage.getItem("isVerifiedAlfaparf")) {
        //   await validateToken();
        //   await VerificarSesion()




        //   setVerificado(true);
        //   setRol(decrypt(Cookies.get("rolAlfaparf")) || "");
          
        // }
  
        const response = await axios.get(`${baseSeguridad}seguridad/consultar/sesion/${sesion}/`);
        // console.log("AppRoutes response:",response)
        if (response.status >= 200 && response.status < 300) {
          const responseSeguridad = await axios.get(`${baseSeguridad}seguridad/token/`, {
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${response.data.str_token}`
            }
          });
  
          if (responseSeguridad.status >= 200 && responseSeguridad.status < 300) {
            Cookies.set("Token", responseSeguridad.data.token);
            Cookies.set("refreshToken", responseSeguridad.data.refresh_token);
  
            await validateToken();
  
            try {
              const respuestaAppsAsignadas = await axios.get(
                `${baseSeguridad}asignacion_aplicacion/suscriptor/${suscriptor}/`, {
                  headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${responseSeguridad.data.token}`
                  }
                }
              );
  
              const dataRespuestaAppsAsignadas = respuestaAppsAsignadas.data;
              
              if (
                !dataRespuestaAppsAsignadas.some(
                  (app) => app.int_idAplicacion === Number(idAplicacion)
                )
              ) {
                return;
              }

  
              const { data: userData } = await axios.get(
                `${baseSeguridad}usuarios/correo/${correoUser}/`, {
                  headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${responseSeguridad.data.token}`
                  }
                }
              );
  
              if (!userData) {
                // window.location.replace("${paginaSeguridad}auth?app=prisma");
                return;
              }
              // console.log("AppRoutes userData:",userData)
              Cookies.set("nombresProyectus", encrypt(userData.str_Nombres));
              Cookies.set("apellidosProyectus", encrypt(userData.str_Apellidos));
              Cookies.set("correoProyectus", encrypt(userData.str_Correo));
              Cookies.set("hora_llegadaProyectus", encrypt(userData.int_idUsuarios.toString()) || "");
              localStorage.setItem("fotoPerfil", userData.str_RutaFoto || "");
              const { data: perfilData } = await axios.get(
                `${baseSeguridad}perfiles/asignado/aplicacion/${idAplicacion}/usuario/${userData.int_idUsuarios}/`, {
                  headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${responseSeguridad.data.token}`
                  }
                }
              );
              console.log("AppRoutes perfilData:",perfilData)

              if (perfilData) {
                setRol(perfilData.str_Nombre);
                setVerificado(true);
                Cookies.set("rol", encrypt(perfilData.str_Nombre));
                localStorage.setItem("isVerified", "true");
              } else {
                // window.location.replace("${paginaSeguridad}auth?app=prisma");
              }
            } catch (error) {
              if (error.response?.status === 401) {
                localStorage.removeItem("isVerified");
                VerificacionSeguridad();
              } else {
                console.error("Error al verificar aplicaciones:", error);
              }
            }
          }
        }

        // Obtener Empresa y guardar en cookie
        const responseEmpresa = await axios.get(API_GESTOR.ObtenerEmpresas(idAplicacion, suscriptor));
        // console.log("AppRoutes responseEmpresa:",responseEmpresa)
        Cookies.set("busProyectus", encrypt(responseEmpresa.data[0].int_idEmpresa.toString()));
        // console.log("AppRoutes responseEmpresa.data[0].int_idEmpresa:",responseEmpresa.data[0].int_idEmpresa)
      } catch (error) {
        console.error(error);

        // window.location.replace("${paginaSeguridad}auth?app=prisma");
      }
    };
  
    VerificacionSeguridad();
  }, [ipAddress]);
  
  const VerificarSesion = async () => {
    const response = await checkSesion(sesion);

    if (response.data.dt_FechaCierre != null) {
      Swal.fire({
        title: "Fin de sesión",
        text: "Su sesión se ha cerrado",
        icon: "info",
        allowOutsideClick: false,
        confirmButtonText: "OK",
      }).then(() => {
        logout();
      });
    }else{
      console.log("ok")
    }
  };
  useEffect(() => {
    const interval = setInterval(() => {
      VerificarSesion();
    }, 5000);

    return () => clearInterval(interval);
  }, []);
  
  return (
    <BrowserRouter>
      <Routes>
        {verificado && (
          <>
            <Route path="/*" element={<PrivateRoutes perfil={rol} />} />
            <Route index element={<Navigate to="/Proyectus/Inicio" />} />
          </>
        )}
      </Routes>
    </BrowserRouter>
  );
};

export { AppRoutes };