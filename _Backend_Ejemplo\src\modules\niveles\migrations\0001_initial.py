# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('controles', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Nivel',
            fields=[
                ('int_idNivel', models.AutoField(primary_key=True, serialize=False)),
                ('str_descripcion', models.TextField()),
                ('int_orden', models.SmallIntegerField()),
                ('int_idControl', models.ForeignKey(db_column='int_idControl', on_delete=django.db.models.deletion.CASCADE, related_name='niveles', to='controles.control')),
            ],
            options={
                'verbose_name': 'nivel',
                'verbose_name_plural': 'niveles',
                'db_table': 'tr_niveles',
                'managed': True,
            },
        ),
    ]
