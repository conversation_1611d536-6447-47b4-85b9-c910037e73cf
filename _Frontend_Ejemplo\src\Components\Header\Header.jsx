import React, { useEffect } from 'react'
import logoProyectus from '../../../public/proyectus_logo.png'
import logoTareus from '../../../public/tareus logo.svg'

import IconoAplicaciones from '../../assets/SVG/IconoAplicaciones.tsx'
import IconoNotification from '../../assets/SVG/IconoNotification.tsx'
import { RoutesPrivate } from '../../Routes/ProtectedRoute.tsx'
import { useGoTo } from '../../Services/Globales.jsx'
const Header = ({perfil}) => {
    const { goTo } = useGoTo();  
    const menuColor = perfil === "Usuario" ? "#47D691" : "#156CFF";
    const fotoLogo = perfil === "Usuario" ? logoTareus : logoProyectus;

    useEffect(() => {
        const favicon = document.querySelector("link[rel='icon']");
        if (favicon) {
            favicon.href = perfil === "Usuario" ? "/tareus_icono.svg" : "/proyectur_icono.svg";
        }
    }, [perfil]);

    return (
         <header className=' w-full  py-4  md:px-10 px-3  flex  justify-between  items-center  shadow-sm  position-sticky  top-0   z-50'>
           <img src={fotoLogo} alt='logo' className={`md:w-100    h-auto ${perfil === "Usuario" ? "max-w-[10rem]" : "max-w-[13rem]"}  max-w-[8rem]  p-2 cursor-pointer`}   onClick={() => goTo(RoutesPrivate.INICIO)}/>
           <div className=" flex  items-center  gap-2  justify-center">
            <IconoNotification size='2.5rem' color="#C2F5FF" colorNotification={menuColor}/>
            <IconoAplicaciones  size='2.5rem' color={menuColor} />
           </div>
         </header>
    )
}

export default Header
