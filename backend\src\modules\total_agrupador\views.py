from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from src.modules.total_agrupador.models import TotalAgrupador
from src.modules.total_agrupador.serializers import TotalAgrupadorSerializer
from src.modules.total_agrupador.controller import TotalAgrupadorController

class TotalAgrupadorView(viewsets.ModelViewSet):
    serializer_class = TotalAgrupadorSerializer
    queryset = TotalAgrupador.objects.all()
    permission_classes = [permissions.AllowAny]

    @swagger_auto_schema(
        method='get',
        operation_description="Obtiene los agrupadores para un estado financiero de simulación específico.",
        manual_parameters=[
            openapi.Parameter(
                name='id_estado_financiero',
                in_=openapi.IN_PATH,
                description='ID del estado financiero de simulación',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
        ],
        responses={
            200: "OK",
            400: "Bad Request",
            404: "Not Found"
        }
    )
    @action(detail=False, methods=['get'], url_path='simulacion/estado-financiero/(?P<id_estado_financiero>[^/.]+)')
    def agrupadores_simulacion(self, request, id_estado_financiero):
        """
        Obtiene los agrupadores para un estado financiero de simulación específico.

        Parámetros de consulta:
        - id_estado_financiero: ID del estado financiero de simulación
        """
        # Validar parámetros
        if not id_estado_financiero:
            return Response(
                {"message": "El parámetro 'id_estado_financiero' es requerido", "data": None, "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            id_estado_financiero = int(id_estado_financiero)
        except ValueError:
            return Response(
                {"message": "El parámetro 'id_estado_financiero' debe ser un número entero", "data": None, "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Llamar al controlador
        controller = TotalAgrupadorController()
        result = controller.obtener_agrupadores_simulacion(id_estado_financiero)

        # Devolver respuesta
        if result.state:
            return Response(
                {"message": result.message, "data": result.data, "state": True},
                status=status.HTTP_200_OK
            )
        else:
            # Verificar si un mensaje incluye "no es válido" o "No se encontró"
            if "no es válido" in result.message or "No se encontró" in result.message:
                return Response(
                    {"message": result.message, "data": None, "state": False},
                    status=status.HTTP_400_BAD_REQUEST
                )
            # Verificar si un mensaje incluye "No se encontraron"
            if "No se encontraron" in result.message:
                return Response(
                    {"message": result.message, "data": None, "state": False},
                    status=status.HTTP_404_NOT_FOUND
                )
            return Response(
                {"message": result.message, "data": None, "state": False},
                status=status.HTTP_404_NOT_FOUND
            )

    @swagger_auto_schema(
        method='get',
        operation_description="Obtiene los agrupadores totales de los últimos 12 periodos para una empresa específica.",
        manual_parameters=[
            openapi.Parameter(
                name='id_empresa',
                in_=openapi.IN_PATH,
                description='ID de la empresa',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='tipo_periodo',
                in_=openapi.IN_PATH,
                description='Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='fecha_fin',
                in_=openapi.IN_PATH,
                description='Fecha de fin del periodo en formato DD-MM-YYYY (ejemplo: 31-12-2023)',
                type=openapi.TYPE_STRING,
                required=True,
                format='date'
            ),
        ],
        responses={
            200: "OK",
            400: "Bad Request",
            404: "Not Found"
        }
    )
    @action(detail=False, methods=['get'], url_path='periodo-especifico/ultimos-periodos/empresa/(?P<id_empresa>[^/.]+)/tipo-periodo/(?P<tipo_periodo>[^/.]+)/fecha-fin/(?P<fecha_fin>[^/.]+)')
    def total_agrupador(self, request, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene los agrupadores totales de los últimos 12 periodos para una empresa específica.

        Parámetros de consulta:
        - id_empresa: ID de la empresa
        - tipo_periodo: ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
        - fecha_fin: Fecha de fin del periodo en formato DD-MM-YYYY (ejemplo: 31-12-2023)
        """

        # Validar parámetros
        if not tipo_periodo:
            return Response(
                {"message": "El parámetro 'tipo_periodo' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not id_empresa:
            return Response(
                {"message": "El parámetro 'id_empresa' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not fecha_fin:
            return Response(
                {"message": "El parámetro 'fecha_fin' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            id_empresa = int(id_empresa)
        except ValueError:
            return Response(
                {"message": "El parámetro 'id_empresa' debe ser un número entero", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            tipo_periodo = int(tipo_periodo)
        except ValueError:
            return Response(
                {"message": "El parámetro 'tipo_periodo' debe ser un número entero", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Llamar al controlador
        controller = TotalAgrupadorController()
        result = controller.obtener_total_agrupador(id_empresa, tipo_periodo, fecha_fin)

        # Devolver respuesta
        if result.state:
            return Response(
                {"message": result.message, "data": result.data, "state": True},
                status=status.HTTP_200_OK
            )
        else:
            # Verificar si un mensaje incluye "no es válido"
            if "no es válido" in result.message:
                return Response(
                    {"message": result.message, "state": False},
                    status=status.HTTP_400_BAD_REQUEST
                )
            # Verificar si un mensaje incluye "No se encontraron"
            if "No se encontraron" in result.message:
                return Response(
                    {"message": result.message, "state": False},
                    status=status.HTTP_404_NOT_FOUND
                )
            return Response(
                {"message": result.message, "state": False},
                status=status.HTTP_404_NOT_FOUND
            )

    @swagger_auto_schema(
        method='get',
        operation_description="Obtiene los agrupadores totales para un periodo específico de una empresa.",
        manual_parameters=[
            openapi.Parameter(
                name='id_empresa',
                in_=openapi.IN_PATH,
                description='ID de la empresa',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='tipo_periodo',
                in_=openapi.IN_PATH,
                description='Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='fecha_fin',
                in_=openapi.IN_PATH,
                description='Fecha de fin del periodo en formato DD-MM-YYYY (ejemplo: 31-12-2023)',
                type=openapi.TYPE_STRING,
                required=True,
                format='date'
            ),
        ],
        responses={
            200: "OK",
            400: "Bad Request",
            404: "Not Found"
        }
    )
    @action(detail=False, methods=['get'], url_path='periodo-especifico/empresa/(?P<id_empresa>[^/.]+)/tipo-periodo/(?P<tipo_periodo>[^/.]+)/fecha-fin/(?P<fecha_fin>[^/.]+)')
    def total_agrupador_periodo_especifico(self, request, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene los agrupadores totales para un periodo específico de una empresa.

        Parámetros de consulta:
        - id_empresa: ID de la empresa
        - tipo_periodo: ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
        - fecha_fin: Fecha de fin del periodo en formato DD-MM-YYYY (ejemplo: 31-12-2023)
        """

        # Validar parámetros
        if not tipo_periodo:
            return Response(
                {"message": "El parámetro 'tipo_periodo' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not id_empresa:
            return Response(
                {"message": "El parámetro 'id_empresa' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not fecha_fin:
            return Response(
                {"message": "El parámetro 'fecha_fin' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            id_empresa = int(id_empresa)
        except ValueError:
            return Response(
                {"message": "El parámetro 'id_empresa' debe ser un número entero", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            tipo_periodo = int(tipo_periodo)
        except ValueError:
            return Response(
                {"message": "El parámetro 'tipo_periodo' debe ser un número entero", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Llamar al controlador
        controller = TotalAgrupadorController()
        result = controller.obtener_total_agrupador_periodo_especifico(id_empresa, tipo_periodo, fecha_fin)

        # Devolver respuesta
        if result.state:
            return Response(
                {"message": result.message, "data": result.data, "state": True},
                status=status.HTTP_200_OK
            )
        else:
            # Verificar si un mensaje incluye "no es válido"
            if "no es válido" in result.message:
                return Response(
                    {"message": result.message, "state": False},
                    status=status.HTTP_400_BAD_REQUEST
                )
            # Verificar si un mensaje incluye "No se encontraron"
            if "No se encontraron" in result.message:
                return Response(
                    {"message": result.message, "state": False},
                    status=status.HTTP_404_NOT_FOUND
                )
            return Response(
                {"message": result.message, "state": False},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @swagger_auto_schema(
        method='get',
        operation_description="Obtiene los agrupadores por periodo mensual para una empresa específica, filtrando por un mes específico y mostrando los últimos 12 años.",
        manual_parameters=[
            openapi.Parameter(
                name='id_empresa',
                in_=openapi.IN_PATH,
                description='ID de la empresa',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='id_mes',
                in_=openapi.IN_PATH,
                description='Número del mes (1-12)',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
        ],
        responses={
            200: "OK",
            400: "Bad Request",
            404: "Not Found"
        }
    )
    @action(detail=False, methods=['get'], url_path='ultimos-periodos/empresa/(?P<id_empresa>[^/.]+)/periodo-mensual-anual/mes/(?P<id_mes>[^/.]+)')
    def agrupadores_por_mes(self, request, id_empresa, id_mes):
        """
        Obtiene los agrupadores por periodo mensual para una empresa específica,
        filtrando por un mes específico y mostrando los últimos 12 años.

        Parámetros de consulta:
        - id_empresa: ID de la empresa
        - id_mes: Número del mes (1-12)
        """

        # Validar parámetros
        if not id_empresa:
            return Response(
                {"message": "El parámetro 'id_empresa' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not id_mes:
            return Response(
                {"message": "El parámetro 'id_mes' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            id_empresa = int(id_empresa)
        except ValueError:
            return Response(
                {"message": "El parámetro 'id_empresa' debe ser un número entero", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            id_mes = int(id_mes)
        except ValueError:
            return Response(
                {"message": "El parámetro 'id_mes' debe ser un número entero", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Llamar al controlador
        controller = TotalAgrupadorController()
        result = controller.obtener_agrupadores_por_mes(id_empresa, id_mes)

        # Devolver respuesta
        if result.state:
            return Response(
                {"message": result.message, "data": result.data, "state": True},
                status=status.HTTP_200_OK
            )
        else:
            # Verificar si un mensaje incluye "no es válido"
            if "no es válido" in result.message:
                return Response(
                    {"message": result.message, "state": False},
                    status=status.HTTP_400_BAD_REQUEST
                )
            # Verificar si un mensaje incluye "No se encontraron"
            if "No se encontraron" in result.message:
                return Response(
                    {"message": result.message, "state": False},
                    status=status.HTTP_404_NOT_FOUND
                )
            return Response(
                {"message": result.message, "state": False},
                status=status.HTTP_404_NOT_FOUND
            )

    @swagger_auto_schema(
        method='get',
        operation_description="Obtiene los agrupadores por periodo para una empresa específica.",
        responses={
            200: "OK",
            400: "Bad Request",
            404: "Not Found"
        }
    )
    @action(detail=False, methods=['get'], url_path='ultimos-periodos/empresa/(?P<id_empresa>[^/.]+)/tipo-periodo/(?P<tipo_periodo>[^/.]+)')
    def agrupadores_por_periodo(self, request, id_empresa, tipo_periodo):
        """
        Obtiene los agrupadores por periodo para una empresa específica.

        Parámetros de consulta:
        - tipo_periodo: ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
        - id_empresa: ID de la empresa
        """

        # Validar parámetros
        if not tipo_periodo:
            return Response(
                {"message": "El parámetro 'tipo_periodo' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not id_empresa:
            return Response(
                {"message": "El parámetro 'id_empresa' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            id_empresa = int(id_empresa)
        except ValueError:
            return Response(
                {"message": "El parámetro 'id_empresa' debe ser un número entero", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )
        try:
            tipo_periodo = int(tipo_periodo)
        except ValueError:
            return Response(
                {"message": "El parámetro 'tipo_periodo' debe ser un número entero", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Llamar al controlador
        controller = TotalAgrupadorController()
        result = controller.obtener_agrupadores_por_periodo(tipo_periodo, id_empresa)

        # Devolver respuesta
        if result.state:
            return Response(
                {"message": result.message, "data": result.data, "state": True},
                status=status.HTTP_200_OK
            )
        else:
            # Verificar si un mensaje incluye "no es válido"
            if "no es válido" in result.message:
                return Response(
                    {"message": result.message, "state": False},
                    status=status.HTTP_400_BAD_REQUEST
                )
            # Verificar si un mensaje incluye "No se encontraron"
            if "No se encontraron" in result.message:
                return Response(
                    {"message": result.message, "state": False},
                    status=status.HTTP_404_NOT_FOUND
                )
            return Response(
                {"message": result.message, "state": False},
                status=status.HTTP_404_NOT_FOUND
            )