# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('suscripciones', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Empresa',
            fields=[
                ('int_idEmpresa', models.AutoField(primary_key=True, serialize=False)),
                ('str_NombreEmpresa', models.Char<PERSON>ield(max_length=255)),
                ('str_RazonSocial', models.Char<PERSON>ield(max_length=255)),
                ('str_Ruc', models.Char<PERSON>ield(max_length=25)),
                ('dt_FechaCreacion', models.DateTimeField()),
                ('dt_FechaModificacion', models.DateTimeField(null=True)),
                ('int_idUsuarioCreacion', models.IntegerField()),
                ('int_idUsuarioModificacion', models.Integer<PERSON>ield(null=True)),
                ('str_Pais', models.Char<PERSON>ield(max_length=100)),
                ('str_Moneda', models.CharField(max_length=50)),
                ('str_<PERSON>mboloMoneda', models.CharField(max_length=10)),
                ('str_idSuscripcion', models.ForeignKey(db_column='str_idSuscripcion', on_delete=django.db.models.deletion.CASCADE, related_name='empresas', to='suscripciones.suscripcion')),
            ],
            options={
                'verbose_name': 'empresa',
                'verbose_name_plural': 'empresas',
                'db_table': 'tc_empresas',
                'managed': True,
            },
        ),
    ]
