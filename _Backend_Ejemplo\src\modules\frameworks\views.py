import os
from typing import Any
from django.http import HttpResponse
from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from src.utils.classes import Response as APIResponse
from rest_framework.decorators import api_view
from drf_yasg.utils import swagger_auto_schema
from src.modules.frameworks.serializers import FrameworkPlantillaSerializer
from .models import Framework
from .serializers import FrameworkSerializer
from rest_framework.decorators import action
from django.db.models import Q
import requests
from .controller import FrameworkController
from src.utils.classes import Config

from drf_yasg import openapi


class FrameworkView(viewsets.ModelViewSet):
    queryset = Framework.objects.all()
    serializer_class = FrameworkSerializer
    permission_classes = [permissions.AllowAny]
    controller = FrameworkController()
    http_method_names = ["get", "post", "patch", "delete"]

    @swagger_auto_schema(
        operation_description="Endpoint para subir una plantilla de Framework",
        request_body=FrameworkPlantillaSerializer,
        responses={
            200: openapi.Response(
                description="Plantilla procesada exitosamente",
                schema=FrameworkPlantillaSerializer,
            ),
            400: openapi.Response(
                description="Error en el procesamiento de la plantilla"
            ),
        },
    )
    @action(methods=["post"], detail=False, url_path="upload/plantilla")
    def upload_plantilla(self, request, *args, **kwargs):
        try:
            if "archivo" not in request.FILES:
                return Response(
                    data="No se ha enviado el archivo plantilla",
                    status=status.HTTP_400_BAD_REQUEST,
                )
            plantilla = request.FILES["archivo"]
            response: APIResponse = self.controller.leer_plantilla(plantilla, request.data)
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(data=response.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.serializer_class(instance, data=request.data)
            serializer.is_valid(raise_exception=True)
            response: APIResponse = self.controller.tiene_evaluacion(
                instance.int_idFramework
            )
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )
            print(serializer.validated_data)
            serializer.save()
            return Response(data=serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Endpoint para obtener frameworks por suscripción",
        responses={200: FrameworkSerializer(many=True)},
    )
    @action(detail=False, methods=["get"], url_path="suscripcion/<str:suscripcion_id>")
    def frameworks_by_suscripcion(self, request, suscripcion_id, *args, **kwargs):
        try:
            response: APIResponse = self.controller.get_by_suscripcion(suscripcion_id)
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(
                data=FrameworkSerializer(response.data, many=True).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Endpoint para cambiar el estado (activo/inactivo) de un framework específico.",
        request_body=None,  # No se necesita cuerpo en la petición
        responses={
            200: openapi.Response("Estado cambiado con éxito."),
            400: openapi.Response("Error al cambiar el estado."),
            404: openapi.Response("Framework no encontrado."),
        },
    )
    @action(detail=False, methods=["patch"], url_path="<int:framework_id>/estado/update")
    def cambiar_estado_framework(self, request, framework_id):
        """
        Acción para cambiar el estado booleano (bool_estado) de un framework.
        """
        try:
            response: APIResponse = self.controller.cambiar_estado(framework_id=framework_id)
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )

            return Response(data=response.message, status=status.HTTP_200_OK)

        except ValueError:
            return Response(
                data="ID de Framework inválido.", status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                data=f"Error inesperado: {str(e)}",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
