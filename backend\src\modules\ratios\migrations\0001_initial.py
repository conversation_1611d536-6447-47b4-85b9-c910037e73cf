# Generated by Django 5.2.1 on 2025-05-14 12:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('empresas', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Ratio',
            fields=[
                ('int_idRatios', models.AutoField(primary_key=True, serialize=False)),
                ('str_descripcion', models.Char<PERSON>ield(max_length=255)),
                ('str_formula', models.Char<PERSON>ield(max_length=255)),
                ('str_idSuscripcion', models.Char<PERSON>ield(max_length=255, null=True)),
                ('int_idEmpresa', models.ForeignKey(db_column='int_idEmpresa', on_delete=django.db.models.deletion.CASCADE, related_name='ratios', to='empresas.empresa')),
            ],
            options={
                'verbose_name': 'ratio',
                'verbose_name_plural': 'ratios',
                'db_table': 'tr_ratios',
                'managed': True,
            },
        ),
    ]
