import functools
from django.http import JsonResponse
from src.modules.seguridad.controller import SeguridadController
from src.utils.classes import Response

def add_session_info(view_func):
    """
    Decorador que agrega la información de sesión al request sin restringir el acceso.
    Útil para filtrar datos según el rol del usuario dentro de la función.
    """
    @functools.wraps(view_func)
    def wrapper(self, request, *args, **kwargs):
        # Obtener token y sesión_id
        token = request.headers.get("Authorization")

        # Intentar obtener sesion_id de diferentes fuentes
        sesion_id = None

        # Intentar desde payload si existe
        if hasattr(request, 'payload') and request.payload:
            sesion_id = request.payload.get("sesion_id")

        # Si no se encontró, intentar desde data o query_params (como en require_session)
        if not sesion_id:
            sesion_id = request.data.get("sesion_id") if hasattr(request, 'data') else None

        if not sesion_id and hasattr(request, 'query_params'):
            sesion_id = request.query_params.get("sesion_id")

        # Si tenemos token y sesion_id, consultar la sesión
        if token and sesion_id:
            try:
                # Consultar sesión
                seguridad_controller = SeguridadController()
                sesion_response = seguridad_controller.consultar_sesion(token, sesion_id)
                if sesion_response.state:
                    # Agregar datos de sesión al request
                    request.session_data = sesion_response.data
                    request.user_id = sesion_response.data.get("user_id")
                    request.perfil = sesion_response.data.get("profile")
                    request.suscripcion = sesion_response.data.get("subscription")
            except Exception as e:
                # Loguear el error pero permitir que la vista continúe
                print(f"Error en add_session_info: {str(e)}")

        # Continuar con la vista (sin importar si hay sesión o no)
        return view_func(self, request, *args, **kwargs)

    return wrapper

def require_session(view_func):
    """
    Decorador que verifica la sesión del usuario antes de ejecutar la vista.
    Agrega los datos de la sesión al request para que estén disponibles en la vista.
    """
    @functools.wraps(view_func)
    def wrapper(self, request, *args, **kwargs):
        # Obtener token y sesión_id
        token = request.headers.get("Authorization")
        sesion_id = request.data.get("sesion_id") or request.query_params.get("sesion_id")

        if not token or not sesion_id:
            return JsonResponse({
                "message": "Token o sesión_id no proporcionados",
                "state": False
            }, status=401)

        # Consultar sesión
        seguridad_controller = SeguridadController()
        sesion_response = seguridad_controller.consultar_sesion(token, sesion_id)

        if not sesion_response.state:
            return JsonResponse({
                "message": "Error al consultar la sesión",
                "data": sesion_response.data,
                "state": False
            }, status=401)

        # Agregar datos de sesión al request
        request.session_data = sesion_response.data
        request.user_id = sesion_response.data.get("int_idUsuarios")
        request.perfil = sesion_response.data.get("profile")
        request.suscripcion = sesion_response.data.get("subscription")

        # Continuar con la vista
        return view_func(self, request, *args, **kwargs)

    return wrapper

def require_role(roles):
    """
    Decorador que verifica que el usuario tenga uno de los roles especificados.
    Debe usarse después del decorador require_session.

    Ejemplo: @require_role(['Administrador', 'Supervisor'])
    """
    def decorator(view_func):
        @functools.wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            if not hasattr(request, 'perfil'):
                return JsonResponse({
                    "message": "Datos de sesión no disponibles",
                    "state": False
                }, status=401)

            if request.perfil not in roles:
                return JsonResponse({
                    "message": f"Acceso denegado. Se requiere uno de estos roles: {', '.join(roles)}",
                    "state": False
                }, status=403)

            return view_func(self, request, *args, **kwargs)
        return wrapper
    return decorator
