from django.urls import path, include
from rest_framework.routers import DefaultRouter

from src.modules.detalles_evaluacion.views import DetallesView


router = DefaultRouter()
router.register("", DetallesView, basename="detalles")

urlpatterns = [
    path("", include(router.urls)),
    # path("estado/update/<int:id>/", DetallesView.as_view({"patch": "cambiar_estado_detalle"})),
    path("subdominio/<int:subdominio_id>/", DetallesView.as_view({"get": "detalle_by_subdominio"})),
]
