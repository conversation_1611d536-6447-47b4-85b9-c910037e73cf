from openpyxl import Workbook
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.workbook.defined_name import DefinedName
from openpyxl.styles import PatternFill, Border, Side, Alignment, Font
from django.db.models import Q
from django.http import FileResponse
import os
from datetime import datetime
from src.modules.detalles_evaluacion.models import DetalleEvaluacion

class PlantillaController:
    def __init__(self, title="Plantilla de Framework"):
        self.title=title
        self.wb = Workbook()
        self.ws = self.wb.active
        self.ws.title = title
        self.archivo = None
        
    def __get_docs(self, path):
        if not os.path.exists(path):
            None

        nombre_archivo = os.path.basename(path)

        archivo_response = FileResponse(open(path, "rb"), as_attachment=True)
        archivo_response["Content-Disposition"] = (
            f'attachment; filename="{nombre_archivo}"'
        )
        archivo_response["Content-Type"] = "application/octet-stream"

        return archivo_response
    
    def __ajustar_ancho_columnas(self):

        for columna in self.ws.columns:
            max_length = 0
            columna_letra = columna[0].column_letter
            for celda in columna:
                try:
                    if len(str(celda.value)) > max_length:
                        max_length = len(str(celda.value))
                except:
                    pass
            adjusted_width = max_length + 2
            self.ws.column_dimensions[columna_letra].width = adjusted_width
        
    def __ajustar_altura_filas(self):

        for fila in self.ws.iter_rows():
            max_height = 0
            for celda in fila:
                try:
                    if len(str(celda.value)) > max_height:
                        max_height = len(str(celda.value))
                except:
                    pass
            self.ws.row_dimensions[celda.row].height = max_height * 1.2

    def __agregar_borde(self):
        for fila in self.ws.iter_rows():
            border = Border(
                top=Side(style="thin", color="000000"),
                bottom=Side(style="thin", color="000000"),
                left=Side(style="thin", color="000000"),
                right=Side(style="thin", color="000000"),
            )
            for celda in fila:
                celda.border = border

    def __estilo_cabecera(self, celdas, color):
        """Aplica estilos a las celdas de cabecera."""
        # Aseguramos que el color tenga el formato correcto (FF + 6 caracteres hex)
        color = color.replace('#', '').upper()
        if len(color) == 6:
            color = f"FF{color}"  # Añadimos FF al inicio para opacidad completa
        
        cabecera_fill = PatternFill(
            start_color=color,
            end_color=color,
            fill_type="solid"
        )
        cabecera_font = Font(
            bold=True,
            color="FFFFFF",
            size=11
        )
        cabecera_align = Alignment(
            horizontal="center",
            vertical="center",
            wrap_text=True
        )

        for celda in celdas:
            celda.fill = cabecera_fill
            celda.font = cabecera_font
            celda.alignment = cabecera_align

    def __background_color(self, celdas, color):
        cabecera_fill = PatternFill(
            start_color=color, fill_type="solid"
        )  # Cambiado a start_color
        cabecera_align = Alignment(horizontal="center", vertical="center")
        for celda in celdas:
            celda.fill = cabecera_fill
            celda.alignment = cabecera_align

    def __centrar_celdas(self):
        for fila in self.ws.iter_rows():
            for celda in fila:
                celda.alignment = Alignment(horizontal="center", vertical="center")

    def __font_color(self, celdas, color):
        cabecera_font = Font(color=color)
        for celda in celdas:
            celda.font = cabecera_font
            
    def descargar_plantilla(self):
        try:
            self.ws["A1"] = "# Dominio"
            self.ws["B1"] = "Dominio"
            
            self.ws["D1"] = "#Dominio"
            self.ws["E1"] = "#Sub Dominio"
            self.ws["F1"] = "Sub Dominio"
            
            
            self.ws["H1"] = "#Sub Dominio"
            self.ws["I1"] = "#Control"
            self.ws["J1"] = "Control"
            self.ws["K1"] = "Nivel Industria"
            
            
            self.ws["L1"] = "#Control"
            self.ws["M1"] = "#Nivel"
            self.ws["N1"] = "Nivel"
            self.ws["O1"] = "Orden"
            self.ws["P1"] = "Valor"
            
            self.ws["Q1"] = "#Nivel"
            self.ws["R1"] = "#Paso"
            self.ws["S1"] = "Paso"
            

            self.__estilo_cabecera(
                [self.ws["A1"], self.ws["B1"]],
                "FF4472C4"  # Azul
            )
            self.__estilo_cabecera(
                [self.ws["D1"], self.ws["E1"], self.ws["F1"]],
                "FF70AD47"  # Verde
            )
            self.__estilo_cabecera(
                [self.ws["H1"], self.ws["I1"], self.ws["J1"], self.ws["K1"]],
                "FFED7D31"  # Naranja
            )
            self.__estilo_cabecera(
                [self.ws["L1"], self.ws["M1"], self.ws["N1"],self.ws["P1"], self.ws["O1"]],
                "FF5B9BD5"  # Azul claro
            )
            self.__estilo_cabecera(
                [ self.ws["Q1"], self.ws["R1"], self.ws["S1"]],
                "FFA5A5A5"  # Gris
            )

            self.__centrar_celdas()
            
            self.__ajustar_altura_filas()
            self.__ajustar_ancho_columnas()

            self.wb.save(f"{self.title}.xlsx")
            return self.__get_docs(f"{self.title}.xlsx")

        except Exception as e:
            print(f"Error al descargar la plantilla: {str(e)}")
            return e

    def descargar_reporte(self, evaluacion_id:int):
        try:
            self.ws["A1"] = "Dominio"
            self.ws["B1"] = "SubDominio"
            self.ws["C1"] = "Control"
            
            
            self.ws["D1"] = "To Be"
            self.ws["E1"] = "As Is"
            self.ws["F1"] = "Nivel Industria"
            
            detalles = DetalleEvaluacion.objects.filter(int_idEvaluacion_id = evaluacion_id).all()

            for ix, detalle in enumerate(detalles):
                indice = ix + 2
                self.ws[f"A{indice}"] = detalle.int_idControl.int_idSubDominio.int_idDominio.str_nombre
                self.ws[f"B{indice}"] = detalle.int_idControl.int_idSubDominio.str_nombre
                self.ws[f"C{indice}"] = detalle.int_idControl.str_descripcion
                self.ws[f"D{indice}"] = detalle.str_valor_tobe
                self.ws[f"E{indice}"] = detalle.str_valor_asis
                self.ws[f"F{indice}"] = detalle.int_idControl.str_valorIndustria

            

            self.__estilo_cabecera(
                [self.ws["A1"], self.ws["B1"], self.ws["C1"], self.ws["D1"], self.ws["E1"], self.ws["F1"]],
                "FF4472C4"  # Azul
            )
                # "FF70AD47"  # Verde
                # "FFED7D31"  # Naranja
                # "FF5B9BD5"  # Azul claro
                # "FFA5A5A5"  # Gris

            self.__centrar_celdas()
            
            # self.__ajustar_altura_filas()
            self.__ajustar_ancho_columnas()

            self.wb.save(f"{self.title}.xlsx")
            return self.__get_docs(f"{self.title}.xlsx")

        except Exception as e:
            print(f"Error al descargar la plantilla: {str(e)}")
            return e

    