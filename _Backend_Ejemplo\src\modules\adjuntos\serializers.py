from rest_framework import serializers
from .models import Adjunto
import os


class AdjuntoSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = Adjunto
        fields = "__all__"

class AdjuntoNombreSerializer(serializers.ModelSerializer):
    nombre_archivo = serializers.SerializerMethodField()

    class Meta:
        model = Adjunto
        fields = ['int_idAdjunto', 'nombre_archivo']

    def get_nombre_archivo(self, obj):
        if obj.str_ruta:
            return os.path.basename(obj.str_ruta)
        return None
