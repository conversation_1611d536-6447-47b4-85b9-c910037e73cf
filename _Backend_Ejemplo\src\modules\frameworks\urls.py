from django.urls import path, include
from rest_framework.routers import DefaultRouter

from src.modules.frameworks.views import FrameworkView


router = DefaultRouter()
router.register("", FrameworkView, basename="framework")

urlpatterns = [
    path("", include(router.urls)),
    path("<int:framework_id>/estado/update/", FrameworkView.as_view({"patch": "cambiar_estado_framework"})),
    path("upload/plantilla/", FrameworkView.as_view({"post": "upload_plantilla"})),
    path("suscripcion/<str:suscripcion_id>/", FrameworkView.as_view({"get": "frameworks_by_suscripcion"})),
]
