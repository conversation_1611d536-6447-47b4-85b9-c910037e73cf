from django.db.models.signals import post_save
from django.dispatch import receiver
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import json

from src.modules.evaluaciones.models import Evaluacion
from src.modules.evaluados.models import Evaluado
from src.modules.detalles_evaluacion.models import DetalleEvaluacion

channel_layer = get_channel_layer()

@receiver(post_save, sender=Eva<PERSON>acion)
def evaluacion_saved(sender, instance, created, **kwargs):
    """
    Signal handler for when an Evaluacion is saved.
    Broadcasts the update to all clients connected to this evaluacion's group.
    """
    # Prepare basic data to send
    data = {
        'id': instance.int_idEvaluacion,
        'nombre': instance.str_nombre,
        'descripcion': instance.str_descripcion,
        'fecha_inicio': instance.dt_fechaInicio.isoformat() if instance.dt_fechaInicio else None,
        'fecha_fin': instance.dt_fechaFin.isoformat() if instance.dt_fechaFin else None,
        'estado': instance.bool_estado,
        'tobe_completado': instance.bool_tobe,
        'asis_completado': instance.bool_asis,
        'updated_field': kwargs.get('update_fields', [])
    }

    # Send to the evaluacion group
    async_to_sync(channel_layer.group_send)(
        f'evaluacion_{instance.int_idEvaluacion}',
        {
            'type': 'evaluacion_update',
            'data': data
        }
    )

@receiver(post_save, sender=Evaluado)
def evaluado_saved(sender, instance, created, **kwargs):
    """
    Signal handler for when an Evaluado is saved.
    Broadcasts the update to all clients connected to this evaluado's group.
    """
    # Calculate progress for this evaluado
    progress = calculate_evaluado_progress(instance)

    # Prepare basic data to send
    data = {
        'id': instance.int_idEvaluado,
        'tipo': instance.int_idTipoEvaluado,
        'estado': instance.bool_estado,
        'avance': progress,
        'evaluacion_id': instance.int_idEvaluacion.int_idEvaluacion,
        'usuario_id': instance.int_idUsuarios.int_idUsuarios if instance.int_idUsuarios else None,
        'updated_field': kwargs.get('update_fields', [])
    }

    # Send to the evaluado group
    async_to_sync(channel_layer.group_send)(
        f'evaluado_{instance.int_idEvaluado}',
        {
            'type': 'evaluado_update',
            'data': data
        }
    )

    # Also send to the parent evaluacion group
    async_to_sync(channel_layer.group_send)(
        f'evaluacion_{instance.int_idEvaluacion.int_idEvaluacion}',
        {
            'type': 'evaluacion_update',
            'data': {
                'evaluado_updated': data
            }
        }
    )

@receiver(post_save, sender=DetalleEvaluacion)
def detalle_evaluacion_saved(sender, instance, created, **kwargs):
    """
    Signal handler for when a DetalleEvaluacion is saved.
    Broadcasts the update to the related evaluado and evaluacion groups.
    """
    if not instance.int_idEvaluado:
        return  # Skip if no evaluado is associated

    # Prepare data about the updated detail
    detalle_data = {
        'id': instance.int_idDetalleEvaluacion,
        'control_id': instance.int_idControl.int_idControl,
        'control_descripcion': instance.int_idControl.str_descripcion,
        'valor_tobe': instance.str_valor_tobe,
        'valor_asis': instance.str_valor_asis,
        'evaluado_id': instance.int_idEvaluado.int_idEvaluado,
        'evaluacion_id': instance.int_idEvaluacion.int_idEvaluacion,
        'updated_field': kwargs.get('update_fields', [])
    }

    # Recalculate progress for the evaluado
    evaluado = instance.int_idEvaluado
    progress = calculate_evaluado_progress(evaluado)

    # Send to the evaluado group
    async_to_sync(channel_layer.group_send)(
        f'evaluado_{evaluado.int_idEvaluado}',
        {
            'type': 'evaluado_update',
            'data': {
                'detalle_updated': detalle_data,
                'avance': progress
            }
        }
    )

    # Also send to the parent evaluacion group
    async_to_sync(channel_layer.group_send)(
        f'evaluacion_{instance.int_idEvaluacion.int_idEvaluacion}',
        {
            'type': 'evaluacion_update',
            'data': {
                'detalle_updated': detalle_data,
                'evaluado_id': evaluado.int_idEvaluado,
                'avance': progress,
                'evaluado_updated': {
                    'id': evaluado.int_idEvaluado,
                    'avance': progress,
                    'estado': evaluado.bool_estado
                }
            }
        }
    )

def calculate_evaluado_progress(evaluado):
    """Calculate progress percentage for an evaluado"""
    try:
        # Get all detalles for this evaluado
        detalles = DetalleEvaluacion.objects.filter(int_idEvaluado=evaluado.int_idEvaluado)

        if not detalles.exists():
            return 0

        total_detalles = detalles.count()

        # For ToBe evaluados, check str_valor_tobe
        if evaluado.int_idTipoEvaluado == 1:
            completed = detalles.exclude(str_valor_tobe__isnull=True).exclude(str_valor_tobe='').count()
        # For AsIs evaluados, check str_valor_asis
        else:
            completed = detalles.exclude(str_valor_asis__isnull=True).exclude(str_valor_asis='').count()

        if total_detalles > 0:
            return int((completed / total_detalles) * 100)
        return 0

    except Exception:
        return 0
