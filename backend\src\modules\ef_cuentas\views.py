from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from src.modules.ef_cuentas.models import EFCuenta
from src.modules.ef_cuentas.serializers import EFCuentaSerializer
from src.modules.ef_cuentas.controller import EFCuentasController

class EFCuentaView(viewsets.ModelViewSet):
    serializer_class = EFCuentaSerializer
    queryset = EFCuenta.objects.all()
    permission_classes = [permissions.AllowAny]
    controller = EFCuentasController()

    @swagger_auto_schema(
        operation_description="Obtiene todas las cuentas de los estados financieros para una empresa, tipo de periodo y fecha fin específicos. La fecha debe estar en formato DD-MM-YYYY",
        manual_parameters=[
            openapi.Parameter(
                name='id_empresa',
                in_=openapi.IN_PATH,
                description='ID de la empresa',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='tipo_periodo',
                in_=openapi.IN_PATH,
                description='Tipo de periodo (1: anual, 2: trimestral, 3: mensual, etc.)',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='fecha_fin',
                in_=openapi.IN_PATH,
                description='Fecha de fin del periodo en formato DD-MM-YYYY (ejemplo: 31-12-2023)',
                type=openapi.TYPE_STRING,
                required=True,
                format='date'
            ),
        ],
        responses={
            200: "OK",
            400: "Bad Request",
            404: "Not Found"
        }
    )
    @action(detail=False, methods=["get"], url_path="empresa/(?P<id_empresa>[^/.]+)/tipo-periodo/(?P<tipo_periodo>[^/.]+)/fecha-fin/(?P<fecha_fin>[^/.]+)")
    def cuentas_por_empresa_periodo(self, request, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene todas las cuentas de los estados financieros para una empresa, tipo de periodo y fecha fin específicos.
        La fecha debe estar en formato DD-MM-YYYY (ejemplo: 31-12-2023).
        """
        try:
            # Validar que los parámetros sean válidos
            try:
                id_empresa = int(id_empresa)
                tipo_periodo = int(tipo_periodo) # 1: anual, 2: trimestral, 3: mensual, etc.
            except ValueError:
                return Response(
                    data={"message": "Los parámetros id_empresa y tipo_periodo deben ser números enteros"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Llamar al método del controlador
            response = self.controller.obtener_cuentas_por_empresa_periodo(
                id_empresa=id_empresa,
                tipo_periodo=tipo_periodo,
                fecha_fin=fecha_fin
            )

            if not response.state:
                return Response(
                    data={"message": response.message},
                    status=status.HTTP_404_NOT_FOUND
                )

            return Response(
                data=response.data,
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                data={"message": f"Error al procesar la solicitud: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )