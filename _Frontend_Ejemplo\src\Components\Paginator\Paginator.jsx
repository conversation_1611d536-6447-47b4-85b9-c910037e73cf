import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import React from "react";

const CustomPaginator = ({ totalPages, currentPage, onPageChange }) => {
  const renderPageNumbers = () => {
    const pageNumbers = [];

    // Always show first page
    if (currentPage > 2) {
      pageNumbers.push(
        <button
          key="first"
          onClick={() => onPageChange(1)}
          className="px-3 py-1 border  text-[#C1C0C0] border-[#C1C0C0] hover:bg-gray-100"
        >
          1
        </button>
      );

      if (currentPage > 3) {
        pageNumbers.push(
          <span key="first-ellipsis" className="px-2 py-1 text-[#C1C0C0]">
            ...
          </span>
        );
      }
    }

    // Determine which page numbers to show
    const startPage = Math.max(1, currentPage - 1);
    const endPage = Math.min(totalPages, currentPage + 1);

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(
        <button
          key={i}
          onClick={() => onPageChange(i)}
          className={`px-3 py-1 border  ${
            currentPage === i
              ? "bg-black text-white border-black"
              : "text-[#C1C0C0] hover:bg-gray-100 border-[#C1C0C0]"
          }`}
        >
          {i}
        </button>
      );
    }

    // Always show last page
    if (currentPage < totalPages - 1) {
      if (currentPage < totalPages - 2) {
        pageNumbers.push(
          <span key="last-ellipsis" className="px-1 py-1 text-[#C1C0C0]">
            ...
          </span>
        );
      }

      pageNumbers.push(
        <button
          key="last"
          onClick={() => onPageChange(totalPages)}
          className="px-3 py-1 border border-[#C1C0C0] text-[#C1C0C0] hover:bg-gray-100"
        >
          {totalPages}
        </button>
      );
    }

    return pageNumbers;
  };

  return (
    <div className="flex justify-center items-center p-4">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="px-1 py-1 border border-[#C1C0C0] text-gray-700 
                   disabled:opacity-50 disabled:cursor-not-allowed 
                   hover:bg-gray-100"
      >
        <ChevronLeftIcon className=" size-6 text-[#C1C0C0]" />
      </button>

      <div className="flex">{renderPageNumbers()}</div>

      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="px-1 py-1 border border-[#C1C0C0] text-gray-700 
                   disabled:opacity-50 disabled:cursor-not-allowed 
                   hover:bg-gray-100"
      >
        <ChevronRightIcon className=" size-6 text-[#C1C0C0]" />
      </button>
    </div>
  );
};

export default CustomPaginator;
