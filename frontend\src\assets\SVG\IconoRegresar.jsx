import React from "react";
import { useNavigate } from "react-router-dom";
 
const IconoRegresar = ({ salir, pagina,size,color }) => {
  const navigate = useNavigate();

  if (salir) {
    return (
 
      <svg
        data-name="Layer 3"
        id="Layer_3"
        viewBox="0 0 32 32"
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        style={{ cursor: "pointer" }}
        onClick={() => {
          navigate(pagina);
        }}
      >
        <title />
        <path
          className="cls-1"
          d="M13.5,19.5v4.08l-9-8.08,9-8.08V11.5s14.17-.75,14.17,12.17C23.25,16.5,13.5,19.5,13.5,19.5Z" fill={color}
        />
        <line className="cls-2" x1="6.53" x2="13.8" y1="17.59" y2="11.24" fill={color}/>
        <polygon
          className="cls-3"
          points="4.5 15.5 5.88 16.63 13.44 9.75 13.5 7.42 4.5 15.5"
          fill={color}
        />
      </svg>
 
    );
  }
};

export default IconoRegresar;
