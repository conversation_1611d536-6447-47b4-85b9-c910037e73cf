En @backend\src\modules\plantillas\controller.py  la función descargar_plantilla crear la estructura de una plantilla excel.
Ahora en el modulo de estado_financiero quiero crear un endpoint que interprete la estructura de un archivo excel subido con la estructura de la plantilla.
El body que va a recibir el endpoint:
{
    int_idEmpresa: 4,
    int_idUsuarios: 1,
    ef_nombre: "Est Situacion Financier SGI",

}
Esta es mi idea:




IMAGEN EJEMPLO DEL EXCEL




Leer fila por fila:
1. Buscar en la columna A que diga una de estas Primeras cuatros etiquetas a buscar: [Tipo Estado Financiero] , [Tipo Periodo] , [Fecha Inicio Periodo] , [Fecha Fin Periodo].
- Si se encuentra una de esas cuatro, buscar en la columna B de la misma fila para extraer el valor, y  pasar a buscar el siguiente etiqueta.
- Si no hay en esa fila, pasar a la siguiente fila y buscar una de las 4 etiquetas o las que faltan, así sucesivamente hasta antes de encontrar la etiqueta [# Agrupador]
- Para la etiqueta [Tipo Estado Financiero] extraer un valor numerico (1: Estado de Situación Financiera, 2: Estado de Resultados )
- Para la etiqueta  [Tipo Periodo]  extraer un valor numerico (1: Anual, 2: Trimestral, 3: Mensual ) 
- Para las etiquetas [Fecha Inicio Periodo] y  [Fecha Fin Periodo]  extraer una fecha con un formato DD/MM/YYYY. Separar por los slash y extraer los datos.
- Si se encuentra la etiqueta [# Agrupador] antes de haber encontrado los valores de las primeras cuatro etiquetas a encontrar. Detener el proceso y mandar un error de que falta uno de esos 4 valores.

2. Cuando se encuentra la etiqueta [# Agrupador] ir a la siguiente fila y buscar en la columna A el nombre del agrupador. Si no hay, buscar en las siguientes filas hasta antes de encontrar una de estas etiquetas: [Nombre Cuenta] ,  [## Subagrupador] , [## Total] ,  [# Total - Agrupador] ,  [# Agrupador] , si no detener el proceso y enviar un error de que falta el nombre del agrupador.
- Si se encontró el nombre del agrupador, tener pendiente que la etiqueta [# Agrupador] tiene una etiqueta que cierra este bloque, la etiqueta de cierre se llama  [# Total - Agrupador] . Y es necesario encontrar la etiqueta de cierre antes de pasar a un nuevo bloque con etiqueta [# Agrupador]

3. Cuando se encuentra la  etiqueta [Nombre Cuenta] ir a la siguiente fila y buscar por cada fila las columnas A, B y C :
- En la columna A debe contener el nombre de la cuenta. 
- En la columna B debe contener un numero de 5 digitos (pero tratarlo como string). 
- En la columna C debe contener un numero con decimales que puede ser positivo o negativo.
Si se encuentra esta etiqueta [Nombre Cuenta], significa que debe estar dentro de un bloque padre: subagrupador ( [## Subagrupador] - [## Total] ) o agrupador ( [# Agrupador] - [# Total - Agrupador] ).
El bloque [# Agrupador] - [# Total - Agrupador] es el bloque principal. Puede contener cero, uno o más bloques [## Subagrupador] - [## Total] .

4. Si se encuentra una etiqueta [## Subagrupador]  ir a la siguiente fila y buscar en la columna A el nombre del subagrupador. Si no hay, buscar en las siguientes filas hasta antes de encontrar una de estas etiquetas: [Nombre Cuenta] ,  [## Subagrupador] , [## Total] ,  [# Total - Agrupador] ,  [# Agrupador] , si no detener el proceso y enviar un error de que falta el nombre del agrupador.
- Si se encontró el nombre del subagrupador, tener pendiente que la etiqueta [## Subagrupador] tiene una etiqueta que cierra este bloque, la etiqueta de cierre se llama  [## Total] . Y es necesario encontrar la etiqueta de cierre antes de pasar a un nuevo bloque con etiqueta [## Subagrupador] 

5. Si se encuentra la etiqueta [# Total - Agrupador] o la etiqueta [## Total] , buscar en la misma fila en la columna C para extraer el valor numerico con decimales.  
Si la etiqueta es [## Total] y no hay un valor en la columna C, significa que estamos en el bloque subagrupador y debemos sumar los valores (Columna C) de las cuentas que se encuentran en ese bloque
Si la etiqueta es [# Total - Agrupador] , no hay un valor en la columna C y no hay ni un bloque subagrupador, entonces eso significa que estamos en un bloque agrupador sin ningun bloque subagrupador dentro y por lo tanto debemos sumar los valores (Columna C) de todas las cuentas dentro del bloque agrupado.
Si la etiqueta es [# Total - Agrupador] , no hay un valor en la columna C y hay uno o mas bloques subagrupador, entonces significa que estamos en un bloque agrupador que dentro tiene uno o mas bloques subagrupador, y por lo tanto no es necesario calcularlo porque no va haber lugar para guardarlo en la base de datos, ya que lo estaría ocupando el valor de [## Total]

6. Los datos se guardarán en las siguientes tablas: tr_estadosfinancieros, tm_cuentas, tr_ef_cuentas, tm_agrupadores, tr_totalagrupador

7. En la tabla tr_estadosfinancieros:
str_nombre: ef_nombre (del body) (si no hay, usar el valor de la etiqueta [Tipo Estado Financiero] convertido a nombre)
dt_fechaRegistro: fecha actual (hallar la fecha actual con alguna librería)
int_idEmpresa: int_idEmpresa (del body)
int_idUsuarios: int_idUsuarios (del body)
int_tipoRegistro:  0 (siempre 0 porque son periodos contables reales)
int_referenciaSimulacion: null (porque no son simulaciones)
int_tipoPeriodo: (obtenido del valor de la etiqueta [Tipo Periodo] )
dt_fechaInicioPeriodo: (obtenido del valor de la etiqueta [Fecha Inicio Periodo] )
dt_fechaFinPeriodo:  (obtenido del valor de la etiqueta [Fecha Fin Periodo] )

8. En la tabla tm_agrupadores:
str_nombre: ( nombre del agrupador del bloque [# Agrupador] - [# Total - Agrupador]  )
str_nombre_subagrupador: null (opcional, si dentro del bloque [# Agrupador] - [# Total - Agrupador] existe al menos un bloque [## Subagrupador] - [## Total] )
* Si exiten dos o mas bloques [## Subagrupador] - [## Total] dentro del bloque agrupador crear un registro en esta tabla tm_agrupadores por cada bloque subagrupador.

9. En la tabla tm_cuentas:
Los valores  debajo de la etiqueta [Nombre Cuenta] de las columnas A y B van aquí. Crear un registro en la tabla tm_cuentas por cada una las filas con los 3 valores.
str_nombre: (valor de la columna A de la fila del excel en que nos encontramos)
str_codigo: (valor de la columna B de la fila del excel en que nos encontramos)
int_idAgrupador: (id del registro creado en la tabla tm_agrupadores para la pareja agrupador - subagrupador)
int_idEmpresa: int_idEmpresa (del body)

10. En la tabla tr_ef_cuentas:
Los valores debajo de la etiqueta [Nombre Cuenta] de la columna C va aquí. Crear un registro en la tabla tm_cuentas por cada una las filas con los 3 valores.
int_idEstadoFinanciero: (id del registro creado en la tabla tr_estadosfinancieros)
db_valor: (valor de la columna C de la fila del excel en que nos encontramos)
int_idCuenta: (id del registro creado en la tabla tm_cuentas)

11. En la tabla tr_totalagrupador:
int_idEstadoFinanciero: (id del registro creado en la tabla tr_estadosfinancieros)
int_idAgrupador: (id del registro creado en la tabla tm_agrupadores)
db_resultadoAgrupador: (puede ir el valor de la etiqueta [# Total - Agrupador] o el valor de la etiqueta [## Total] )
Si dentro de un bloque agrupador exite uno o mas bloques subagrupador, ya no se usa el valor de la etiqueta [# Total - Agrupador].

