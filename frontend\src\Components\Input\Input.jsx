import React from "react";

const Input = ({
  placeholder = "",
  className = "",
  icon = null,
  type = null,
  icon_direction = "right",
  disabled = false,
  value = "",
  isTextArea = false,
  rows = 4,
  onChange = null,
}) => {
  const inputPaddingClass = icon
    ? icon_direction === "left"
      ? "pl-12"
      : "pr-10"
    : "";

  return (
    <div className="relative">
      {icon && icon_direction === "left" ? (
        <div className="absolute left-3 top-1/2 -translate-y-1/2 z-10 text-gray-500">
          {icon}
        </div>
      ) : null}

      {isTextArea ? (
        <textarea
          disabled={disabled}
          className={`${className} ${inputPaddingClass} resize-none`}
          placeholder={placeholder}
          value={value}
          rows={rows}
          onChange={onChange}
        />
      ) : (
        <input
          type={type || "text"}
          disabled={disabled}
          className={`${className} ${inputPaddingClass}`}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
        />
      )}

      {icon && icon_direction === "right" ? (
        <div className="absolute right-3 top-1/2 -translate-y-1/2 z-10 text-gray-500">
          {icon}
        </div>
      ) : null}
    </div>
  );
};

export default Input;
