# Generated by Django 5.1 on 2025-04-18 20:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('detalles_evaluacion', '0004_detalleevaluacion_int_idevaluado'),
        ('evaluados', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='detalleevaluacion',
            name='int_idEvaluado',
        ),
        migrations.AddField(
            model_name='detalleevaluacion',
            name='int_idEvaluadoAsis',
            field=models.ForeignKey(db_column='int_idEvaluadoAsis', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='evaluado_asis', to='evaluados.evaluado'),
        ),
        migrations.AddField(
            model_name='detalleevaluacion',
            name='int_idEvaluadoTobe',
            field=models.ForeignKey(db_column='int_idEvaluadoTobe', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='evaluado_tobe', to='evaluados.evaluado'),
        ),
    ]
