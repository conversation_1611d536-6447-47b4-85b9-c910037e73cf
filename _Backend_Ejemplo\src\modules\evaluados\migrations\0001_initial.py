# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('evaluaciones', '0001_initial'),
        ('usuarios', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Evaluado',
            fields=[
                ('int_idEvaluado', models.AutoField(primary_key=True, serialize=False)),
                ('int_idTipoEvaluado', models.IntegerField()),
                ('int_idEvaluacion', models.ForeignKey(db_column='int_idEvaluacion', on_delete=django.db.models.deletion.CASCADE, related_name='evaluados', to='evaluaciones.evaluacion')),
                ('int_idUsuarios', models.ForeignKey(db_column='int_idUsuarios', on_delete=django.db.models.deletion.CASCADE, related_name='evaluados', to='usuarios.usuario')),
            ],
            options={
                'verbose_name': 'evaluado',
                'verbose_name_plural': 'evaluados',
                'db_table': 'tr_evaluados',
                'managed': True,
            },
        ),
    ]
