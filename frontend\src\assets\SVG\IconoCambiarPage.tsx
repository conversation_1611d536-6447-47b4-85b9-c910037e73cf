import React from "react";

const IconoCambiarPage = ({ size , color }) => {
  return (
    <svg
    width={size}
    height={size}
      version="1.1"
      viewBox="0 0 512 512"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Layer_1" />
      <g id="Layer_2">
        <g>
          <path
            d="M394.11,303.93V76.58c0-16.19-13.18-29.37-29.37-29.37H245.68c-16.19,0-29.37,13.18-29.37,29.37v102.11    h-69.05c-16.19,0-29.37,13.18-29.37,29.37v227.36c0,16.19,13.18,29.37,29.37,29.37h119.06c16.19,0,29.37-13.18,29.37-29.37V333.3    h69.05C380.93,333.3,394.11,320.13,394.11,303.93z M263.69,432.79h-113.8v-222.1h82.42h31.38V317.3V432.79z M362.11,301.3h-66.42    v-93.24c0-16.19-13.18-29.37-29.37-29.37h-18.01V79.21h113.8V301.3z"fill={color}
          />
          <path
            d="M446.62,431.1c8.84,0,16-7.16,16-16v-52.89c0-0.53-0.03-1.06-0.08-1.59c-0.02-0.23-0.06-0.45-0.1-0.67    c-0.04-0.29-0.08-0.58-0.13-0.87c-0.05-0.26-0.12-0.52-0.19-0.77c-0.06-0.25-0.12-0.5-0.19-0.75c-0.08-0.25-0.17-0.5-0.26-0.75    c-0.09-0.24-0.17-0.49-0.27-0.73c-0.1-0.23-0.21-0.46-0.32-0.68c-0.12-0.25-0.23-0.49-0.35-0.74c-0.12-0.22-0.25-0.43-0.38-0.64    c-0.14-0.24-0.27-0.47-0.43-0.71c-0.16-0.24-0.33-0.46-0.5-0.68c-0.14-0.19-0.27-0.39-0.43-0.57c-0.67-0.82-1.42-1.56-2.23-2.23    c-0.19-0.15-0.38-0.28-0.57-0.43c-0.23-0.17-0.45-0.35-0.69-0.5c-0.23-0.15-0.47-0.29-0.7-0.43c-0.21-0.13-0.42-0.26-0.64-0.38    c-0.24-0.13-0.49-0.24-0.73-0.35c-0.23-0.11-0.45-0.22-0.69-0.32c-0.24-0.1-0.48-0.18-0.72-0.26c-0.25-0.09-0.5-0.19-0.75-0.26    c-0.24-0.07-0.49-0.13-0.73-0.19c-0.26-0.07-0.52-0.14-0.78-0.19c-0.28-0.06-0.57-0.09-0.85-0.13c-0.23-0.03-0.46-0.08-0.69-0.1    c-0.53-0.05-1.05-0.08-1.58-0.08h-52.89c-8.84,0-16,7.16-16,16s7.16,16,16,16H408l-78.48,78.48c-6.25,6.25-6.25,16.38,0,22.63    c3.12,3.12,7.22,4.69,11.31,4.69s8.19-1.56,11.31-4.69l78.48-78.48v14.27C430.62,423.94,437.79,431.1,446.62,431.1z"fill={color}
          />
          <path
            d="M49.38,96.9v52.89c0,0.53,0.03,1.06,0.08,1.59c0.02,0.23,0.06,0.45,0.1,0.67c0.04,0.29,0.08,0.58,0.13,0.87    c0.05,0.26,0.12,0.52,0.19,0.77c0.06,0.25,0.12,0.5,0.19,0.75c0.08,0.25,0.17,0.5,0.26,0.75c0.09,0.24,0.17,0.49,0.27,0.73    c0.1,0.23,0.21,0.46,0.32,0.68c0.12,0.25,0.23,0.49,0.35,0.74c0.12,0.22,0.25,0.43,0.38,0.64c0.14,0.24,0.27,0.47,0.43,0.71    c0.16,0.24,0.33,0.46,0.5,0.68c0.14,0.19,0.27,0.39,0.43,0.57c0.67,0.82,1.42,1.56,2.23,2.23c0.18,0.15,0.38,0.28,0.57,0.43    c0.23,0.17,0.45,0.35,0.69,0.51c0.23,0.15,0.46,0.28,0.7,0.42c0.22,0.13,0.43,0.26,0.65,0.38c0.24,0.13,0.48,0.23,0.73,0.35    c0.23,0.11,0.46,0.22,0.69,0.32c0.24,0.1,0.48,0.18,0.72,0.26c0.25,0.09,0.5,0.19,0.76,0.27c0.24,0.07,0.48,0.13,0.72,0.19    c0.26,0.07,0.53,0.14,0.79,0.19c0.28,0.06,0.56,0.09,0.84,0.13c0.24,0.03,0.47,0.08,0.71,0.1c0.52,0.05,1.05,0.08,1.58,0.08h52.9    c8.84,0,16-7.16,16-16s-7.16-16-16-16H104l78.48-78.48c6.25-6.25,6.25-16.38,0-22.63c-6.25-6.25-16.38-6.25-22.63,0l-78.48,78.48    V96.9c0-8.84-7.16-16-16-16S49.38,88.06,49.38,96.9z" fill={color}
          />
        </g>
      </g>
    </svg>
  );
};

export default IconoCambiarPage;
