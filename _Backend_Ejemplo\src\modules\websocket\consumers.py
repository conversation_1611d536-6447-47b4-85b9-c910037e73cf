import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from src.modules.evaluaciones.models import Evaluacion
from src.modules.evaluados.models import Evaluado
from src.modules.detalles_evaluacion.models import DetalleEvaluacion

class EvaluacionConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.evaluacion_id = self.scope['url_route']['kwargs']['evaluacion_id']
        self.evaluacion_group_name = f'evaluacion_{self.evaluacion_id}'

        # Join evaluacion group
        await self.channel_layer.group_add(
            self.evaluacion_group_name,
            self.channel_name
        )

        await self.accept()

    async def disconnect(self, close_code):
        # Leave evaluacion group
        await self.channel_layer.group_discard(
            self.evaluacion_group_name,
            self.channel_name
        )

    # Receive message from WebSocket
    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message_type = text_data_json.get('type')
        
        # Handle different message types
        if message_type == 'request_update':
            # Client is requesting an update
            evaluacion_data = await self.get_evaluacion_data(self.evaluacion_id)
            await self.send(text_data=json.dumps({
                'type': 'evaluacion_update',
                'data': evaluacion_data
            }))

    # Receive message from evaluacion group
    async def evaluacion_update(self, event):
        # Send message to WebSocket
        await self.send(text_data=json.dumps(event))

    @database_sync_to_async
    def get_evaluacion_data(self, evaluacion_id):
        try:
            evaluacion = Evaluacion.objects.get(int_idEvaluacion=evaluacion_id)
            
            # Get basic evaluacion data
            data = {
                'id': evaluacion.int_idEvaluacion,
                'nombre': evaluacion.str_nombre,
                'descripcion': evaluacion.str_descripcion,
                'fecha_inicio': evaluacion.dt_fechaInicio.isoformat() if evaluacion.dt_fechaInicio else None,
                'fecha_fin': evaluacion.dt_fechaFin.isoformat() if evaluacion.dt_fechaFin else None,
                'estado': evaluacion.bool_estado,
                'tobe_completado': evaluacion.bool_tobe,
                'asis_completado': evaluacion.bool_asis,
            }
            
            # Get evaluados data
            evaluados = Evaluado.objects.filter(int_idEvaluacion=evaluacion_id)
            evaluados_data = []
            
            for evaluado in evaluados:
                # Calculate progress for this evaluado
                progress = self.calculate_evaluado_progress(evaluado)
                
                evaluados_data.append({
                    'id': evaluado.int_idEvaluado,
                    'tipo': evaluado.int_idTipoEvaluado,  # 1 for ToBe, 2 for AsIs
                    'estado': evaluado.bool_estado,
                    'avance': progress,
                    'usuario_id': evaluado.int_idUsuarios.int_idUsuarios if evaluado.int_idUsuarios else None,
                    'usuario_nombre': f"{evaluado.int_idUsuarios.str_Nombres} {evaluado.int_idUsuarios.str_Apellidos}" if evaluado.int_idUsuarios else None,
                })
            
            data['evaluados'] = evaluados_data
            return data
            
        except Evaluacion.DoesNotExist:
            return {'error': 'Evaluación no encontrada'}
    
    def calculate_evaluado_progress(self, evaluado):
        """Calculate progress percentage for an evaluado"""
        try:
            # Get all detalles for this evaluado
            detalles = DetalleEvaluacion.objects.filter(int_idEvaluado=evaluado.int_idEvaluado)
            
            if not detalles.exists():
                return 0
                
            total_detalles = detalles.count()
            
            # For ToBe evaluados, check str_valor_tobe
            if evaluado.int_idTipoEvaluado == 1:
                completed = detalles.exclude(str_valor_tobe__isnull=True).exclude(str_valor_tobe='').count()
            # For AsIs evaluados, check str_valor_asis
            else:
                completed = detalles.exclude(str_valor_asis__isnull=True).exclude(str_valor_asis='').count()
                
            if total_detalles > 0:
                return int((completed / total_detalles) * 100)
            return 0
            
        except Exception:
            return 0


class EvaluadoConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.evaluado_id = self.scope['url_route']['kwargs']['evaluado_id']
        self.evaluado_group_name = f'evaluado_{self.evaluado_id}'

        # Join evaluado group
        await self.channel_layer.group_add(
            self.evaluado_group_name,
            self.channel_name
        )

        # Also join the parent evaluacion group
        evaluacion_id = await self.get_evaluacion_id(self.evaluado_id)
        if evaluacion_id:
            self.evaluacion_group_name = f'evaluacion_{evaluacion_id}'
            await self.channel_layer.group_add(
                self.evaluacion_group_name,
                self.channel_name
            )

        await self.accept()

    async def disconnect(self, close_code):
        # Leave evaluado group
        await self.channel_layer.group_discard(
            self.evaluado_group_name,
            self.channel_name
        )
        
        # Leave evaluacion group if we joined it
        if hasattr(self, 'evaluacion_group_name'):
            await self.channel_layer.group_discard(
                self.evaluacion_group_name,
                self.channel_name
            )

    # Receive message from WebSocket
    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message_type = text_data_json.get('type')
        
        # Handle different message types
        if message_type == 'request_update':
            # Client is requesting an update
            evaluado_data = await self.get_evaluado_data(self.evaluado_id)
            await self.send(text_data=json.dumps({
                'type': 'evaluado_update',
                'data': evaluado_data
            }))

    # Receive message from evaluado group
    async def evaluado_update(self, event):
        # Send message to WebSocket
        await self.send(text_data=json.dumps(event))
    
    # Also handle messages from the parent evaluacion group
    async def evaluacion_update(self, event):
        # Send message to WebSocket
        await self.send(text_data=json.dumps(event))

    @database_sync_to_async
    def get_evaluacion_id(self, evaluado_id):
        try:
            evaluado = Evaluado.objects.get(int_idEvaluado=evaluado_id)
            return evaluado.int_idEvaluacion.int_idEvaluacion
        except Evaluado.DoesNotExist:
            return None

    @database_sync_to_async
    def get_evaluado_data(self, evaluado_id):
        try:
            evaluado = Evaluado.objects.get(int_idEvaluado=evaluado_id)
            
            # Calculate progress
            progress = self.calculate_evaluado_progress(evaluado)
            
            # Get basic evaluado data
            data = {
                'id': evaluado.int_idEvaluado,
                'tipo': evaluado.int_idTipoEvaluado,  # 1 for ToBe, 2 for AsIs
                'estado': evaluado.bool_estado,
                'avance': progress,
                'evaluacion_id': evaluado.int_idEvaluacion.int_idEvaluacion,
                'evaluacion_nombre': evaluado.int_idEvaluacion.str_nombre,
                'usuario_id': evaluado.int_idUsuarios.int_idUsuarios if evaluado.int_idUsuarios else None,
                'usuario_nombre': f"{evaluado.int_idUsuarios.str_Nombres} {evaluado.int_idUsuarios.str_Apellidos}" if evaluado.int_idUsuarios else None,
            }
            
            # Get detalles data
            detalles = DetalleEvaluacion.objects.filter(int_idEvaluado=evaluado)
            detalles_data = []
            
            for detalle in detalles:
                detalles_data.append({
                    'id': detalle.int_idDetalleEvaluacion,
                    'control_id': detalle.int_idControl.int_idControl,
                    'control_descripcion': detalle.int_idControl.str_descripcion,
                    'valor_tobe': detalle.str_valor_tobe,
                    'valor_asis': detalle.str_valor_asis,
                    'subdominio': detalle.int_idControl.int_idSubDominio.str_nombre,
                    'dominio': detalle.int_idControl.int_idSubDominio.int_idDominio.str_nombre,
                })
            
            data['detalles'] = detalles_data
            return data
            
        except Evaluado.DoesNotExist:
            return {'error': 'Evaluado no encontrado'}
    
    def calculate_evaluado_progress(self, evaluado):
        """Calculate progress percentage for an evaluado"""
        try:
            # Get all detalles for this evaluado
            detalles = DetalleEvaluacion.objects.filter(int_idEvaluado=evaluado.int_idEvaluado)
            
            if not detalles.exists():
                return 0
                
            total_detalles = detalles.count()
            
            # For ToBe evaluados, check str_valor_tobe
            if evaluado.int_idTipoEvaluado == 1:
                completed = detalles.exclude(str_valor_tobe__isnull=True).exclude(str_valor_tobe='').count()
            # For AsIs evaluados, check str_valor_asis
            else:
                completed = detalles.exclude(str_valor_asis__isnull=True).exclude(str_valor_asis='').count()
                
            if total_detalles > 0:
                return int((completed / total_detalles) * 100)
            return 0
            
        except Exception:
            return 0
