import React from "react";

const IconoCirculoUnderline = ({size,color}) => {
  return (
    <svg
      height={size}
      version="1.1"
      viewBox="0 0 20 20"
      width={size}
      xmlns="http://www.w3.org/2000/svg"
    >
      <title />
      <desc />
      <defs />
      <g
        fill="none"
        fill-rule="evenodd"
        id="Page-1"
        stroke="none"
        stroke-width="1"
      >
        <g
          fill="#000000"
          id="Core"
          transform="translate(-170.000000, -86.000000)"
        >
          <g
            id="check-circle-outline-blank"
            transform="translate(170.000000, 86.000000)"
          >
            <path
              d="M10,0 C4.5,0 0,4.5 0,10 C0,15.5 4.5,20 10,20 C15.5,20 20,15.5 20,10 C20,4.5 15.5,0 10,0 L10,0 Z M10,18 C5.6,18 2,14.4 2,10 C2,5.6 5.6,2 10,2 C14.4,2 18,5.6 18,10 C18,14.4 14.4,18 10,18 L10,18 Z"
              id="Shape"
              fill= {color}
             />
          </g>
        </g>
      </g>
    </svg>
  );
};

export default IconoCirculoUnderline;
