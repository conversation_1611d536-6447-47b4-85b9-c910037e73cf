from src.utils.classes import Response
from .models import SubDominio


class SubdominioController:
    def __init__(self): ...

    def get_by_dominio(self, dominio_id: int):
        try:
            subdominios = SubDominio.objects.filter(
                int_idDominio_id=dominio_id
            ).all()
            if not subdominios:
                return Response("No se encontraron subdominios", state=False)
            return Response(data=subdominios, state=True)

        except Exception as e:
            return Response(str(e), e)