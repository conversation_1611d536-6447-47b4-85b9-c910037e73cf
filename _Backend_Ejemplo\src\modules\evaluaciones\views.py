import os
from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from src.modules.categorias import serializers
from src.utils.classes import Response as APIResponse
from rest_framework.decorators import api_view
from drf_yasg.utils import swagger_auto_schema
from src.modules.detalles_evaluacion.controller import DetalleController
from src.modules.evaluados.serializers import EvaluadoReducidoConAvanceSerializer
from .models import Evaluacion
from .serializers import EvaluacionSerializer
from rest_framework.decorators import action
from django.db.models import Q
from .controller import EvaluacionController

from drf_yasg import openapi


class EvaluacionView(viewsets.ModelViewSet):
    queryset = Evaluacion.objects.all()
    serializer_class = EvaluacionSerializer
    permission_classes = [permissions.AllowAny]
    controller = EvaluacionController()
    detalle_controller = DetalleController()
    http_method_names = ["get", "post", "patch", "delete"]



    @swagger_auto_schema(
        operation_description="Crear una nueva evaluación",
        responses={
            201: EvaluacionSerializer(),
            400: "Error de validación - La fecha de fin debe ser posterior a la fecha de inicio"
        }
    )
    def create(self, request, *args, **kwargs):
        try:
            serializer = self.serializer_class(data=request.data)
            serializer.is_valid(raise_exception=True)
            evaluacion = serializer.save()
            # self.detalle_controller.crear_detalles(evaluacion)
            return Response(
                self.serializer_class(evaluacion).data,
                status=status.HTTP_201_CREATED
            )
        except ValueError as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Buscar evaluaciones por nombre",
        manual_parameters=[
            openapi.Parameter(
                'nombre',
                openapi.IN_QUERY,
                description="Nombre o parte del nombre a buscar",
                type=openapi.TYPE_STRING,
                required=True
            )
        ],
        responses={
            200: EvaluacionSerializer(many=True),
            400: "Error en la búsqueda"
        }
    )
    @action(detail=False, methods=['get'], url_path='buscar')
    def buscar_evaluaciones(self, request):
        """
        Endpoint para buscar evaluaciones por nombre
        """
        try:
            nombre = request.query_params.get('nombre', '')
            if not nombre:
                return Response(
                    {"error": "El parámetro 'nombre' es requerido"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            response = self.controller.buscar_por_nombre(nombre)

            if not response.state:
                return Response(
                    {"error": response.message},
                    status=status.HTTP_400_BAD_REQUEST
                )

            serializer = self.serializer_class(response.data, many=True)
            return Response(
                serializer.data,
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @swagger_auto_schema(
        operation_description="Búsqueda avanzada de evaluaciones",
        manual_parameters=[
            openapi.Parameter(
                'nombre',
                openapi.IN_QUERY,
                description="Nombre o parte del nombre a buscar",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'framework_id',
                openapi.IN_QUERY,
                description="ID del framework",
                type=openapi.TYPE_INTEGER,
                required=False
            ),
            openapi.Parameter(
                'fecha_inicio',
                openapi.IN_QUERY,
                description="Fecha de inicio (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'estado',
                openapi.IN_QUERY,
                description="Estado de la evaluación (true/false)",
                type=openapi.TYPE_BOOLEAN,
                required=False
            )
        ],
        responses={
            200: EvaluacionSerializer(many=True),
            400: "Error en la búsqueda"
        }
    )
    @action(detail=False, methods=['get'], url_path='filtro')
    def busqueda_avanzada(self, request):
        """
        Endpoint para búsqueda avanzada de evaluaciones con múltiples filtros opcionales
        """
        try:
            filtros = {
                'nombre': request.query_params.get('nombre'),
                'framework_id': request.query_params.get('framework_id'),
                'fecha_inicio': request.query_params.get('fecha_inicio'),
                'estado': request.query_params.get('estado')
            }

            # Eliminar los filtros que no tienen valor
            filtros = {k: v for k, v in filtros.items() if v is not None}

            response = self.controller.buscar_avanzado(filtros)

            if not response.state:
                return Response(
                    {"error": response.message},
                    status=status.HTTP_400_BAD_REQUEST
                )

            serializer = self.serializer_class(response.data, many=True)
            return Response(
                serializer.data,
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @swagger_auto_schema(
        operation_description="Hacer y descargar un reporte de controles de una evaluación",
        responses={
            200: "Reporte descargado",
            400: "Error al descargar el reporte",
        },
    )
    @action(detail=False, methods=['get'], url_path='<int:id_evaluacion>/reporte/controles')
    def reporte_controles(self, request, id_evaluacion):
        """
        Endpoint para hacer un reporte de controles de una evaluación
        """
        try:

            fileResponse = self.controller.reportar_controles(id_evaluacion)

            if isinstance(fileResponse,APIResponse) and not fileResponse.state:
                print(fileResponse.message)
                return Response(
                    "Error al descargar el reporte",
                    status=status.HTTP_400_BAD_REQUEST
                )

            return fileResponse
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)


    @swagger_auto_schema(
        operation_description="Hacer y descargar una lista de resultados de una evaluación",
        responses={
            200: "Reporte descargado",
            400: "Error al descargar la lista de Resultados",
        },
    )
    @action(detail=False, methods=['get'], url_path='<int:id_evaluacion>/resultados')
    def lista_resultados(self, request, id_evaluacion):
        """
        Endpoint para retornar una lista de resultados.
        """
        try:

            resultados = self.controller.listar_resultados(id_evaluacion)

            if not resultados.state:
                return Response(resultados.message, status=status.HTTP_400_BAD_REQUEST)

            return Response(resultados.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)


    @swagger_auto_schema(
        operation_description="Hacer y descargar una lista de resultados de una evaluación por subdominio",
        responses={
            200: "Reporte descargado",
            400: "Error al descargar la lista de Resultados por subdominio",
        },
    )
    @action(detail=False, methods=['get'], url_path='<int:id_evaluacion>/resultados/subdominio/<int:id_subdominio>')
    def lista_resultados_by_subdominio(self, request, id_evaluacion, id_subdominio):
        """
        Endpoint para retornar una lista de resultados por subdominio.
        """
        try:

            resultados = self.controller.listar_resultados_by_subdominio(id_evaluacion, id_subdominio)

            if not resultados.state:
                return Response(resultados.message, status=status.HTTP_400_BAD_REQUEST)

            return Response(resultados.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)



    @swagger_auto_schema(
        operation_description="Calcular el avance ToBe de una evaluación",
        responses={
            200: "Avance ToBe",
            400: "Error al calcular el avance ToBe",
        },
    )
    @action(detail=False, methods=['get'], url_path='<int:id_evaluacion>/avance/tobe')
    def avance_tobe_by_evaluacion(self, request, id_evaluacion):
        """
        Endpoint para calcular el avance ToBe de una evaluación.
        """
        try:

            avance_tobe = self.controller.calcular_avance_tobe(id_evaluacion)

            if not avance_tobe.state:
                return Response(avance_tobe.message, status=status.HTTP_400_BAD_REQUEST)

            return Response(avance_tobe.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Calcular el avance ToBe de un evaluado específico",
        responses={
            200: "Avance ToBe del evaluado",
            400: "Error al calcular el avance ToBe del evaluado",
        },
    )
    @action(detail=False, methods=['get'], url_path='<int:id_evaluacion>/avance/tobe/evaluado/<int:id_evaluado>')
    def avance_tobe_by_evaluado(self, request, id_evaluacion, id_evaluado):
        """
        Endpoint para calcular el avance ToBe de un evaluado específico.
        """
        try:
            avance_tobe = self.controller.calcular_avance_tobe_evaluado(id_evaluacion, id_evaluado)

            if not avance_tobe.state:
                return Response(avance_tobe.message, status=status.HTTP_400_BAD_REQUEST)

            return Response(avance_tobe.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Calcular el avance AsIs de una evaluación",
        responses={
            200: "Avance AsIs",
            400: "Error al calcular el avance AsIs",
        },
    )
    @action(detail=False, methods=['get'], url_path='<int:id_evaluacion>/avance/asis')
    def avance_asis_by_evaluacion(self, request, id_evaluacion):
        """
        Endpoint para calcular el avance AsIs de una evaluación.
        """
        try:

            avance_asis = self.controller.calcular_avance_asis(id_evaluacion)

            if not avance_asis.state:
                return Response(avance_asis.message, status=status.HTTP_400_BAD_REQUEST)

            return Response(avance_asis.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Calcular el avance AsIs de un evaluado específico",
        responses={
            200: "Avance AsIs del evaluado",
            400: "Error al calcular el avance AsIs del evaluado",
        },
    )
    @action(detail=False, methods=['get'], url_path='<int:id_evaluacion>/avance/asis/evaluado/<int:id_evaluado>')
    def avance_asis_by_evaluado(self, request, id_evaluacion, id_evaluado):
        """
        Endpoint para calcular el avance AsIs de un evaluado específico.
        """
        try:
            avance_asis = self.controller.calcular_avance_asis_evaluado(id_evaluacion, id_evaluado)

            if not avance_asis.state:
                return Response(avance_asis.message, status=status.HTTP_400_BAD_REQUEST)

            return Response(avance_asis.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)


    @swagger_auto_schema(
        operation_description="Endpoint para cambiar el estado de una evaluacion específica.",
        request_body=None,  # No se necesita cuerpo en la petición
        responses={
            200: openapi.Response("Estado cambiado con éxito."),
            400: openapi.Response("Error al cambiar el estado."),
            404: openapi.Response("Evaluacion no encontrada."),
        },
    )
    @action(detail=False, methods=["patch"], url_path="<int:id_evaluacion>/estado/update")
    def cambiar_estado_evaluacion(self, request, id_evaluacion):
        """
        Acción para cambiar el estado booleano (bool_estado) de una evaluacion.
        true: En proceso
        false: Terminado
        """
        try:
            response: APIResponse = self.controller.cambiar_estado(id_evaluacion)
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )

            return Response(data=response.message, status=status.HTTP_200_OK)

        except ValueError:
            return Response(
                data="ID de la evaluacion inválido.", status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                data=f"Error inesperado: {str(e)}",
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


    @swagger_auto_schema(
        operation_description="Calcular el Resultado promedio ToBe y AsIs de una evaluación",
        responses={
            200: "Resultado promedio ToBe y AsIs",
            400: "Error al calcular el resultado promedio",
        },
    )
    @action(detail=False, methods=['get'], url_path='<int:id_evaluacion>/resultado/promedio')
    def resultado_promedio_by_evaluacion(self, request, id_evaluacion):
        """
        Endpoint para calcular el Resultado promedio ToBe y AsIs de una evaluación.
        """
        try:

            resultado_promedio = self.controller.calcular_resultado_promedio(id_evaluacion)

            if not resultado_promedio.state:
                return Response(resultado_promedio.message, status=status.HTTP_400_BAD_REQUEST)

            return Response(resultado_promedio.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)


    @swagger_auto_schema(
        operation_description="Lista de evaluaciones por el id usuario que tiene el objeto evaluado.",
        responses={
            200: "Lista de evaluaciones por usuario del evaluado",
            400: "Error al traer la lista de evaluaciones por usuario del evaluado",
        },
    )
    @action(detail=False, methods=['get'], url_path='evaluado/<int:id_evaluado>')
    def evaluacion_by_evaluado(self, request, id_evaluado):
        """
        Endpoint para traer la lista de evaluaciones por el id usuario que tiene el objeto evaluado (obtenido con el id_evaluado).
        """
        try:

            evaluaciones = self.controller.get_by_evaluado(id_evaluado)

            if not evaluaciones.state:
                return Response(evaluaciones.message, status=status.HTTP_400_BAD_REQUEST)

            return Response(EvaluacionSerializer(evaluaciones.data,many=True).data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Obtener evaluaciones directamente por ID de usuario",
        responses={
            200: EvaluacionSerializer(many=True),
            400: "Error al obtener evaluaciones"
        }
    )
    @action(detail=False, methods=['get'], url_path='usuario/<int:id_usuario>')
    def evaluacion_by_usuario(self, request, id_usuario):
        """
        Endpoint para traer la lista de evaluaciones directamente por el id usuario.
        Incluye los evaluados asociados a cada evaluación que pertenecen al usuario.
        """
        try:
            evaluaciones = self.controller.get_by_usuario(id_usuario)

            if not evaluaciones.state:
                return Response(evaluaciones.message, status=status.HTTP_400_BAD_REQUEST)

            # Procesamos los datos para incluir los evaluados en cada evaluación
            resultado = []
            for item in evaluaciones.data:
                evaluacion_data = EvaluacionSerializer(item['evaluacion']).data

                # Procesamos los evaluados con su avance
                evaluados_data = []
                for evaluado_item in item['evaluados']:
                    evaluado_data = {
                        'int_idEvaluado': evaluado_item['evaluado'].int_idEvaluado,
                        'int_idTipoEvaluado': evaluado_item['evaluado'].int_idTipoEvaluado,
                        'bool_estado': evaluado_item['evaluado'].bool_estado,
                        'avance': evaluado_item['avance']
                    }
                    evaluados_data.append(evaluado_data)

                evaluacion_data['evaluados'] = evaluados_data
                resultado.append(evaluacion_data)

            return Response(resultado, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Obtener resultados promedio por control de una evaluación",
        responses={
            200: "Resultados promedio por control",
            400: "Error al obtener los resultados promedio por control",
        },
    )
    @action(detail=False, methods=['get'], url_path='<int:id_evaluacion>/resultados/control/promedio')
    def lista_resultados_control_promedio(self, request, id_evaluacion):
        """
        Endpoint para retornar una lista de resultados promedio por control.
        """
        try:
            resultados = self.controller.listar_resultados_control_promedio(id_evaluacion)

            if not resultados.state:
                return Response(resultados.message, status=status.HTTP_400_BAD_REQUEST)

            return Response(resultados.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Obtener resultados promedio por control de una evaluación filtrados por subdominio",
        responses={
            200: "Resultados promedio por control y subdominio",
            400: "Error al obtener los resultados promedio por control y subdominio",
        },
    )
    @action(detail=False, methods=['get'], url_path='<int:id_evaluacion>/resultados/control/promedio/subdominio/<int:id_subdominio>')
    def lista_resultados_control_promedio_by_subdominio(self, request, id_evaluacion, id_subdominio):
        """
        Endpoint para retornar una lista de resultados promedio por control filtrados por subdominio.
        """
        try:
            resultados = self.controller.listar_resultados_control_promedio_by_subdominio(id_evaluacion, id_subdominio)

            if not resultados.state:
                return Response(resultados.message, status=status.HTTP_400_BAD_REQUEST)

            return Response(resultados.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)