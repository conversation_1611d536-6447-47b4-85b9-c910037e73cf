import React, { useState, useEffect } from "react";

const FiltroModal = ({ open, handleClose, handleSubmit, clearFiltersExternal }) => {
  const [filters, setFilters] = useState({
    origen: "",
    fecha_inicio: "",
    fecha_fin: "",
    estado: null
  });

  // Effect to handle external filter clearing
  useEffect(() => {
    if (clearFiltersExternal) {
      clearFilters();
    }
  }, [clearFiltersExternal]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (value) => {
    setFilters(prev => ({
      ...prev,
      estado: prev.estado === value ? null : value
    }));
  };

  const clearFilters = () => {
    // Reset all filters to their initial state
    setFilters({
      origen: "",
      fecha_inicio: "",
      fecha_fin: "",
      estado: null
    });
  };

  const onSubmit = () => {
    // Filter out empty values
    const filtersToSubmit = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) =>
        value !== null && value !== ""
      )
    );
    handleSubmit(filtersToSubmit);
    // Cerrar el modal después de aplicar los filtros
    handleClose();
  };

  // Expose clearFilters to parent component
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      window.clearFilterModal = clearFilters;
    }
    return () => {
      if (typeof window !== 'undefined') {
        delete window.clearFilterModal;
      }
    };
  }, []);

  if (!open) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
      onClick={handleClose} // Close when clicking outside
    >
      <div
        className="relative w-full max-w-[30rem] max-h-[50rem] m-4 md:m-0"
        onClick={e => e.stopPropagation()} // Prevent closing when clicking inside
      >
        <div className="bg-white rounded-xl shadow-xl p-6 relative">
          {/* Contenido del Modal */}
          <div className="p-4 mb-4">
            {/* Contenido principal del modal */}
            <h2 className="text-xl font-bold mb-4">Filtro de Proyectos</h2>

            {/* Filtros */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Proyecto/Origen
                </label>
                <input
                  type="text"
                  name="origen"
                  value={filters.origen}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                  placeholder="Buscar por nombre u origen"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Fecha de Inicio
                </label>
                <input
                  type="date"
                  name="fecha_inicio"
                  value={filters.fecha_inicio}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Fecha de Fin
                </label>
                <input
                  type="date"
                  name="fecha_fin"
                  value={filters.fecha_fin}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Estado
                </label>
                <div className="flex flex-wrap gap-4 items-center mt-2">
                  <div className="flex gap-2 items-center">
                    <input
                      type="checkbox"
                      id="estado-nuevo"
                      checked={filters.estado === "3"}
                      onChange={() => handleCheckboxChange("3")}
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="estado-nuevo" className="text-sm font-medium text-gray-700">
                      Nuevo
                    </label>
                  </div>
                  <div className="flex gap-2 items-center">
                    <input
                      type="checkbox"
                      id="estado-proceso"
                      checked={filters.estado === "2"}
                      onChange={() => handleCheckboxChange("2")}
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="estado-proceso" className="text-sm font-medium text-gray-700">
                      En Proceso
                    </label>
                  </div>
                  <div className="flex gap-2 items-center">
                    <input
                      type="checkbox"
                      id="estado-terminado"
                      checked={filters.estado === "1"}
                      onChange={() => handleCheckboxChange("1")}
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="estado-terminado" className="text-sm font-medium text-gray-700">
                      Terminado
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Botones de acción */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={clearFilters}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition cursor-pointer"
            >
              Limpiar
            </button>
            <button
              onClick={onSubmit}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition cursor-pointer"
            >
              Aplicar Filtros
            </button>
          </div>

          {/* Botón de cierre en la esquina superior derecha */}
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 text-gray-600 hover:text-gray-900 transition cursor-pointer"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};
export default FiltroModal;
