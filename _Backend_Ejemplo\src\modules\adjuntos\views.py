from rest_framework import viewsets, status, permissions
from rest_framework.response import Response
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from .models import Adjunto
from .serializers import AdjuntoSerializer, AdjuntoNombreSerializer
from .controller import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AdjuntoDownloadController, AdjuntoDeleteController
from src.utils.classes import Response as ResponseAPI

class AdjuntosView(viewsets.ModelViewSet):
    """
    ViewSet para gestionar los archivos de documentos.
    """
    queryset = Adjunto.objects.all()
    serializer_class = AdjuntoSerializer
    permission_classes = [permissions.AllowAny]
    http_method_names = ["get", "post", "delete"]

    @swagger_auto_schema(operation_description="Crear un nuevo adjunto de paso")
    @action(
        detail=False,
        methods=["post"],
        url_path="upload/paso/<int:paso_id>",
    )
    def subir_adjunto_paso(self, request, *args, **kwargs):
        """
        Crear un nuevo adjunto.
        """
        paso_id = kwargs.get("paso_id")
        file = request.FILES.get("archivo")
        controller = AdjuntoController(file, paso_id)

        response: ResponseAPI = controller.registrar_adjunto_paso()

        if not response.state:
            return Response(response.message, status=status.HTTP_400_BAD_REQUEST)

        return Response(response.message, status=status.HTTP_201_CREATED)
    
    @swagger_auto_schema(operation_description="Descargar adjunto")
    @action(
        detail=False,
        methods=["get"],
        url_path="<int:adjunto_id>/download",
    )
    def download_adjunto_paso(self, request, *args, **kwargs):
        adjunto_id = kwargs.get("adjunto_id")
        controller = AdjuntoDownloadController()
        adjunto = Adjunto.objects.filter(int_idAdjunto=adjunto_id).first()
    
        if not adjunto:
            return Response(
                "No existe el adjunto.",
                status=status.HTTP_404_NOT_FOUND,
            )

        file = controller.get_docs(adjunto.str_ruta)
        if isinstance(file, ResponseAPI):
            return Response(file.message, status=status.HTTP_400_BAD_REQUEST)
        return file
    
    @swagger_auto_schema(operation_description="Eliminar un adjunto")
    def destroy(self, request, *args, **kwargs):
        adjunto_id = self.kwargs.get("pk")  # Get the ID from the URL keyword arguments
        controller = AdjuntoDeleteController()
        response = controller.delete_adjunto(adjunto_id)
        if not response.state:
            return Response(response.message, status=status.HTTP_404_NOT_FOUND)
        return Response(
            response.message,
            status=status.HTTP_204_NO_CONTENT,
        )
    

    @swagger_auto_schema(operation_description="Lista de Nombres de adjuntos por paso")
    @action(
        detail=False,
        methods=["get"],
        url_path="paso/<int:paso_id>",
    )
    def listName_adjunto_paso(self, request, *args, **kwargs):
        paso_id = kwargs.get("paso_id")
        adjuntos = Adjunto.objects.filter(int_idPaso_id=paso_id)

        if not adjuntos:
            return Response(
                "No existen adjuntos.",
                status=status.HTTP_404_NOT_FOUND,
            )

        # Serializar los datos usando el nuevo serializer
        serializer = AdjuntoNombreSerializer(adjuntos, many=True)

        # Devolver los datos serializados
        return Response(serializer.data, status=status.HTTP_200_OK)
    

