from django.shortcuts import render
from rest_framework import viewsets
from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema

from src.utils.classes import Response as APIResponse
from .models import Paso
from .serializers import PasoSerializer
from .controller import <PERSON><PERSON><PERSON><PERSON><PERSON>
# Create your views here.

class PasoView(viewsets.ModelViewSet):
    queryset = Paso.objects.all()
    serializer_class = PasoSerializer
    permission_classes = [permissions.AllowAny]
    controller = PasoController()
    http_method_names = ["get", "post", "patch", "delete"]

    @swagger_auto_schema(
        operation_description="Endpoint para obtener pasos por nivel",
    )
    @action(detail=False, methods=["get"], url_path="nivel/<int:nivel_id>")
    def pasos_by_nivel(self, request, nivel_id, *args, **kwargs):
        try:
            # serializer = PasosSerializer(data=request.data)
            # serializer.is_valid(raise_exception=True)
            # serializer.validated_data
            response: APIResponse = self.controller.get_by_nivel(nivel_id)
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(
                data=PasoSerializer(response.data, many=True).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Endpoint para obtener los pasos necesarios para ir del nivel Asis al nivel Tobe para una evaluación",
    )
    @action(detail=False, methods=["get"], url_path="evaluacion/<int:evaluacion_id>/asis-to-tobe")
    def pasos_asis_to_tobe(self, request, evaluacion_id, *args, **kwargs):
        """
        Obtiene todos los pasos necesarios para ir del nivel Asis al nivel Tobe
        para una evaluación específica.
        """
        try:
            response: APIResponse = self.controller.get_pasos_asis_to_tobe(evaluacion_id)
            if not response.state:
                print("Error en get_pasos_asis_to_tobe")
                print(response.message)
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(
                data=response.data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            print("Error en pasos_asis_to_tobe")
            print(str(e))
            return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)