from django.db import models

class Usuario(models.Model):
    class Meta:
        db_table = 'tm_usuarios'
        managed = True
        verbose_name = 'usuario'
        verbose_name_plural = 'usuarios'
        
    int_idUsuarios = models.AutoField(primary_key=True)
    str_Nombres = models.CharField(max_length=255)
    str_Apellidos = models.CharField(max_length=255)
    str_Correo = models.CharField(max_length=150)
    str_Documento = models.CharField(max_length=20)
    str_UnidadNegocio = models.CharField(max_length=255)
    str_Clave = models.Char<PERSON>ield(max_length=255)
    int_idEspecialidad = models.IntegerField()
    int_Estado = models.IntegerField(default=1)
    str_Codigo = models.CharField(max_length=8, null=True)
    dt_FechaCreacion = models.DateTimeField()
    dt_FechaModificacion = models.DateTimeField(null=True)
    int_idUsuarioCreacion = models.IntegerField()
    int_idUsuarioModificacion = models.IntegerField(null=True)
    
    def __str__(self):
        return f"{self.str_Nombres} {self.str_Apellidos}"
