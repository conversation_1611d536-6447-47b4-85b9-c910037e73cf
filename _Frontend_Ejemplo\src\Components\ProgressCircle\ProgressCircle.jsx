import React from "react";

const ProgressCircle = ({ percentage, size = 80, strokeWidth = 8 }) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const offset = circumference - (percentage / 100) * circumference;

  // Calcular la posición del círculo final con corrección
  const centerX = size / 2;
  const centerY = size / 2;
  const angle = ((percentage + 26) / 100) * 2 * Math.PI;
  const endX = centerX + radius * Math.cos(angle - Math.PI / 2);
  const endY = centerY + radius * Math.sin(angle - Math.PI / 2);

  return (
    <div className="relative inline-block">
      <svg
        width={size}
        height={size}
        viewBox={`0 0 ${size} ${size}`}
        className="transform -rotate-90"
      >
        {/* Background Circle */}
        <circle
          cx={centerX}
          cy={centerY}
          r={radius}
          fill="transparent"
          stroke={percentage >= 50 ? "#F6FFFB" : "#FFF3F7"}
          strokeWidth={strokeWidth}
        />

        {/* Progress Circle */}
        <circle
          cx={centerX}
          cy={centerY}
          r={radius}
          fill="transparent"
          stroke={percentage >=50 ? "#47D691" : "#FF0000"}
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          strokeDashoffset={offset}
          strokeLinecap="round"
        />

        {/* End Cap Circle - White Center */}
        <circle
          
          cx={endX}
          cy={endY}
          r={strokeWidth / 1.3}
          fill="white"
          stroke={percentage >=50 ? "#47D691" : "#FF0000"}
          strokeWidth={strokeWidth / 2}
        />
      </svg>

      {/* Percentage Text */}
      <div
        className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 
                    text-sm font-bold text-gray-700 text-center`}
      >
        <div className="px-3 text-lg poppins-font-600">{percentage}%</div>
        <div className="px-1 poppins-font">Avance</div>
      </div>
    </div>
  );
};

export default ProgressCircle;
