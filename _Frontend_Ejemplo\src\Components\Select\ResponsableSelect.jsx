import { ChevronDownIcon } from "@heroicons/react/16/solid";
import React, { useState, useRef, useEffect, useCallback } from "react";
import Portal from "../Portal/Portal";

const ResponsableSelect = ({
  options,
  placeholder = "Seleccionar...",
  onChange,
  className = "",
  disabled = false,
  borderColor = "#7BACFF",
  textColor = "#91A8E2",
  hoverBgColor = "#F8FAFB",
  value = null,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const selectRef = useRef(null);
  const buttonRef = useRef(null);

  // Función para calcular la posición del dropdown
  const calculateDropdownPosition = useCallback(() => {
    if (!isOpen || !buttonRef.current) return;

    const rect = buttonRef.current.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const windowWidth = window.innerWidth;
    const dropdownHeight = Math.min(options.length * 40, 240);

    // Verificar espacios disponibles
    const spaceBelow = windowHeight - rect.bottom;
    const spaceAbove = rect.top;

    // Determinar si mostrar abajo o arriba
    const showBelow = spaceBelow >= dropdownHeight || spaceBelow >= spaceAbove;

    // Calcular posición vertical
    let top = showBelow
      ? rect.bottom + window.scrollY
      : (rect.top - dropdownHeight) + window.scrollY;

    // Calcular posición horizontal
    let left = rect.left + window.scrollX;

    // Asegurar que el dropdown no se salga de la pantalla horizontalmente
    if (left + rect.width > windowWidth + window.scrollX) {
      left = windowWidth - rect.width + window.scrollX;
    }

    // Asegurar que el dropdown no se salga de la pantalla verticalmente
    if (!showBelow && top < window.scrollY) {
      // Si no hay suficiente espacio arriba, mostrar abajo de todos modos
      top = rect.bottom + window.scrollY;
    } else if (showBelow && top + dropdownHeight > windowHeight + window.scrollY) {
      // Si no hay suficiente espacio abajo, ajustar la altura o mostrar arriba
      if (spaceAbove >= dropdownHeight) {
        top = rect.top - dropdownHeight + window.scrollY;
      }
    }

    setDropdownPosition({
      top,
      left,
      width: rect.width,
    });
  }, [isOpen, options.length]);

  // Calcular la posición del dropdown cuando se abre
  useEffect(() => {
    if (isOpen) {
      calculateDropdownPosition();
    }
  }, [isOpen, calculateDropdownPosition]);

  // Cerrar el select cuando se hace clic fuera del componente
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target) &&
        !event.target.closest('.responsable-select-dropdown-menu')
      ) {
        setIsOpen(false);
      }
    };

    // Agregar event listener cuando el select está abierto
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    // Limpiar event listener
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  // Recalcular posición cuando cambia el tamaño de la ventana o al hacer scroll
  useEffect(() => {
    // Crear una referencia al ResizeObserver para poder limpiarlo después
    let resizeObserver = null;

    if (isOpen) {
      // Agregar event listeners
      window.addEventListener('resize', calculateDropdownPosition);
      window.addEventListener('scroll', calculateDropdownPosition);

      // Usar ResizeObserver si está disponible en el navegador
      if (typeof ResizeObserver !== 'undefined' && buttonRef.current) {
        resizeObserver = new ResizeObserver(calculateDropdownPosition);
        resizeObserver.observe(buttonRef.current);
      }
    }

    return () => {
      // Limpiar event listeners
      window.removeEventListener('resize', calculateDropdownPosition);
      window.removeEventListener('scroll', calculateDropdownPosition);

      // Desconectar ResizeObserver si existe
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [isOpen, calculateDropdownPosition]);

  // Actualizar el estado interno cuando cambia la prop value
  useEffect(() => {
    if (value !== null) {
      const option = options.find(opt => opt.value === value);
      if (option) {
        setSelectedOption(option);
      }
    } else {
      setSelectedOption(null);
    }
  }, [value, options]);

  const handleSelect = (option) => {
    setSelectedOption(option);
    setIsOpen(false);
    if (onChange) {
      onChange(option);
    }
  };

  return (
    <div className={`w-full ${className}`} ref={selectRef}>
      <button
        type="button"
        disabled={disabled}
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className={`
          w-full flex items-center justify-between
          px-4 py-2 text-left
          border rounded-md
          poppins-font
          h-[2.5rem]
          ${
            disabled
              ? "bg-gray-100 text-gray-500 cursor-not-allowed"
              : "bg-white hover:bg-gray-50"
          }
        `}
        style={{ borderColor: borderColor }}
      >
        <span style={{ color: textColor }}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>

        <ChevronDownIcon
          className={`w-5 h-5 transition-transform
            ${isOpen ? "rotate-180" : ""}
            ${disabled ? "opacity-50" : ""}`}
          style={{ color: textColor }}
        />
      </button>

      {isOpen && !disabled && (
        <Portal>
          <div
            className="responsable-select-dropdown-menu fixed z-50
            bg-white border rounded-md
            shadow-lg max-h-60 overflow-y-auto"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              width: `${dropdownPosition.width}px`,
              borderColor: borderColor
            }}
          >
            {options.map((option) => (
              <div
                key={option.value}
                onClick={() => handleSelect(option)}
                className={`
                  px-4 py-2
                  cursor-pointer
                  poppins-font
                `}
                style={{
                  color: textColor,
                  backgroundColor: option.value === selectedOption?.value ? hoverBgColor : 'transparent'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = hoverBgColor}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = option.value === selectedOption?.value ? hoverBgColor : 'transparent'}
              >
                {option.label}
              </div>
            ))}
          </div>
        </Portal>
      )}
    </div>
  );
};

export default ResponsableSelect;
