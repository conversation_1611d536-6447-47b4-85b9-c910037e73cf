import axios from 'axios';
import Cookies from 'js-cookie';
import { decrypt } from './TokenService';
import API_GESTOR from '../assets/APIS/ApisAdministrador';

/**
 * Service for handling project-related API requests
 */
class ProyectosService {
  /**
   * Get the user ID from cookies
   * @returns {string|null} The user ID or null if not found
   */
  getUserId() {
    try {
      const userId = decrypt(Cookies.get("hora_llegadaProyectus"));
      return userId;
    } catch (error) {
      console.error("Error getting user ID:", error);
      return null;
    }
  }

  getPerfil() {
    try {
      const perfil = decrypt(Cookies.get("rol"));
      return perfil;
    } catch (error) {
      console.error("Error getting perfil:", error);
      return null;
    }
  }

  /**
   * Get the subscription ID from cookies
   * @returns {string|null} The subscription ID or null if not found
   */
  getSubscriptionId() {
    try {
      const subscriptionId = decrypt(Cookies.get("suscripcionProyectus"));
      return subscriptionId;
    } catch (error) {
      console.error("Error getting subscription ID:", error);
      return null;
    }
  }

  /**
   * Get the company ID from cookies
   * @returns {number|null} The company ID or null if not found
   */
  getCompanyId() {
    try {
      const companyId = decrypt(Cookies.get("busProyectus"));
      return parseInt(companyId);
    } catch (error) {
      console.error("Error getting company ID:", error);
      return null;
    }
  }

  /**
   * Get the authentication token from cookies
   * @returns {string|null} The token or null if not found
   */
  getToken() {
    try {
      const token = Cookies.get("Token");
      return token;
    } catch (error) {
      console.error("Error getting token:", error);
      return null;
    }
  }

  /**
   * Get all projects for the current user
   * @returns {Promise<Array>} Array of projects
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getProyectosByUsuario() {
    try {
      const userId = this.getUserId();
      if (!userId) {
        throw new Error("User ID not found");
      }

      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(API_GESTOR.ProyectosByUsuario(userId), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        // console.log("ProyectosService getProyectosByUsuario response:", response.data);
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error fetching projects:", error);

      // Manejar específicamente el error 404
      if (error.response && error.response.status === 404) {
        const customError = new Error("No tienes proyectos asignados. ¡Es momento de comenzar tu primer proyecto!");
        customError.isNotFound = true; // Añadimos una propiedad para identificar este tipo de error
        throw customError;
      }

      // Para otros errores, simplemente los propagamos
      throw error;
    }
  }

  /**
   * Filter projects by various criteria
   * @returns {Promise<Array>} Array of filtered projects
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async filterProyectos(filters = {}) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(API_GESTOR.ProyectosFilterByUsuario(), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: filters,
      });

      if (response.status === 200) {
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error filtering projects:", error);

      // Manejar específicamente el error 404
      if (error.response && error.response.status === 404) {
        // Crear un mensaje personalizado basado en los filtros aplicados
        let message = "No se encontraron proyectos que coincidan con los filtros aplicados";

        // Si hay filtros específicos, podemos personalizarlo aún más
        const filterKeys = Object.keys(filters).filter(key => filters[key]);
        if (filterKeys.length > 0) {
          message += ". Intenta con criterios de búsqueda diferentes.";
        }

        const customError = new Error(message);
        customError.isNotFound = true;
        throw customError;
      }

      // Para otros errores, simplemente los propagamos
      throw error;
    }
  }

  /**
   * Search projects by name or origin
   * @param {string} searchTerm - The search term
   * @returns {Promise<Array>} Array of matching projects
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async buscarProyectos(searchTerm) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(API_GESTOR.ProyectosBuscarByUsuario(searchTerm), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error searching projects:", error);

      // Manejar específicamente el error 404
      if (error.response && error.response.status === 404) {
        const customError = new Error(`No se encontraron proyectos que coincidan con "${searchTerm}"`);
        customError.isNotFound = true;
        throw customError;
      }

      // Para otros errores, simplemente los propagamos
      throw error;
    }
  }

  /**
   * Get all users for the current subscription
   * @returns {Promise<Array>} Array of users
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getUsuarios() {
    try {
      const subscriptionId = this.getSubscriptionId();
      if (!subscriptionId) {
        throw new Error("Subscription ID not found");
      }

      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(API_GESTOR.ObtenerUsuarios(subscriptionId), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      // console.log("ProyectosService getUsuarios response:", response.data);
      if (response.status === 200) {
        // Traer usuario de Proyectus
        const responseProyectus = await axios.get(API_GESTOR.ObtenerUsuariosProyectus(), {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        // console.log("ProyectosService getUsuarios responseProyectus:", responseProyectus.data);

        // Intersectar por el atributo str_Correo
        const users = response.data.filter(user => responseProyectus.data.some(proyectusUser => proyectusUser.str_Correo === user.str_Correo));
        // console.log("ProyectosService getUsuarios users:", users);

        // Return the intersected users
        return users.map(user => ({
            value: user.int_idUsuarios.toString(),
            label: `${user.str_Nombres} ${user.str_Apellidos}`,
            userData: user // Keep the original user data for reference if needed
          }));

        // Format users to include full name for display
        // return response.data.map(user => ({
        //   value: user.int_idUsuarios.toString(),
        //   label: `${user.str_Nombres} ${user.str_Apellidos}`,
        //   userData: user // Keep the original user data for reference if needed
        // }));
      }
      return [];
    } catch (error) {
      console.error("Error fetching users:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error("No se encontraron usuarios disponibles");
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Create a new project
   * @param {Object} projectData - The project data
   * @returns {Promise<Object>} The created project
   * @throws {Error} Custom error with message for errors
   */
  async createProyecto(projectData) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      // Get company ID from cookie
      const companyId = this.getCompanyId();
      if (!companyId) {
        throw new Error("Company ID not found");
      }

      // Add company ID to project data
      const completeProjectData = {
        ...projectData,
        int_idEmpresa: companyId
      };

      // Ensure the data structure matches the required format
      const requiredData = {
        str_nombre: completeProjectData.str_nombre,
        dt_fechaInicio: completeProjectData.dt_fechaInicio,
        dt_fechaFin: completeProjectData.dt_fechaFin,
        int_idUsuarios: completeProjectData.int_idUsuarios,
        int_idEmpresa: completeProjectData.int_idEmpresa,
        // Optional fields with default values
        str_presupuesto: completeProjectData.str_presupuesto || "0"
      };

      const response = await axios.post(API_GESTOR.CrearProyecto(), requiredData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
      });

      if (response.status === 201 || response.status === 200) {
        return response.data;
      }
      throw new Error("Error al crear el proyecto");
    } catch (error) {
      console.error("Error creating project:", error);

      // Handle specific error messages from the backend if available
      if (error.response && error.response.data) {
        throw new Error(error.response.data);
      }

      // For other errors, throw a generic message
      throw new Error("No se pudo crear el proyecto. Por favor, inténtalo de nuevo.");
    }
  }

  /**
   * Get tasks for a specific project
   * @param {string|number} proyectoId - The project ID
   * @returns {Promise<Array>} Array of tasks
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getTareasByProyecto(proyectoId) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(API_GESTOR.TareasByProyecto(proyectoId), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error fetching tasks:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error("No se encontraron tareas para este proyecto");
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Get statistics for a specific project
   * @param {string|number} proyectoId - The project ID
   * @returns {Promise<Object>} Project statistics
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getProyectoEstadisticas(proyectoId) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(API_GESTOR.ProyectoEstadisticas(proyectoId), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        return response.data;
      }
      return { presupuesto: 0, gasto_real: 0 };
    } catch (error) {
      console.error("Error fetching project statistics:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error("No se encontraron estadísticas para este proyecto");
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Get tasks for the current user
   * @returns {Promise<Array>} Array of tasks
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getTareasByUsuario() {
    try {
      const userId = this.getUserId();
      if (!userId) {
        throw new Error("User ID not found");
      }

      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(API_GESTOR.TareasByUsuario(userId), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error fetching tasks for user:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error("No se encontraron tareas para el usuario especificado");
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Filter tasks by various criteria
   * @param {Object} filters - The filter criteria (nombre, fecha_inicio, fecha_fin, prioridad, estado)
   * @returns {Promise<Array>} Array of filtered tasks
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async filterTareas(filters = {}) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      // Get user ID to ensure we only get tasks for the current user
      const userId = this.getUserId();
      if (!userId) {
        throw new Error("User ID not found");
      }

      // Get perfil to ensure we only get tasks for the current user
      const perfil = this.getPerfil();
      if (!perfil) {
        throw new Error("Perfil not found");
      }

      // Add user ID to filters
      const filterParams = {
        ...filters,
        usuario_id_2: userId,
        perfil_2: perfil
      };

      const response = await axios.get(API_GESTOR.TareasFilter(), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        params: filterParams,
      });

      if (response.status === 200) {
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error filtering tasks:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        // Create a custom message based on applied filters
        let message = "No se encontraron tareas que coincidan con los filtros aplicados";

        // If there are specific filters, we can customize the message further
        const filterKeys = Object.keys(filters).filter(key => filters[key]);
        if (filterKeys.length > 0) {
          message += ". Intenta con criterios de búsqueda diferentes.";
        }

        const customError = new Error(message);
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Create a new task
   * @param {Object} taskData - The task data
   * @returns {Promise<Object>} The created task
   * @throws {Error} Custom error with message for errors
   */
  async createTarea(taskData) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      // Create a new object with only the required fields
      const requestData = {
        str_nombre: taskData.str_nombre,
        str_descripcion: taskData.str_descripcion,
        dt_fechaInicio: taskData.dt_fechaInicio,
        dt_fechaFin: taskData.dt_fechaFin,
        int_idUsuarios: taskData.int_idUsuarios,
        int_idProyecto: taskData.int_idProyecto
      };

      // Add optional fields only if they have values
      if (taskData.str_presupuesto && taskData.str_presupuesto !== "0") {
        requestData.str_presupuesto = taskData.str_presupuesto;
      }

      if (taskData.int_idPrioridad) {
        requestData.int_idPrioridad = taskData.int_idPrioridad;
      }

      if (taskData.int_idTareaPadre) {
        requestData.int_idTareaPadre = taskData.int_idTareaPadre;
      }

      const response = await axios.post(API_GESTOR.CrearTarea(), requestData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
      });

      if (response.status === 201 || response.status === 200) {
        return response.data;
      }
      throw new Error("Error al crear la tarea");
    } catch (error) {
      console.error("Error creating task:", error);

      // Handle specific error messages from the backend if available
      if (error.response && error.response.data) {
        throw new Error(error.response.data);
      }

      // For other errors, throw a generic message
      throw new Error("No se pudo crear la tarea. Por favor, inténtalo de nuevo.");
    }
  }

  /**
   * Update a task with progress, description and expense information
   * @param {number|string} tareaId - The task ID to update
   * @param {Object} updateData - The data to update (description, progress, expense)
   * @returns {Promise<Object>} The updated task
   * @throws {Error} Custom error with message for errors
   */
  async updateTarea(tareaId, updateData) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      // Create a request object with the fields to update
      const requestData = {};

      // Add description if provided
      if (updateData.str_descripcion !== undefined) {
        requestData.str_descripcion = updateData.str_descripcion;
      }

      // Add progress if provided (ensure it's in decimal format 0-1)
      if (updateData.db_avance !== undefined) {
        // If db_avance is provided as percentage (0-100), convert to decimal (0-1)
        requestData.db_avance = updateData.db_avance > 1
          ? updateData.db_avance / 100
          : updateData.db_avance;
      }

      // Add expense if provided
      if (updateData.str_gasto !== undefined) {
        requestData.str_gasto = updateData.str_gasto;
      }

      // Add responsible user if provided
      if (updateData.int_idUsuarios !== undefined) {
        requestData.int_idUsuarios = updateData.int_idUsuarios;
      }

      // Add budget if provided
      if (updateData.str_presupuesto !== undefined) {
        requestData.str_presupuesto = updateData.str_presupuesto;
      }

      // Add priority if provided
      if (updateData.int_idPrioridad !== undefined) {
        requestData.int_idPrioridad = updateData.int_idPrioridad;
      }

      // Make the PATCH request
      const response = await axios.patch(API_GESTOR.ActualizarTarea(tareaId), requestData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
      });

      if (response.status === 200) {
        return response.data;
      }
      throw new Error("Error al actualizar la tarea");
    } catch (error) {
      console.error("Error updating task:", error);

      // Handle specific error messages from the backend if available
      if (error.response && error.response.data) {
        throw new Error(error.response.data);
      }

      // For other errors, throw a generic message
      throw new Error("No se pudo actualizar la tarea. Por favor, inténtalo de nuevo.");
    }
  }

  /**
   * Upload evidence file for a task
   * @param {number|string} tareaId - The task ID to associate with the evidence
   * @param {File} file - The file to upload
   * @returns {Promise<Object>} The response from the server
   * @throws {Error} Custom error with message for errors
   */
  async uploadEvidencia(tareaId, file) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      if (!file) {
        throw new Error("No se ha seleccionado ningún archivo");
      }

      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('archivo', file);
      formData.append('int_idTarea', tareaId);

      // Make the POST request
      const response = await axios.post(API_GESTOR.SubirEvidencia(), formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        },
      });

      if (response.status === 201) {
        return response.data;
      }
      throw new Error("Error al subir la evidencia");
    } catch (error) {
      console.error("Error uploading evidence:", error);

      // Handle specific error messages from the backend if available
      if (error.response && error.response.data) {
        throw new Error(error.response.data.error || error.response.data);
      }

      // For other errors, throw a generic message
      throw new Error("No se pudo subir la evidencia. Por favor, inténtalo de nuevo.");
    }
  }

  /**
   * Get all evidences for a specific task
   * @param {number|string} tareaId - The task ID
   * @returns {Promise<Array>} Array of evidences
   * @throws {Error} Custom error with message for 404 or other errors
   */
  async getEvidenciasByTarea(tareaId) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await axios.get(API_GESTOR.ObtenerEvidenciasByTarea(tareaId), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        return response.data;
      }
      return [];
    } catch (error) {
      console.error("Error fetching evidences:", error);

      // Handle 404 error specifically
      if (error.response && error.response.status === 404) {
        const customError = new Error("No se encontraron evidencias para esta tarea");
        customError.isNotFound = true;
        throw customError;
      }

      // For other errors, just propagate them
      throw error;
    }
  }

  /**
   * Finish a task (mark it as completed)
   * @param {number|string} tareaId - The task ID to finish
   * @returns {Promise<Object>} Success message
   * @throws {Error} Custom error with message for 400 or other errors
   */
  async terminarTarea(tareaId) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      // Make the PUT request to finish the task
      const response = await axios.put(API_GESTOR.TerminarTarea(tareaId), {}, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
      });

      if (response.status === 200) {
        return { success: true, message: "Tarea terminada correctamente" };
      }
      throw new Error("Error al terminar la tarea");
    } catch (error) {
      console.error("Error finishing task:", error);

      // Handle specific error messages from the backend
      if (error.response && error.response.status === 400) {
        // Extract the error message from the response
        let errorMessage = "No se pudo terminar la tarea";

        if (error.response.data) {
          try {
            // The error message might be in a specific format like "['Message here']"
            // Try to parse and clean it
            const rawMessage = error.response.data;
            if (typeof rawMessage === 'string' && rawMessage.includes('[') && rawMessage.includes(']')) {
              // Extract the message from between quotes
              const matches = rawMessage.match(/'([^']+)'/);
              if (matches && matches[1]) {
                errorMessage = matches[1];
              } else {
                // If no matches found, use the raw message
                errorMessage = rawMessage;
              }
            } else if (typeof rawMessage === 'string') {
              errorMessage = rawMessage;
            } else if (typeof rawMessage === 'object') {
              errorMessage = JSON.stringify(rawMessage);
            }
          } catch (parseError) {
            console.error("Error parsing error message:", parseError);
            errorMessage = String(error.response.data);
          }
        }

        throw new Error(errorMessage);
      }

      // For other errors, throw a generic message
      throw new Error("No se pudo terminar la tarea. Por favor, inténtalo de nuevo.");
    }
  }

  /**
   * Download an evidence file
   * @param {number|string} evidenciaId - The evidence ID to download
   * @param {string} [nombreEvidencia] - Optional name for the downloaded file
   * @returns {Promise<void>} Promise that resolves when the download starts
   * @throws {Error} Custom error with message for errors
   */
  async descargarEvidencia(evidenciaId, nombreEvidencia = null) {
    try {
      const token = this.getToken();
      if (!token) {
        throw new Error("Authentication token not found");
      }

      // Get the download URL
      const url = API_GESTOR.DescargarEvidencia(evidenciaId);

      // Realizar la solicitud con axios para obtener el archivo con el token en el encabezado
      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        responseType: 'blob', // Importante para manejar archivos binarios
      });

      // Crear un objeto URL para el blob
      const blob = new Blob([response.data]);
      const downloadUrl = window.URL.createObjectURL(blob);

      // Usar el nombre proporcionado o un nombre genérico
      let filename = nombreEvidencia || `evidencia_${evidenciaId}`;

      // Crear un elemento <a> para descargar el archivo
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = filename;
      document.body.appendChild(a);
      a.click();

      // Limpiar
      window.URL.revokeObjectURL(downloadUrl);
      document.body.removeChild(a);

    } catch (error) {
      console.error("Error downloading evidence:", error);

      // Para errores, lanzar un mensaje genérico
      throw new Error("No se pudo descargar la evidencia. Por favor, inténtalo de nuevo.");
    }
  }
}

export default new ProyectosService();
