from src.utils.classes import Response as ResponseAPI
import os

class GestorArchivosController:
    def __init__(self):
        ...

    def save_docs(self, folder, archivo,
                  nombre_archivo_original, nombre_archivo_base, 
                  extension_archivo
                  ):
        """Guarda el archivo en la carpeta correspondiente."""
        try:
            # 1. Si la carpeta no existe, se crea
            if not os.path.exists(folder):
                os.makedirs(folder)
            
            # 2. Define la ruta del archivo
            ruta_file = os.path.join(folder, nombre_archivo_original)

            # 3. Si el archivo ya existe, se renombra con un sufijo "-versionX"
            contador_version = 2
            while os.path.exists(ruta_file):
                nuevo_file = (
                    f"{nombre_archivo_base}-version{contador_version}{extension_archivo}"
                )
                ruta_file = os.path.join(folder, nuevo_file)
                contador_version += 1

            # 4. Guarda el archivo en la carpeta
            with open(ruta_file, "wb+") as destino:
                for chunk in archivo.chunks():
                    destino.write(chunk)

            # 5. Retorna la ruta del archivo
            return ResponseAPI("OK", ruta_file, state=True)
        except Exception as e:
            return ResponseAPI(str(e), e, state=False)
    
    