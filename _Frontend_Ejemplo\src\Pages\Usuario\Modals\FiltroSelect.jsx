import { ChevronDownIcon } from "@heroicons/react/16/solid";
import React, { useState, useRef, useEffect } from "react";
import Portal from "../../../Components/Portal/Portal";

/**
 * Custom Select component specifically designed for FiltroModal
 * This component is a specialized version of the generic Select component
 * with styling and behavior tailored for the FiltroModal context
 */
const FiltroSelect = ({
  options,
  placeholder = "Seleccionar...",
  onChange,
  className = "",
  disabled = false,
  value = null,
}) => {
  // Default colors for FiltroModal context
  const borderColor = "#D1D5DB";
  const textColor = "#6B7280";
  const hoverBgColor = "#F3F4F6";
  const selectedBgColor = "#EFF6FF"; // Light blue background for selected item

  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const selectRef = useRef(null);
  const buttonRef = useRef(null);

  // Calculate dropdown position when opened
  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const dropdownHeight = Math.min(options.length * 40, 240); // Approx height (40px per option, max 240px)

      // Check if there's enough space below
      const spaceBelow = windowHeight - rect.bottom;
      const showBelow = spaceBelow >= dropdownHeight;

      setDropdownPosition({
        top: showBelow
          ? rect.bottom + window.scrollY
          : (rect.top - dropdownHeight) + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width,
      });
    }
  }, [isOpen, options.length]);

  // Close select when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target) &&
        !event.target.closest('.filtro-select-dropdown-menu')
      ) {
        setIsOpen(false);
      }
    };

    // Add event listener when select is open
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    // Clean up event listener
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  // Recalculate position on window resize
  useEffect(() => {
    const handleResize = () => {
      if (isOpen && buttonRef.current) {
        const rect = buttonRef.current.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const dropdownHeight = Math.min(options.length * 40, 240);

        const spaceBelow = windowHeight - rect.bottom;
        const showBelow = spaceBelow >= dropdownHeight;

        setDropdownPosition({
          top: showBelow
            ? rect.bottom + window.scrollY
            : (rect.top - dropdownHeight) + window.scrollY,
          left: rect.left + window.scrollX,
          width: rect.width,
        });
      }
    };

    if (isOpen) {
      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleResize);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleResize);
    };
  }, [isOpen, options.length]);

  // Update internal state when value prop changes
  useEffect(() => {
    if (value !== null) {
      const option = options.find(opt => opt.value === value);
      if (option) {
        setSelectedOption(option);
      }
    } else {
      setSelectedOption(null);
    }
  }, [value, options]);

  const handleSelect = (option) => {
    setSelectedOption(option);
    setIsOpen(false);
    if (onChange) {
      onChange(option);
    }
  };

  // Get color based on priority value
  const getPriorityColor = (value) => {
    switch(value) {
      case "1": return "#EF4444"; // Red for Alta
      case "2": return "#F59E0B"; // Amber for Media
      case "3": return "#10B981"; // Green for Baja
      default: return textColor;
    }
  };

  // Get background color based on priority value
  const getPriorityBgColor = (value) => {
    switch(value) {
      case "1": return "#FEF2F2"; // Light red for Alta
      case "2": return "#FFFBEB"; // Light amber for Media
      case "3": return "#ECFDF5"; // Light green for Baja
      default: return "transparent";
    }
  };

  return (
    <div className={`w-full ${className}`} ref={selectRef}>
      <button
        type="button"
        disabled={disabled}
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className={`
          w-full flex items-center justify-between
          px-4 py-2 text-left
          border rounded-md
          poppins-font
          h-[2.5rem]
          transition-colors
          ${
            disabled
              ? "bg-gray-100 text-gray-500 cursor-not-allowed"
              : "bg-white hover:bg-gray-50"
          }
        `}
        style={{ 
          borderColor: borderColor,
          boxShadow: isOpen ? "0 0 0 2px rgba(209, 213, 219, 0.5)" : "none"
        }}
      >
        <span style={{ 
          color: selectedOption ? getPriorityColor(selectedOption.value) : textColor,
          fontWeight: selectedOption ? 500 : 400
        }}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>

        <ChevronDownIcon
          className={`w-5 h-5 transition-transform duration-200
            ${isOpen ? "rotate-180" : ""}
            ${disabled ? "opacity-50" : ""}`}
          style={{ color: textColor }}
        />
      </button>

      {isOpen && !disabled && (
        <Portal containerId="filtro-select-portal">
          <div
            className="filtro-select-dropdown-menu fixed z-50
            bg-white border rounded-md
            shadow-lg max-h-60 overflow-y-auto"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              width: `${dropdownPosition.width}px`,
              borderColor: borderColor
            }}
          >
            {options.map((option) => (
              <div
                key={option.value}
                onClick={() => handleSelect(option)}
                className={`
                  px-4 py-2
                  cursor-pointer
                  poppins-font
                  transition-colors
                  duration-150
                `}
                style={{
                  color: getPriorityColor(option.value),
                  backgroundColor: option.value === selectedOption?.value 
                    ? getPriorityBgColor(option.value) 
                    : 'transparent',
                  fontWeight: option.value === selectedOption?.value ? 500 : 400
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = option.value === selectedOption?.value 
                  ? getPriorityBgColor(option.value) 
                  : hoverBgColor}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = option.value === selectedOption?.value 
                  ? getPriorityBgColor(option.value) 
                  : 'transparent'}
              >
                {option.label}
              </div>
            ))}
          </div>
        </Portal>
      )}
    </div>
  );
};

export default FiltroSelect;
