# Generated by Django 5.2.1 on 2025-05-14 12:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('agrupadores', '0001_initial'),
        ('empresas', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Cuenta',
            fields=[
                ('int_idCuenta', models.AutoField(primary_key=True, serialize=False)),
                ('str_nombre', models.CharField(max_length=255)),
                ('int_idAgrupador', models.ForeignKey(db_column='int_idAgrupador', on_delete=django.db.models.deletion.CASCADE, related_name='cuentas', to='agrupadores.agrupador')),
                ('int_idCuentaPadre', models.ForeignKey(db_column='int_idCuentaPadre', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='cuentas_hijas', to='cuentas.cuenta')),
                ('int_idEmpresa', models.ForeignKey(db_column='int_idEmpresa', on_delete=django.db.models.deletion.CASCADE, related_name='cuentas', to='empresas.empresa')),
            ],
            options={
                'verbose_name': 'cuenta',
                'verbose_name_plural': 'cuentas',
                'db_table': 'tm_cuentas',
                'managed': True,
            },
        ),
    ]
