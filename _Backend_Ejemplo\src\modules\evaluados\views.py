from django.shortcuts import render
from rest_framework import viewsets
from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
import logging

from src.modules.evaluaciones.models import Evaluacion
from src.modules.detalles_evaluacion.controller import DetalleController
from src.utils.classes import Response as APIResponse
from .models import Evaluado
from .serializers import EvaluadoSerializer
from .controller import EvaluadoController

# Configurar logger
logger = logging.getLogger(__name__)

# Create your views here.

class EvaluadoView(viewsets.ModelViewSet):
    queryset = Evaluado.objects.all()
    serializer_class = EvaluadoSerializer
    permission_classes = [permissions.AllowAny]
    controller = EvaluadoController()
    detalle_controller = DetalleController()
    http_method_names = ["get", "post", "patch", "delete"]

    @swagger_auto_schema(
        operation_description="Actualizar parcialmente un evaluado. Si todos los valores están completos, actualiza el estado a true.",
        responses={
            200: EvaluadoSerializer(),
            400: "Error de validación o datos incompletos",
            404: "Evaluado no encontrado"
        }
    )
    def partial_update(self, request, *args, **kwargs):
        """
        Actualiza parcialmente un evaluado y verifica si todos los valores están completos
        para actualizar su estado a true si es necesario.
        """
        try:
            evaluado_id = kwargs.get('pk')
            logger.info(f"Procesando partial_update para evaluado ID: {evaluado_id}")

            # 1. Actualizar el evaluado con los datos de la solicitud si hay alguno
            resultado_actualizacion = self.controller.actualizar_parcial(
                evaluado_id,
                request.data,
                self.serializer_class
            )

            if not resultado_actualizacion.state:
                if resultado_actualizacion.message == "Evaluado no encontrado":
                    return Response(
                        {"detail": resultado_actualizacion.message},
                        status=status.HTTP_404_NOT_FOUND
                    )
                else:
                    return Response(
                        {"detail": resultado_actualizacion.message},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            # 2. Verificar completitud y actualizar estado usando el controlador
            resultado_verificacion = self.controller.verificar_completitud_y_actualizar_estado(evaluado_id)

            if not resultado_verificacion.state:
                logger.warning(f"Error al verificar completitud: {resultado_verificacion.message}")
                return Response(
                    {"detail": resultado_verificacion.message},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # 3. Preparar respuesta con el evaluado actualizado
            evaluado_actualizado = resultado_verificacion.data.get('evaluado')
            serializer = self.serializer_class(evaluado_actualizado)

            return Response(
                {
                    "detail": resultado_verificacion.message,
                    "evaluado": serializer.data,
                    "todos_completos": resultado_verificacion.data.get('todos_completos'),
                    "detalles_incompletos": resultado_verificacion.data.get('detalles_incompletos')
                },
                status=status.HTTP_200_OK
            )

        except Exception as e:
            logger.error(f"Error en partial_update: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @swagger_auto_schema(
        operation_description="Crear un nuevo Evaluado",
        responses={
            201: EvaluadoSerializer(),
            400: "Error de validación - La fecha de fin debe ser posterior a la fecha de inicio"
        }
    )
    def create(self, request, *args, **kwargs):
        try:
            # --- Validación Previa de Existencia del Evaluado ---
            usuario_id = request.data.get('int_idUsuarios')
            evaluacion_id = request.data.get('int_idEvaluacion')
            tipo_evaluado_id = request.data.get('int_idTipoEvaluado')

            # Validar que los IDs necesarios para la comprobación estén presentes
            if not all([usuario_id, evaluacion_id, tipo_evaluado_id]):
                missing_fields = [
                    field for field, value in {
                        'int_idUsuarios': usuario_id,
                        'int_idEvaluacion': evaluacion_id,
                        'int_idTipoEvaluado': tipo_evaluado_id
                    }.items() if not value
                ]
                return Response(
                    {"detail": f"Faltan los siguientes campos requeridos para la validación: {', '.join(missing_fields)}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Verificar si ya existe un Evaluado con la misma combinación única
            evaluado_existente = Evaluado.objects.filter(
                int_idUsuarios=usuario_id,
                int_idEvaluacion=evaluacion_id,
                int_idTipoEvaluado=tipo_evaluado_id
            ).exists()

            if evaluado_existente:
                return Response(
                    {"detail": "Ya existe un evaluado con la misma combinación de usuario, evaluación y tipo."},
                    status=status.HTTP_409_CONFLICT
                )
            # --- Fin Validación Previa ---

            # Creación del Evaluado
            serializer = self.serializer_class(data=request.data)
            serializer.is_valid(raise_exception=True)
            evaluado = serializer.save() # Guarda el nuevo evaluado si la validación previa pasó

            # Ya que estamos creando un nuevo evaluado
            # Debemos verificar, segun el tipoEvaluado, si el campo bool_tobe o bool_asis de la evaluacion es True
            # para cambiarlo a false, porque al tener un nuevo evaluado significa que la evaluacion tobe o
            # evaluacion asis no esta completa.
            evaluacion = Evaluacion.objects.get(int_idEvaluacion=evaluacion_id)
            if tipo_evaluado_id == 1 and evaluacion.bool_tobe == True: # ToBe
                evaluacion.bool_tobe = False
                evaluacion.save()
            elif tipo_evaluado_id == 2 and evaluacion.bool_asis == True: # AsIs
                evaluacion.bool_asis = False
                evaluacion.save()

            # Crear los detalles de evaluación
            self.detalle_controller.crear_detalles(evaluado)
            return Response(
                self.serializer_class(evaluado).data,
                status=status.HTTP_201_CREATED
            )

        except ValueError as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Endpoint para obtener evaluados por evaluacion",
    )
    @action(detail=False, methods=["get"], url_path="evaluacion/<int:evaluacion_id>")
    def evaluado_by_evaluacion(self, request, evaluacion_id, *args, **kwargs):
        try:
            # serializer = EvaluadoSerializer(data=request.data)
            # serializer.is_valid(raise_exception=True)
            # serializer.validated_data
            response: APIResponse = self.controller.get_by_evaluacion(evaluacion_id)
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(
                data=EvaluadoSerializer(response.data, many=True).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Endpoint para obtener evaluados por usuario",
    )
    @action(detail=False, methods=["get"], url_path="usuario/<int:id_usuario>")
    def evaluado_by_usuario(self, request, id_usuario, *args, **kwargs):
        try:
            response: APIResponse = self.controller.get_by_usuario(id_usuario)
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(
                data=EvaluadoSerializer(response.data, many=True).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(
        operation_description="Eliminar un evaluado y verificar si todos los evaluados del mismo tipo tienen bool_estado=true para actualizar la evaluación",
        responses={
            204: "Evaluado eliminado correctamente",
            404: "Evaluado no encontrado",
            400: "Error al eliminar el evaluado"
        }
    )
    def destroy(self, request, *args, **kwargs):
        """
        Elimina un evaluado y verifica si todos los evaluados del mismo tipo tienen bool_estado=true
        para actualizar el campo bool_tobe o bool_asis de la evaluación según corresponda.
        """
        try:
            evaluado_id = kwargs.get('pk')
            logger.info(f"Procesando destroy para evaluado ID: {evaluado_id}")

            # Eliminar el evaluado y verificar estado de la evaluación
            resultado = self.controller.delete_evaluado(evaluado_id)

            if not resultado.state:
                if resultado.message == "Evaluado no encontrado":
                    return Response(
                        {"detail": resultado.message},
                        status=status.HTTP_404_NOT_FOUND
                    )
                else:
                    return Response(
                        {"detail": resultado.message},
                        status=status.HTTP_400_BAD_REQUEST
                    )

            return Response(
                {"detail": resultado.message},
                status=status.HTTP_204_NO_CONTENT
            )

        except Exception as e:
            logger.error(f"Error en destroy: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )