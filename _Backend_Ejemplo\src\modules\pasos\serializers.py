from rest_framework import serializers

from src.modules.adjuntos.models import Adjunto
from .models import Paso


class PasoSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = Paso
        fields = "__all__"


    def to_representation(self, instance):
        representation = super().to_representation(instance)
        adjunto = Adjunto.objects.filter(int_idPaso_id=instance.int_idPaso)
        representation["bool_adjunto"] = True if adjunto else False
        return representation