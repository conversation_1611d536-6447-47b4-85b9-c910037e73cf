# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('niveles', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Paso',
            fields=[
                ('int_idPaso', models.AutoField(primary_key=True, serialize=False)),
                ('str_descripcion', models.TextField()),
                ('int_idNivel', models.ForeignKey(db_column='int_idNivel', on_delete=django.db.models.deletion.CASCADE, related_name='pasos', to='niveles.nivel')),
            ],
            options={
                'verbose_name': 'pasos',
                'verbose_name_plural': 'pasoss',
                'db_table': 'tr_pasos',
                'managed': True,
            },
        ),
    ]
