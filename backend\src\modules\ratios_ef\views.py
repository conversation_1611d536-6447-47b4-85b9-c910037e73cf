from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from src.modules.ratios_ef.models import RatioEF
from src.modules.ratios_ef.serializers import RatioEFSerializer, RatioEFSimpleSerializer
from src.modules.ratios_ef.controller import RatiosEFController

class RatioEFView(viewsets.ModelViewSet):
    serializer_class = RatioEFSerializer
    queryset = RatioEF.objects.all()
    permission_classes = [permissions.AllowAny]
    controller = RatiosEFController()

    @swagger_auto_schema(
        operation_description="Obtiene los ratios financieros para un estado financiero de simulación específico.",
        manual_parameters=[
            openapi.Parameter(
                name='id_estado_financiero',
                in_=openapi.IN_PATH,
                description='ID del estado financiero de simulación',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
        ],
        responses={
            200: openapi.Response(
                description="Ratios obtenidos exitosamente",
                schema=RatioEFSimpleSerializer(many=True)
            ),
            400: openapi.Response(
                description="Error en la solicitud"
            ),
            404: openapi.Response(
                description="No se encontraron ratios para el estado financiero especificado"
            ),
        },
    )
    @action(detail=False, methods=["get"], url_path="simulacion/estado-financiero/(?P<id_estado_financiero>[^/.]+)")
    def ratios_simulacion(self, request, id_estado_financiero):
        """
        Obtiene los ratios financieros para un estado financiero de simulación específico.
        """
        try:
            # Validar que los parámetros sean válidos
            try:
                id_estado_financiero = int(id_estado_financiero)
            except ValueError:
                return Response(
                    data={"message": "El parámetro id_estado_financiero debe ser un número entero"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Llamar al método del controlador
            response = self.controller.obtener_ratios_simulacion(
                id_estado_financiero=id_estado_financiero
            )

            if not response.state:
                return Response(
                    data={"message": response.message},
                    status=status.HTTP_404_NOT_FOUND
                )

            return Response(
                data=response.data,
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                data={"message": f"Error al procesar la solicitud: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

    @swagger_auto_schema(
        operation_description="Obtiene todos los ratios de una empresa para un periodo específico. La fecha debe estar en formato DD-MM-YYYY",
        manual_parameters=[
            openapi.Parameter(
                name='id_empresa',
                in_=openapi.IN_PATH,
                description='ID de la empresa',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='tipo_periodo',
                in_=openapi.IN_PATH,
                description='Tipo de periodo (1: anual, 2: trimestral, 3: mensual, etc.)',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='fecha_fin',
                in_=openapi.IN_PATH,
                description='Fecha de fin del periodo en formato DD-MM-YYYY (ejemplo: 31-12-2023)',
                type=openapi.TYPE_STRING,
                required=True,
                format='date'
            ),
        ],
        responses={
            200: openapi.Response(
                description="Ratios obtenidos exitosamente",
                schema=RatioEFSimpleSerializer(many=True)
            ),
            400: openapi.Response(
                description="Error en la solicitud o formato de fecha inválido"
            ),
            404: openapi.Response(
                description="No se encontraron ratios para los criterios especificados"
            ),
        },
    )
    @action(detail=False, methods=["get"], url_path="periodo-especifico/empresa/(?P<id_empresa>[^/.]+)/tipo-periodo/(?P<tipo_periodo>[^/.]+)/fecha-fin/(?P<fecha_fin>[^/.]+)")
    def ratios_por_empresa_periodo(self, request, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene todos los ratios de una empresa para un periodo específico.
        La fecha debe estar en formato DD-MM-YYYY (ejemplo: 31-12-2023).
        """
        try:

            # Validar que los parámetros sean válidos
            try:
                id_empresa = int(id_empresa)
                tipo_periodo = int(tipo_periodo) # 1: anual, 2: trimestral, 3: mensual, etc.
            except ValueError:
                return Response(
                    data={"message": "Los parámetros id_empresa y tipo_periodo deben ser números enteros"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Llamar al método del controlador
            response = self.controller.obtener_ratios_por_empresa_periodo(
                id_empresa=id_empresa,
                tipo_periodo=tipo_periodo,
                fecha_fin=fecha_fin
            )

            if not response.state:
                return Response(
                    data={"message": response.message},
                    status=status.HTTP_404_NOT_FOUND
                )

            return Response(
                data=response.data,
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                data={"message": f"Error al procesar la solicitud: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )

    @swagger_auto_schema(
        operation_description="Obtiene los ratios financieros de los últimos 12 periodos para una empresa específica. La fecha debe estar en formato DD-MM-YYYY",
        manual_parameters=[
            openapi.Parameter(
                name='id_empresa',
                in_=openapi.IN_PATH,
                description='ID de la empresa',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='tipo_periodo',
                in_=openapi.IN_PATH,
                description='Tipo de periodo (1: anual, 2: trimestral, 3: mensual, etc.)',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='fecha_fin',
                in_=openapi.IN_PATH,
                description='Fecha de fin del periodo en formato DD-MM-YYYY (ejemplo: 31-12-2023)',
                type=openapi.TYPE_STRING,
                required=True,
                format='date'
            ),
        ],
        responses={
            200: openapi.Response(
                description="Ratios de los últimos periodos obtenidos exitosamente"
            ),
            400: openapi.Response(
                description="Error en la solicitud o formato de fecha inválido"
            ),
            404: openapi.Response(
                description="No se encontraron ratios para los criterios especificados"
            ),
        },
    )
    @action(detail=False, methods=["get"], url_path="periodo-especifico/ultimos-periodos/empresa/(?P<id_empresa>[^/.]+)/tipo-periodo/(?P<tipo_periodo>[^/.]+)/fecha-fin/(?P<fecha_fin>[^/.]+)")
    def ratios_ultimos_periodos(self, request, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene los ratios financieros de los últimos 12 periodos para una empresa específica.
        La fecha debe estar en formato DD-MM-YYYY (ejemplo: 31-12-2023).
        """
        try:
            # Validar que los parámetros sean válidos
            try:
                id_empresa = int(id_empresa)
                tipo_periodo = int(tipo_periodo) # 1: anual, 2: trimestral, 3: mensual, etc.
            except ValueError:
                return Response(
                    data={"message": "Los parámetros id_empresa y tipo_periodo deben ser números enteros"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Llamar al método del controlador
            response = self.controller.obtener_ratios_ultimos_periodos(
                id_empresa=id_empresa,
                tipo_periodo=tipo_periodo,
                fecha_fin=fecha_fin
            )

            if not response.state:
                return Response(
                    data={"message": response.message},
                    status=status.HTTP_404_NOT_FOUND
                )

            return Response(
                data=response.data,
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response(
                data={"message": f"Error al procesar la solicitud: {str(e)}"},
                status=status.HTTP_400_BAD_REQUEST
            )