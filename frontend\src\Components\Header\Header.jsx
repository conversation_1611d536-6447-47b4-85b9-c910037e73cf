import React, { useEffect } from "react";

import { RoutesPrivate } from "../../Routes/ProtectedRoute.tsx";
import { useGoTo } from "../../Services/Globales.jsx";
import FotoPerfil from "../../assets/avatars/300-13.jpg";
import Logo from "../../../public/Balancx_logo.png";
import IconoLogout from "../../assets/SVG/IconoLogout.tsx";
const Header = ({ perfil }) => {
  const { goTo } = useGoTo();
  const menuColor = "#156CFF";
  const Nombres = "José María";
  const Apellidos = "Torres";
  return (
    <header className="w-full py-3 md:py-4 px-2 md:px-10 flex justify-between items-center sticky top-0 z-50 bg-white shadow-sm">
      <img
        src={Logo}
        alt="logo"
        className="h-auto max-w-[6rem] sm:max-w-[8rem] md:max-w-[11rem] cursor-pointer transition-all"
        onClick={() => goTo(RoutesPrivate.INICIO)}
      />
      <div className="flex items-center gap-2 md:gap-4 justify-center">
        <img 
          src={FotoPerfil} 
          alt="Foto de perfil" 
          className="w-8 sm:w-10 md:w-12 rounded-md shadow-sm" 
        />
        <div className="hidden sm:flex flex-col justify-start items-start">
          <span className="text-sm md:text-base font-normal text-[#156CFF]">Hola,</span>
          <span className="text-sm md:text-base font-normal truncate max-w-[120px] md:max-w-full">
            {Nombres} {Apellidos}
          </span>
        </div>
        <button className="cursor-pointer hover:bg-gray-100 p-2 md:p-3 rounded-xl transition-all">
          <IconoLogout size={"1.5rem"} className="sm:size-[1.8rem]" />
        </button>
      </div>
    </header>
  );
};

export default Header;
