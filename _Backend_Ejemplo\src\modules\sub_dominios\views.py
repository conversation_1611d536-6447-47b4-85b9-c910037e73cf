from django.shortcuts import render
from rest_framework import viewsets
from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema

from src.utils.classes import Response as APIResponse
from .models import SubDominio
from .serializers import SubdominioSerializer
from .controller import SubdominioController
# Create your views here.

class SubdominioView(viewsets.ModelViewSet):
    queryset = SubDominio.objects.all()
    serializer_class = SubdominioSerializer
    permission_classes = [permissions.AllowAny]
    controller = SubdominioController()
    http_method_names = ["get", "post", "patch", "delete"]

    @swagger_auto_schema(
        operation_description="Endpoint para obtener subdominios por dominio",
    )
    @action(detail=False, methods=["get"], url_path="dominio/<int:dominio_id>")
    def subdominio_by_dominio(self, request, dominio_id, *args, **kwargs):
        try:
            # serializer = SubdominioSerializer(data=request.data)
            # serializer.is_valid(raise_exception=True)
            # serializer.validated_data
            response: APIResponse = self.controller.get_by_dominio(dominio_id)
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(
                data=SubdominioSerializer(response.data, many=True).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)