from rest_framework import serializers
from .models import Evaluado
from src.modules.usuarios.models import Usuario

class UsuarioReducidoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Usuario
        fields = ['int_idUsuarios', 'str_Nombres', 'str_Apellidos']

class EvaluadoReducidoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Evaluado
        fields = ['int_idEvaluado', 'int_idTipoEvaluado', 'bool_estado']

class EvaluadoReducidoConAvanceSerializer(serializers.ModelSerializer):
    avance = serializers.IntegerField()

    class Meta:
        model = Evaluado
        fields = ['int_idEvaluado', 'int_idTipoEvaluado', 'bool_estado', 'avance']

class EvaluadoSerializer(serializers.ModelSerializer):

    class Meta:
        model = Evaluado
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["int_idUsuarios"] = UsuarioReducidoSerializer(
            instance.int_idUsuarios
        ).data

        return representation

    # comentario