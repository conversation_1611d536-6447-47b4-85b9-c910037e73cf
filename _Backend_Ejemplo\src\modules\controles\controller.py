from src.utils.classes import Response
from .models import Control


class ControlController:
    def get_by_sub_dominio(self, sub_dominio_id: int):
        try:
            controles = Control.objects.filter(int_idSubDominio_id=sub_dominio_id).all()
            if not controles:
                return Response("No se encontraron controles", state=False)
            return Response(data= controles, state=True)
        except Exception as e:
            return Response(str(e), e)

    