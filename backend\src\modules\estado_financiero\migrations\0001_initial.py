# Generated by Django 5.2.1 on 2025-05-14 12:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('empresas', '0001_initial'),
        ('usuarios', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='EstadoFinanciero',
            fields=[
                ('int_idEstadoFinanciero', models.AutoField(primary_key=True, serialize=False)),
                ('str_nombre', models.CharField(max_length=255)),
                ('dt_fechaRegistro', models.DateTimeField()),
                ('int_tipoRegistro', models.IntegerField()),
                ('int_referenciaSimulacion', models.IntegerField(null=True)),
                ('int_idEmpresa', models.ForeignKey(db_column='int_idEmpresa', on_delete=django.db.models.deletion.CASCADE, related_name='estados_financieros', to='empresas.empresa')),
                ('int_idUsuarios', models.ForeignKey(db_column='int_idUsuarios', on_delete=django.db.models.deletion.CASCADE, related_name='estados_financieros', to='usuarios.usuario')),
            ],
            options={
                'verbose_name': 'estado financiero',
                'verbose_name_plural': 'estados financieros',
                'db_table': 'tr_estadosfinancieros',
                'managed': True,
            },
        ),
    ]
