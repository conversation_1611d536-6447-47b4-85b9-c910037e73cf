# Generated by Django 5.2.1 on 2025-05-14 12:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cuentas', '0001_initial'),
        ('estado_financiero', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='EFCuenta',
            fields=[
                ('int_idValor', models.AutoField(primary_key=True, serialize=False)),
                ('db_valor', models.DecimalField(decimal_places=2, max_digits=18)),
                ('int_idCuenta', models.ForeignKey(db_column='int_idCuenta', on_delete=django.db.models.deletion.CASCADE, related_name='valores', to='cuentas.cuenta')),
                ('int_idEstadoFinanciero', models.ForeignKey(db_column='int_idEstadoFinanciero', on_delete=django.db.models.deletion.CASCADE, related_name='valores', to='estado_financiero.estadofinanciero')),
            ],
            options={
                'verbose_name': 'ef cuenta',
                'verbose_name_plural': 'ef cuentas',
                'db_table': 'tr_ef_cuentas',
                'managed': True,
            },
        ),
    ]
