import os
from django.shortcuts import render
from django.db.models import Q
from rest_framework import viewsets
from rest_framework import status, permissions
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.decorators import api_view
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema

from src.utils.classes import Response as APIResponse
from src.modules.categorias import serializers
from .models import DetalleEvaluacion
from .serializers import DetallesEvaluacionSerializer
from .controller import DetalleController

from drf_yasg import openapi


class DetallesView(viewsets.ModelViewSet):
    queryset = DetalleEvaluacion.objects.all()
    serializer_class = DetallesEvaluacionSerializer
    permission_classes = [permissions.AllowAny]
    controller = DetalleController()
    http_method_names = ["get", "post", "patch"]
    
    # @swagger_auto_schema(
    #     operation_description="Endpoint para cambiar el estado del detalle de una evaluacion.",
    #     request_body=None,  # No se necesita cuerpo en la petición
    #     responses={
    #         200: openapi.Response("Estado cambiado con éxito."),
    #         400: openapi.Response("Error al cambiar el estado."),
    #         404: openapi.Response("Detalle de evaluacion no encontrado."),
    #     },
    # )
    # @action(detail=False, methods=["patch"], url_path="estado/update/<int:id>")
    # def cambiar_estado_detalle(self, request, id):
    #     """
    #     Acción para cambiar el estado booleano (bool_estado) de un detalle.
    #     """
    #     try:
    #         # El pk viene de la URL (detail=True)
    #         response: APIResponse = self.controller.cambiar_estado(detalle_id=id)
    #         if not response.state:
    #             return Response(
    #                 data=response.message, status=status.HTTP_400_BAD_REQUEST
    #             )

    #         # Si el estado se cambió correctamente
    #         return Response(data=response.message, status=status.HTTP_200_OK)

    #     except ValueError:  # Si pk no es un entero válido
    #         return Response(
    #             data="ID de Detalle inválido.", status=status.HTTP_400_BAD_REQUEST
    #         )
    #     except Exception as e:
    #         # Captura general para otros errores inesperados
    #         # Considerar registrar el error aquí
    #         return Response(
    #             data=f"Error inesperado: {str(e)}",
    #             status=status.HTTP_500_INTERNAL_SERVER_ERROR,
    #         )
    
    @swagger_auto_schema(
        operation_description="Endpoint para obtener detalles de evaluacion por subdominio",
        responses={200: DetallesEvaluacionSerializer(many=True)},
    )
    @action(detail=False, methods=["get"], url_path="subdominio/<int:subdominio_id>")
    def detalle_by_subdominio(self, request, subdominio_id, *args, **kwargs):
        """
        Obtiene los detalles de evaluacion por subdominio
        """
        try:
            response: APIResponse = self.controller.get_by_subdominio(subdominio_id)
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )
            # response.data ya contiene los datos serializados por el controlador
            return Response(
                data=response.data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)
    

    @swagger_auto_schema(
        operation_description="Endpoint para actualizar parcialmente un detalle de evaluacion",
        responses={200: DetallesEvaluacionSerializer()},
    )
    def partial_update(self, request, *args, **kwargs):
        """
        Endpoint para actualizar parcialmente un detalle de evaluacion.
        1. Verificar el tipo de evaluado
        2. Llenar el campo str_valor_tobe o str_valor_asis segun corresponda
        """
        try:
            id_detalle = kwargs.get("pk")
            detalle = DetalleEvaluacion.objects.get(int_idDetalleEvaluacion=id_detalle)
            tipo_evaluado = detalle.int_idEvaluado.int_idTipoEvaluado # 1:ToBe, 2:AsIs
            print(tipo_evaluado)
            if tipo_evaluado == 1:
                detalle.str_valor_tobe = request.data["str_valor_tobe"]
            elif tipo_evaluado == 2:
                detalle.str_valor_asis = request.data["str_valor_asis"]
            detalle.save()
            serializer = self.serializer_class(detalle)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)