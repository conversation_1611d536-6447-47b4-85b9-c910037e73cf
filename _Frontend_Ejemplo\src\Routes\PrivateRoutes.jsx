import { Navigate, Route, Routes } from "react-router-dom";
import { RoutesPrivate } from "./ProtectedRoute";
  import Template from "../Components/Template/Template";
import Inicio from "../Pages/Administrador/Inicio/Inicio";
import Tareas from "../Pages/Administrador/Tareas/Tareas";
import InicioUsuario from "../Pages/Usuario/IndexUsuario";
 
const PrivateRoutes = ({ perfil }) => {
  return (
    <>
      {perfil === "Administrador" ? (
        <Template perfil={perfil}>
          <Routes>
            <>
              <Route path="/*" element={<Navigate to="/Proyectus/Inicio" />} />
              <Route
                path={RoutesPrivate.INICIO}
                element={<Inicio />}
              />
              <Route
                path={RoutesPrivate.TAREAS}
                element={<Tareas />}
              />
               
            </>
          </Routes>
        </Template>
      ) :perfil === "Usuario" ? (
        <Template perfil={perfil}>
        <Routes>
          <>
            <Route path="/*" element={<Navigate to="/Proyectus/Inicio" />} />
            <Route
                path={RoutesPrivate.INICIO}
                element={<InicioUsuario />}
              />
          </>
        </Routes>
      </Template>
      ):""}
    </>
  );
};

export { PrivateRoutes };
