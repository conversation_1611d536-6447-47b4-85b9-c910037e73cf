import React, { useState, useEffect } from "react";
import Modal from "../../../Components/Modal/Modal";
import Input from "../../../Components/Input/Input";
import Select from "react-select";
import Button from "../../../Components/Button/Button";
import ProyectosService from "../../../Services/ProyectosService";

const CrearProyectoModal = ({ isOpen, onClose }) => {
  // Form state
  const [nombre, setNombre] = useState("");
  const [responsableId, setResponsableId] = useState("");
  const [fechaInicio, setFechaInicio] = useState("");
  const [fechaFin, setFechaFin] = useState("");
  const [presupuesto, setPresupuesto] = useState("");

  // Users for the dropdown
  const [usuarios, setUsuarios] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Form validation
  const [formErrors, setFormErrors] = useState({});

  // Fetch users when the modal opens
  useEffect(() => {
    if (isOpen) {
      fetchUsuarios();
    }
  }, [isOpen]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);

  const resetForm = () => {
    setNombre("");
    setResponsableId("");
    setFechaInicio("");
    setFechaFin("");
    setPresupuesto("");
    setFormErrors({});
  };

  const fetchUsuarios = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await ProyectosService.getUsuarios();
      setUsuarios(data);
    } catch (err) {
      console.error("Error fetching users:", err);
      setError(err.message || "Error al cargar los usuarios");
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!nombre.trim()) {
      errors.nombre = "El nombre es requerido";
    }

    if (!responsableId) {
      errors.responsableId = "El responsable es requerido";
    }

    if (!fechaInicio) {
      errors.fechaInicio = "La fecha de inicio es requerida";
    }

    if (!fechaFin) {
      errors.fechaFin = "La fecha de fin es requerida";
    } else if (fechaInicio && fechaFin && new Date(fechaFin) < new Date(fechaInicio)) {
      errors.fechaFin = "La fecha de fin debe ser posterior a la fecha de inicio";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Prepare project data with the required structure
      const projectData = {
        str_nombre: nombre,
        int_idUsuarios: parseInt(responsableId),
        dt_fechaInicio: fechaInicio,
        dt_fechaFin: fechaFin,
        str_presupuesto: presupuesto || "0"
        // Note: int_idEmpresa will be added from the busProyectus cookie in the service
      };

      await ProyectosService.createProyecto(projectData);

      // Close modal and reset form on success
      onClose();
    } catch (err) {
      console.error("Error creating project:", err);
      setError(err.message || "Error al crear el proyecto");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} titulo={"Nuevo Proyecto"}>
      <div className="w-full max-w-full md:max-w-4xl lg:max-w-6xl bg-white p-2 sm:p-4">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mx-2 sm:mx-4 md:mx-8 mb-4">
            {error}
          </div>
        )}

        {/* Nombre y Responsable */}
        <div className="flex flex-col md:flex-row w-full px-2 sm:px-4 md:px-8 py-2 gap-4 md:gap-6">
          {/* Nombre */}
          <div className="flex-row w-full space-y-2">
            <div className="poppins-font-600 text-[#272727]">
              Nombre <span className="text-red-500 ml-1">*</span>
            </div>
            <Input
              placeholder="Nombre del Proyecto"
              className={`text-[#91A8E2] border-[#7BACFF] border-1 w-full h-[2.5rem] rounded-[0.5rem] px-4 poppins-font ${
                formErrors.nombre ? "border-red-500" : ""
              }`}
              value={nombre}
              onChange={(e) => setNombre(e.target.value)}
            />
            {formErrors.nombre && (
              <div className="text-red-500 text-sm">{formErrors.nombre}</div>
            )}
          </div>

          {/* Responsable */}
          <div className="flex-row w-full space-y-2">
            <div className="poppins-font-600 text-[#272727]">
              Responsable <span className="text-red-500 ml-1">*</span>
            </div>
            <Select
              placeholder={loading ? "Cargando usuarios..." : "Seleccione un nombre"}
              className={`${formErrors.responsableId ? "border-red-500" : ""}`}
              classNamePrefix="react-select"
              options={usuarios}
              isDisabled={loading}
              onChange={(option) => setResponsableId(option.value)}
              styles={{
                control: (baseStyles) => ({
                  ...baseStyles,
                  borderColor: '#7BACFF',
                  borderRadius: '0.5rem',
                  minHeight: '2.5rem',
                  height: '2.5rem',
                  boxShadow: 'none',
                }),
                valueContainer: (baseStyles) => ({
                  ...baseStyles,
                  padding: '0 8px',
                  height: '2.5rem',
                }),
                input: (baseStyles) => ({
                  ...baseStyles,
                  margin: '0',
                  padding: '0',
                }),
                indicatorsContainer: (baseStyles) => ({
                  ...baseStyles,
                  height: '2.5rem',
                }),
                placeholder: (baseStyles) => ({
                  ...baseStyles,
                  color: '#91A8E2',
                }),
                option: (baseStyles, { isFocused }) => ({
                  ...baseStyles,
                  backgroundColor: isFocused ? '#F8FAFB' : 'transparent',
                  color: '#91A8E2',
                }),
                menu: (baseStyles) => ({
                  ...baseStyles,
                  zIndex: 9999,
                }),
              }}
            />
            {formErrors.responsableId && (
              <div className="text-red-500 text-sm">{formErrors.responsableId}</div>
            )}
          </div>
        </div>

        {/* Fechas y Presupuesto */}
        <div className="flex flex-col w-full px-2 sm:px-4 md:px-8 py-2 sm:py-4 md:py-6">
          <div className="flex flex-col sm:flex-row w-full gap-4 sm:gap-6">
            {/* Fecha de Inicio */}
            <div className="space-y-2 w-full sm:w-1/3">
              <div className="flex flex-wrap poppins-font-600 text-[#272727]">
                Fecha esperada de Inicio
                <span className="text-red-500 ml-1">*</span>
              </div>
              <Input
                disabled={false}
                placeholder="Seleccione una fecha"
                type={"date"}
                className={`text-[#91A8E2] border-[#7BACFF] border-1 w-full h-[2.5rem] rounded-[0.5rem] px-4 poppins-font ${
                  formErrors.fechaInicio ? "border-red-500" : ""
                }`}
                value={fechaInicio}
                onChange={(e) => setFechaInicio(e.target.value)}
              />
              {formErrors.fechaInicio && (
                <div className="text-red-500 text-sm">{formErrors.fechaInicio}</div>
              )}
            </div>

            {/* Fecha de Fin */}
            <div className="space-y-2 w-full sm:w-1/3">
              <div className="flex flex-wrap poppins-font-600 text-[#272727]">
                Fecha esperada de Fin
                <span className="text-red-500 ml-1">*</span>
              </div>
              <Input
                disabled={false}
                placeholder="Seleccione una fecha"
                type={"date"}
                className={`text-[#91A8E2] border-[#7BACFF] border-1 w-full h-[2.5rem] rounded-[0.5rem] px-4 poppins-font ${
                  formErrors.fechaFin ? "border-red-500" : ""
                }`}
                value={fechaFin}
                onChange={(e) => setFechaFin(e.target.value)}
              />
              {formErrors.fechaFin && (
                <div className="text-red-500 text-sm">{formErrors.fechaFin}</div>
              )}
            </div>

            {/* Presupuesto */}
            <div className="space-y-2 w-full sm:w-1/3">
              <div className="poppins-font-600 text-[#272727]">Presupuesto</div>
              <Input
                placeholder="Presupuesto de proyecto"
                type={"number"}
                className="text-[#91A8E2] border-[#7BACFF] border-1 w-full h-[2.5rem] rounded-[0.5rem] px-4 poppins-font"
                value={presupuesto}
                onChange={(e) => setPresupuesto(e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Botones */}
        <div className="flex w-full px-2 sm:px-4 md:px-8 py-4 sm:py-6 justify-end">
          <div className="flex gap-3 sm:gap-5">
            <Button
              text_button="Cancelar"
              className="cursor-pointer bg-[#E9E9E9] poppins-font rounded-lg p-2 w-[5rem] sm:w-[6rem] md:w-[7.1875rem]"
              accion={onClose}
              disabled={loading}
            />
            <Button
              text_button={loading ? "Creando..." : "Crear"}
              className="cursor-pointer bg-[#1890FF] poppins-font-600 text-white rounded-lg p-2 w-[5rem] sm:w-[6rem] md:w-[7.1875rem]"
              accion={handleSubmit}
              disabled={loading}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default CrearProyectoModal;