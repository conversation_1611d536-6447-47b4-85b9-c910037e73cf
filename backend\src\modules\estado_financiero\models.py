from django.db import models
from src.modules.empresas.models import Empresa
from src.modules.usuarios.models import Usuario

class EstadoFinanciero(models.Model):
    class Meta:
        db_table = 'tr_estadosfinancieros'
        managed = True
        verbose_name = 'estado financiero'
        verbose_name_plural = 'estados financieros'

    # Opciones para el tipo de periodo
    PERIODO_CHOICES = [
        (1, 'Anual'),
        (2, 'Trimestral'),
        (3, 'Mensual'),
        (4, 'Semanal'),
        (5, 'Diario'),
    ]

    # Opciones para el tipo de estado financiero
    ESTADO_FINANCIERO_CHOICES = [
        (1, 'Estado de Situación Financiera'),
        (2, 'Estado de Resultados'),
    ]

    int_idEstadoFinanciero = models.AutoField(primary_key=True)
    str_nombre = models.CharField(max_length=255)
    dt_fechaRegistro = models.DateTimeField()
    int_idEmpresa = models.ForeignKey(Empresa, on_delete=models.CASCADE, db_column='int_idEmpresa', related_name='estados_financieros')
    int_idUsuarios = models.ForeignKey(Usuario, on_delete=models.CASCADE, db_column='int_idUsuarios', related_name='estados_financieros')
    int_tipoRegistro = models.IntegerField() # 0: periodo contable, 1: simulacion,
    int_referenciaSimulacion = models.ForeignKey('self', on_delete=models.CASCADE, null=True, db_column='int_referenciaSimulacion', related_name='simulaciones')
    # Nuevos campos para periodo
    int_tipoPeriodo = models.IntegerField(choices=PERIODO_CHOICES, default=3) # 1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario
    # Tipo de Estado Financiero
    int_tipoEstadoFinanciero = models.IntegerField(choices=ESTADO_FINANCIERO_CHOICES, default=1) # 1: Estado de Situación Financiera, 2: Estado de Resultados
    dt_fechaInicioPeriodo = models.DateField(null=True)
    dt_fechaFinPeriodo = models.DateField(null=True)

    def __str__(self):
        return self.str_nombre