import React from "react";

const getColorByEstado = (estado) => {
  switch (estado) {
    case "Nuevo":
      return "#D9D9D9"; 
    case "Asignado":
      return "#A6E0FF"; 
    case "En Proceso":
      return "#EFF928"; 
    case "En Validación":
      return "#FFD071"; 
    case "En Aprobación":
      return "#85EE85";
    case "Aprobado":
      return "#0E880E";
    case "Aceptado":
      return "#156CFF";
    case "Firmado":
      return "#156CFF";
      case "Atrasado":
        return "#e6483d";
    default:
      return "#000"; 
  }
};

const CircleEstado = ({ estado }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="0.875rem" height="0.875rem" style={{right: 0,}}>
    <path
      d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512z"
      fill={getColorByEstado(estado)}
    />
  </svg>
);

export default CircleEstado;