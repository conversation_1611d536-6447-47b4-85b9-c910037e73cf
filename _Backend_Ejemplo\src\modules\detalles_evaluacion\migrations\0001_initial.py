# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('controles', '0001_initial'),
        ('evaluaciones', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DetalleEvaluacion',
            fields=[
                ('int_idDetalleEvaluacion', models.AutoField(primary_key=True, serialize=False)),
                ('str_valor', models.CharField(max_length=3)),
                ('int_tipo', models.SmallIntegerField()),
                ('bool_estado', models.BooleanField()),
                ('int_idControl', models.ForeignKey(db_column='int_idControl', on_delete=django.db.models.deletion.CASCADE, related_name='control', to='controles.control')),
                ('int_idEvaluacion', models.ForeignKey(db_column='int_idEvaluacion', on_delete=django.db.models.deletion.CASCADE, related_name='evaluacion', to='evaluaciones.evaluacion')),
            ],
            options={
                'verbose_name': 'detalle_evaluacion',
                'verbose_name_plural': 'detalle_evaluaciones',
                'db_table': 'tr_detalles_evaluacion',
                'managed': True,
            },
        ),
    ]
