# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('sub_dominios', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Control',
            fields=[
                ('int_idControl', models.AutoField(primary_key=True, serialize=False)),
                ('str_descripcion', models.CharField(max_length=255)),
                ('str_valorIndustria', models.Char<PERSON>ield(max_length=3)),
                ('int_idSubDominio', models.ForeignKey(db_column='int_idSubDominio', on_delete=django.db.models.deletion.CASCADE, related_name='controles', to='sub_dominios.subdominio')),
            ],
            options={
                'verbose_name': 'control',
                'verbose_name_plural': 'controles',
                'db_table': 'tr_controles',
                'managed': True,
            },
        ),
    ]
