from django.db import models

from src.modules.suscripciones.models import Suscripcion

# Create your models here.
class Empresa(models.Model):
    class Meta:
        db_table = 'tc_empresas'
        managed = True
        verbose_name = 'empresa'
        verbose_name_plural = 'empresas'
    
    int_idEmpresa = models.AutoField(primary_key=True)
    str_NombreEmpresa = models.CharField(max_length=255)
    str_RazonSocial = models.CharField(max_length=255)
    str_Ruc = models.CharField(max_length=25)
    dt_FechaCreacion = models.DateTimeField()
    dt_FechaModificacion = models.DateTimeField(null=True)
    int_idUsuarioCreacion = models.IntegerField()
    int_idUsuarioModificacion = models.IntegerField(null=True)
    str_idSuscripcion = models.ForeignKey(Suscripcion, on_delete=models.CASCADE, db_column='str_idSuscripcion', related_name="empresas")
    str_Pais = models.CharField(max_length=100)
    str_Moneda = models.CharField(max_length=50)
    str_SimboloMoneda = models.CharField(max_length=10)
    