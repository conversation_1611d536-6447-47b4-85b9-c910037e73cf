import React, { useState, useEffect, useRef } from "react";
import Modal from "../../../Components/Modal/Modal";
import Button from "../../../Components/Button/Button";
import { DocumentIcon } from "@heroicons/react/24/solid";
import { EnvelopeIcon } from "@heroicons/react/24/solid";
import { PhoneIcon } from "@heroicons/react/16/solid";
import Card from "../../../Components/Card/Card";
import ProgressBar from "../../../Components/ProgressBar/ProgressBar";
import ProyectosService from "../../../Services/ProyectosService";
import EvidenciasModal from "./EvidenciasModal";

const TareaModal = ({ isOpen, handleCloseModal, editData }) => {
  const [evidencias, setEvidencias] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showEvidenciasModal, setShowEvidenciasModal] = useState(false);
  const [modalPosition, setModalPosition] = useState(null);
  const evidenciasColumnRef = useRef(null);

  useEffect(() => {
    if (isOpen && editData?.id) {
      fetchEvidencias();
    }
  }, [isOpen, editData]);

  const fetchEvidencias = async () => {
    if (!editData?.id) return;

    setLoading(true);
    setError(null);

    try {
      const data = await ProyectosService.getEvidenciasByTarea(editData.id);
      setEvidencias(data);
    } catch (err) {
      console.error("Error fetching evidences:", err);
      setError(err.message || "Error al cargar las evidencias");
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadEvidencia = async (evidenciaId, nombreEvidencia) => {
    try {
      setLoading(true);
      await ProyectosService.descargarEvidencia(evidenciaId, nombreEvidencia);
      setError(null);
    } catch (err) {
      console.error("Error downloading evidence:", err);
      setError(err.message || "Error al descargar la evidencia");
    } finally {
      setLoading(false);
    }
  };

  const handleCloseEvidenciasModal = () => {
    setShowEvidenciasModal(false);
  };

  return (
    <>
      <Modal isOpen={isOpen} onClose={handleCloseModal}>
        <div className="p-4 md:p-6 w-full max-w-[77.5625rem]">
        {/* Cards superiores */}
        <div className="flex flex-col md:flex-row gap-4">
          <Card
            className="w-full md:w-[36.5rem] h-auto md:h-[7.75rem]"
            bg_color="F8FAFB"
            padding="p-4"
          >
            <div className="flex flex-col justify-between h-full">
              <div className="space-y-4">
                <div
                  className={`flex items-center text-white justify-center w-[7.375rem] h-[1.9375rem] poppins-font-500 p-2 rounded-[0.5rem] ${
                    editData?.estado === 2
                      ? "bg-[#FFA20E]"
                      : editData?.estado === 3
                      ? "bg-[#1890FF]"
                      : "bg-[#47D691]"
                  }`}
                >
                  {editData?.estado === 2
                    ? "En Proceso"
                    : editData?.estado === 3
                    ? "Nuevo"
                    : "Terminado"}
                </div>
                <div className="poppins-font-600 pb-[2.125rem]">
                  {editData?.proyecto?.nombre}
                </div>
              </div>
            </div>
          </Card>
          <Card
            bg_color="#F8FAFB"
            shadow="none"
            className="border-1 border-[#D0D5DD] w-full md:w-[35rem] h-auto md:h-[7.75rem]"
            padding="p-0"
          >
            <div className="flex flex-col md:flex-row justify-between h-full">
              <div className="flex-row gap-2 text-[#272727] py-5 px-4 md:px-10">
                <div className="poppins-font-500 text-[1rem]">
                  {editData?.responsable?.nombre}
                </div>
                <div className="text-[0.9rem]">Desarrollo</div>
                <div className="text-[0.9rem] flex gap-2 items-center">
                  <EnvelopeIcon className="size-4" /> {editData?.responsable?.correo}
                </div>
                <div className="text-[0.9rem] flex gap-2 items-center">
                  <PhoneIcon className="size-4" />
                  +51 999 999 999
                </div>
              </div>
              <div className="bg-[#F8FAFB]  border-[#D0D5DD] flex items-center justify-center w-full md:w-[9.5625rem] h-full rounded-r-[0.75rem]">
                <img
                  className="rounded-full w-[6.125rem] h-[6.125rem] my-4 md:my-0"
                  src={editData?.responsable?.foto}
                  alt="Foto de perfil"
                />
              </div>
            </div>
          </Card>
        </div>

        {/* Título de la tarea */}
        <div className="py-4 flex-row">
          <div className="poppins-font-italic text-[1.25rem]">Tarea</div>
          <div className="poppins-font-600 text-[1.5rem]">
            {editData?.nombre}
          </div>
        </div>

        {/* Tabla */}
        <div className="border-1 border-[#D0D5DD] rounded-[0.75rem] poppins-font overflow-x-auto mt-4">
          <table className="w-full min-w-[640px]">
            <thead>
              <tr>
                <th className="px-4 py-3 bg-[#FBFCFF] text-[#272727] text-sm font-semibold border-r-1 border-[#f9f0f0] text-left">
                  Fecha de Inicio
                </th>
                <th className="px-4 py-3 bg-[#FBFCFF] text-[#272727] text-sm font-semibold border-r-1 border-[#f9f0f0] text-left">
                  Fecha de Fin
                </th>
                <th className="px-4 py-3 bg-[#FBFCFF] text-[#272727] text-sm font-semibold border-r-1 border-[#f9f0f0] text-left">
                  Avance
                </th>
                <th className="px-4 py-3 bg-[#FBFCFF] text-[#272727] text-sm font-semibold border-r-1 border-[#f9f0f0] text-left">
                  Comentario
                </th>
                <th className="px-4 py-3 bg-[#FBFCFF] text-[#272727] text-sm font-semibold border-r-1 border-[#f9f0f0] text-left">
                  Evidencias
                </th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-y-1 border-[#D0D5DD]">
                <td className="px-4 py-3 text-sm border-r-1 border-[#f9f0f0]">
                  {editData?.inicio_real || "-"}
                </td>
                <td className="px-4 py-3 text-sm border-r-1 border-[#f9f0f0]">
                  {editData?.fin_real || "-"}
                </td>
                <td className="px-4 py-3 text-sm border-r-1 border-[#f9f0f0]">
                  <div className="flex items-center gap-2">
                    <div className="w-ful">
                      <ProgressBar
                        percentage={editData?.avance ? Math.round(editData.avance * 100) : 0}
                        color="green"
                      />
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3 text-sm border-r-1 border-[#f9f0f0]">
                  <div className="max-w-xs">
                    {editData?.descripcion || "-"}
                  </div>
                </td>
                <td className="px-4 py-3 text-sm border-r-1 border-[#f9f0f0]" ref={evidenciasColumnRef}>
                  <div className="flex items-center gap-2">
                    {loading ? (
                      <div>Cargando...</div>
                    ) : error ? (
                      <div className="text-red-500">{error}</div>
                    ) : evidencias.length === 0 ? (
                      <div>No hay evidencias</div>
                    ) : evidencias.length === 1 ? (
                      <a
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          handleDownloadEvidencia(evidencias[0].int_idEvidencia, evidencias[0].str_nombre);
                        }}
                        className="text-[#1890FF] hover:underline flex items-center gap-1"
                      >
                        <DocumentIcon className="h-4 w-4" />
                        {evidencias[0].str_nombre}
                      </a>
                    ) : (
                      <a
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          // Calculate position for the modal
                          if (evidenciasColumnRef.current) {
                            const rect = evidenciasColumnRef.current.getBoundingClientRect();
                            setModalPosition({
                              top: rect.top - 220, // Position above the column, adjusted for new size
                              left: rect.left - 150, // Position to the left of the column, adjusted for new size
                            });
                          }
                          setShowEvidenciasModal(true);
                        }}
                        className="text-[#1890FF] hover:underline flex items-center gap-1"
                      >
                        <DocumentIcon className="h-4 w-4" />
                        Ver {evidencias.length} evidencias
                      </a>
                    )}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Botón de cerrar */}
        <div className="flex justify-end mt-6">
          <Button
            text_button="Cerrar"
            className="cursor-pointer bg-[#E9E9E9] poppins-font rounded-lg p-2 w-full md:w-[7.1875rem]"
            accion={handleCloseModal}
          />
        </div>
      </div>
    </Modal>

    {/* Modal para mostrar múltiples evidencias */}
    <EvidenciasModal
      isOpen={showEvidenciasModal}
      onClose={handleCloseEvidenciasModal}
      evidencias={evidencias}
      onDownload={handleDownloadEvidencia}
      loading={loading}
      position={modalPosition}
    />
    </>
  );
};

export default TareaModal;
