import React from "react";
import { Tooltip } from "@mui/material";

const IconoDocFirmado = ({onClick}) => (
    <Tooltip title="Contrato Subido" placement="top">

  <svg
    className="bi bi-file-earmark-check-fill"
    fill="currentColor"
    height="1.3rem"
    viewBox="0 0 16 16"
    width="1.3rem"
    xmlns="http://www.w3.org/2000/svg"
    onClick={onClick}
    style={{cursor:'pointer'}}

  >
    <path d="M9.293 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.707A1 1 0 0 0 13.707 4L10 .293A1 1 0 0 0 9.293 0zM9.5 3.5v-2l3 3h-2a1 1 0 0 1-1-1zm1.354 4.354-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 1 1 .708-.708L7.5 9.793l2.646-2.647a.5.5 0 0 1 .708.708z" fill="green"/>
  </svg>
  </Tooltip>

);

export default IconoDocFirmado;
