from django.urls import path, include
from rest_framework.routers import DefaultRouter

from src.modules.adjuntos.views import AdjuntosView


router = DefaultRouter()
router.register("", AdjuntosView, basename="adjunto")

urlpatterns = [
    path("", include(router.urls)),
    path("upload/paso/<int:paso_id>/", AdjuntosView.as_view({"post": "subir_adjunto_paso"})),
    path("<int:adjunto_id>/download/", AdjuntosView.as_view({"get": "download_adjunto_paso"})),
    path("paso/<int:paso_id>/", AdjuntosView.as_view({"get": "listName_adjunto_paso"})),
]