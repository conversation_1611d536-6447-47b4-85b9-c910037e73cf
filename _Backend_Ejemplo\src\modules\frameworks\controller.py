from src.modules.dominios.models import Dominio
from src.modules.sub_dominios.models import SubDominio
from src.modules.controles.models import Control
from src.modules.niveles.models import Nivel
from src.modules.pasos.models import Paso
from src.utils.classes import Response
from .models import Framework
import re
import pandas as pd
from src.modules.suscripciones.models import Suscripcion


class FrameworkController:
    def __init__(self):
        self.monto_regex = r"^\d+(\.\d{1,2})?$"

    def __es_digito(self, monto):
        return re.match(self.monto_regex, monto)

    def validar_row(self, row):
        __msg = "Revise el id en columna "
        print(row)
        if not self.__es_digito(str(row.iloc[0])):
            raise ValueError(__msg + "A")
        if not self.__es_digito(str(row.iloc[3])):
            raise ValueError(__msg + "D")
        if not self.__es_digito(str(row.iloc[4])):
            raise ValueError(__msg + "E")
        if not self.__es_digito(str(row.iloc[7])):
            raise ValueError(__msg + "H")
        if not self.__es_digito(str(row.iloc[8])):
            raise ValueError(__msg + "I")
        if not self.__es_digito(str(row.iloc[11])):
            raise ValueError(__msg + "L")
        if not self.__es_digito(str(row.iloc[12])):
            raise ValueError(__msg + "M")
        if not self.__es_digito(str(row.iloc[15])):
            raise ValueError(__msg + "Q")
        if not self.__es_digito(str(row.iloc[16])):
            raise ValueError(__msg + "R")

    def __registrar_framework(self, request_data):
        categoria_id = request_data.get("int_idCategoria")
        nombre = request_data.get("str_nombre")
        suscripcion_id = request_data.get("str_idSuscripcion")
        descripcion = request_data.get("str_descripcion")
        framework = Framework.objects.create(
            int_idCategoria_id=categoria_id,
            str_nombre=nombre,
            str_idSuscripcion_id=suscripcion_id,
            str_descripcion=descripcion,
        )
        return framework

    def __registrar_dominios(self, dominios_dict, framework_id):
        try:
            lista_dominios = []
            for dominio_id, nombre in dominios_dict.items():
                dominio = Dominio.objects.create(
                    int_idFramework_id=framework_id,
                    str_nombre=nombre,
                )
                lista_dominios.append({dominio_id : dominio})
            return Response("Dominios registrados correctamente", data=lista_dominios, state=True)
        except Exception as e:
            return Response(str(e), e)
    
    def __registrar_sub_dominios(self, sub_dominios_dict, dominios_response):
        try:
            lista_sub_dominios = []
            # Crear un mapeo de IDs viejos a nuevos dominios
            dominios_mapping = {}
            for dominio_data in dominios_response.data:
                for excel_id, dominio_obj in dominio_data.items():
                    # Convertir el excel_id a formato "1.0" para que coincida con el formato en subdominios
                    dominios_mapping[str(int(float(excel_id)))] = dominio_obj.int_idDominio
            
            for sub_dominio_id, data in sub_dominios_dict.items():
                # Obtener el ID del nuevo dominio usando el ID del Excel
                
                excel_dominio_id = data['dominio_id']  # Ya viene en formato "1.0"
                
                nuevo_dominio_id = dominios_mapping.get(str(int(float(excel_dominio_id))))
                
                if nuevo_dominio_id is None:
                    raise ValueError(f"No se encontró el dominio correspondiente al ID {excel_dominio_id}")
                
                sub_dominio = SubDominio.objects.create(
                    int_idDominio_id=nuevo_dominio_id,
                    str_nombre=data['nombre']
                )
                lista_sub_dominios.append({sub_dominio_id: sub_dominio})
            
            return Response(
                message="Sub dominios registrados correctamente",
                data=lista_sub_dominios,
                state=True
            )
        except Exception as e:
            return Response(str(e), e)

    def __registrar_controles(self, controles_dict, subdominios_response):
        try:
            lista_controles = []
            # Crear mapeo de IDs de subdominios
            subdominios_mapping = {}
            for subdominio_data in subdominios_response.data:
                for excel_id, subdominio_obj in subdominio_data.items():
                    subdominios_mapping[str(int(float(excel_id)))] = subdominio_obj.int_idSubDominio
            
            for control_id, data in controles_dict.items():
                excel_subdominio_id = data['subdominio_id']
                
                nuevo_subdominio_id = subdominios_mapping.get(str(int(float(excel_subdominio_id))))
                
                if nuevo_subdominio_id is None:
                    raise ValueError(f"No se encontró el subdominio correspondiente al ID {excel_subdominio_id}")
                
                control = Control.objects.create(
                    int_idSubDominio_id=nuevo_subdominio_id,
                    str_descripcion=data['nombre'],
                    str_valorIndustria=str(int(float(data["nivel_industria"])))
                )
                lista_controles.append({control_id: control})
            
            return Response("Controles registrados correctamente", data=lista_controles, state=True)
        except Exception as e:
            return Response(str(e), e)

    def __registrar_niveles(self, niveles_dict, controles_response):
        try:
            lista_niveles = []
            # Crear mapeo de IDs de controles
            controles_mapping = {}
            for control_data in controles_response.data:
                for excel_id, control_obj in control_data.items():
                    controles_mapping[str(int(float(excel_id)))] = control_obj.int_idControl
            
            for nivel_id, data in niveles_dict.items():
                excel_control_id = data['control_id']
                nuevo_control_id = controles_mapping.get(str(int(float(excel_control_id))))
                
                if nuevo_control_id is None:
                    raise ValueError(f"No se encontró el control correspondiente al ID {excel_control_id}")
                nivel = Nivel.objects.create(
                    int_idControl_id=nuevo_control_id,
                    str_descripcion=data['nombre'],
                    int_orden=int(float(data["orden"])),
                    int_valor =int(float(data["valor"]))
                )
                lista_niveles.append({nivel_id: nivel})
            
            return Response("Niveles registrados correctamente", data=lista_niveles, state=True)
        except Exception as e:
            return Response(str(e), e)

    def __registrar_pasos(self, pasos_dict, niveles_response):
        try:
            lista_pasos = []
            # Crear mapeo de IDs de niveles
            niveles_mapping = {}
            for nivel_data in niveles_response.data:
                for excel_id, nivel_obj in nivel_data.items():
                    niveles_mapping[str(int(float(excel_id)))] = nivel_obj.int_idNivel
            
            for paso_id, data in pasos_dict.items():
                excel_nivel_id = data['nivel_id']
                nuevo_nivel_id = niveles_mapping.get(str(int(float(excel_nivel_id))))
                
                if nuevo_nivel_id is None:
                    raise ValueError(f"No se encontró el nivel correspondiente al ID {excel_nivel_id}")
                
                paso = Paso.objects.create(
                    int_idNivel_id=nuevo_nivel_id,
                    str_descripcion=data['nombre']
                )
                lista_pasos.append({paso_id: paso})
            
            return Response("Pasos registrados correctamente", data=lista_pasos, state=True)
        except Exception as e:
            return Response(str(e), e)

    def leer_plantilla(self, file, request_data):
        try:
            dominios = {}
            subdominios = {}
            controles = {}
            niveles = {}
            pasos = {}

            if not Suscripcion.objects.filter(
                str_idSuscripcion=request_data.get("str_idSuscripcion")
            ).exists():
                return Response("La suscripción no existe", state=False)

            framework = self.__registrar_framework(request_data)
            df = pd.read_excel(file)

            for _, row in df.iterrows():
                id_dominio = str(row.iloc[0])
                if id_dominio != "nan":
                    dominios[id_dominio] = row.iloc[1]

                id_subdominio = str(row.iloc[4])
                if id_subdominio != "nan":
                    subdominios[id_subdominio] = {
                        "nombre": row.iloc[5],
                        "dominio_id": str(row.iloc[3]),
                    }

                id_control = str(row.iloc[8])
                if id_control != "nan":
                    controles[id_control] = {
                        "nombre": row.iloc[9],
                        "subdominio_id": str(row.iloc[7]),
                        "nivel_industria": str(row.iloc[10])
                    }

                id_nivel = str(row.iloc[12])
                if id_nivel != "nan":
                    niveles[id_nivel] = {
                        "nombre": row.iloc[13],
                        "control_id": str(row.iloc[11]),
                        "orden": str(row.iloc[14]) if not pd.isna(row.iloc[14]) else None,
                        "valor": str(row.iloc[15]) if not pd.isna(row.iloc[15]) else None,
                    }

                id_paso = str(row.iloc[17])
                if id_paso != "nan":
                    pasos[id_paso] = {
                        "nombre": row.iloc[18],
                        "nivel_id": str(row.iloc[16]),
                    }
                    
            # Registrar en cascada
            dominios_response = self.__registrar_dominios(dominios, framework.int_idFramework)
            if not dominios_response.state:
                return dominios_response
            
            subdominios_response = self.__registrar_sub_dominios(subdominios, dominios_response)
            if not subdominios_response.state:
                return subdominios_response

            controles_response = self.__registrar_controles(controles, subdominios_response)
            if not controles_response.state:
                return controles_response
          

            niveles_response = self.__registrar_niveles(niveles, controles_response)
            if not niveles_response.state:
                return niveles_response
            pasos_response = self.__registrar_pasos(pasos, niveles_response)
            if not pasos_response.state:
                return pasos_response

            return Response("Framework registrado correctamente", data=framework.int_idFramework, state=True)

        except ValueError as ve:
            return Response(str(ve), ve)
        except Exception as e:
            return Response(str(e), e)

    def get_by_suscripcion(self, suscripcion_id: str):
        try:
            frameworks = Framework.objects.filter(
                str_idSuscripcion_id=suscripcion_id
            ).all()
            if not frameworks:
                return Response("No se encontraron frameworks", state=False)
            return Response(data=frameworks, state=True)

        except Exception as e:
            return Response(str(e), e)

    def tiene_evaluacion(self, framework_id: int):
        """Verifica si el framework tiene evaluaciones

        Args:
            framework_id (int): Id del framework

        Returns:
            Response: Respuesta de la funcion
        """
        try:
            if Framework.objects.get(
                int_idFramework=framework_id
            ).evaluaciones_framework.all():
                return Response(
                    "No se puede editar el framework, tiene evaluaciones", state=False
                )
            return Response("Ok", state=True)

        except Exception as e:
            return Response(str(e), e)

    def cambiar_estado(self, framework_id: int):
        """Cambia el estado (activo/inactivo) de un framework.

        Args:
            framework_id (int): ID del framework a modificar.

        Returns:
            Response: Respuesta indicando el resultado de la operación.
        """
        try:
            framework = Framework.objects.get(int_idFramework=framework_id)
            # Cambia el estado booleano
            framework.bool_estado = not framework.bool_estado
            framework.save()
            nuevo_estado = "activo" if framework.bool_estado else "inactivo"
            return Response(
                f"Estado del framework cambiado a {nuevo_estado}", state=True
            )
        except Framework.DoesNotExist:
            return Response(
                f"Framework con ID {framework_id} no encontrado", state=False
            )
        except Exception as e:
            # Considerar registrar el error aquí para depuración
            return Response(f"Error al cambiar estado: {str(e)}", state=False)
