"""
URL configuration for financiera project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.urls import path, include, re_path
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework import permissions
from django.conf import settings
from django.conf.urls.static import static
from django.views.static import serve

schema_view = get_schema_view(
    openapi.Info(
        title="Financiera API Documentation",
        default_version="v0.1",
        description="Endpoints for Financiera API documentation",
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    path(
        "swagger/",
        schema_view.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui",
    ),
    path("empresas/", include("src.modules.empresas.urls")),
    path("usuarios/", include("src.modules.usuarios.urls")),
    path("agrupadores/", include("src.modules.agrupadores.urls")),
    path("cuentas/", include("src.modules.cuentas.urls")),
    path("ef_cuentas/", include("src.modules.ef_cuentas.urls")),
    path("estados_financieros/", include("src.modules.estado_financiero.urls")),
    path("plantillas/", include("src.modules.plantillas.urls")),
    path("ratios/", include("src.modules.ratios.urls")),
    path("ratios_ef/", include("src.modules.ratios_ef.urls")),
    path("total_agrupador/", include("src.modules.total_agrupador.urls")),
]

if not settings.DEBUG:
    urlpatterns += [
        re_path(
            r'^static/(?P<path>.*)$',
            serve,
            {'document_root': settings.STATIC_ROOT}
        ),
    ]
else:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
