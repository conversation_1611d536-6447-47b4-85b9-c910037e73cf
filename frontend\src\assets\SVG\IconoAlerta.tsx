import React from "react";

const IconoAlerta = ({size,color}) => {
  return (
    <svg
       fill="none"
      height={size}
      stroke="currentColor"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      viewBox="0 0 24 24"
      width={size}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" fill={color}/>
      <line x1="12" x2="12" y1="9" y2="13" fill={color}/>
      <line x1="12" x2="12.01" y1="17" y2="17" fill={color}/>
    </svg>
  );
};

export default IconoAlerta;
