import React from "react";

const Modal = ({ isOpen, onClose, children, titulo = null, edidData=null }) => {
  if (!isOpen) return null;

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center overflow-x-hidden overflow-y-auto outline-none focus:outline-none bg-black/30"
      onClick={handleBackdropClick}
    >
      <div className="relative w-full max-w-[75rem] mx-4 md:mx-auto">
        <div className="relative flex flex-col w-full bg-white rounded-[0.5rem] shadow-lg overflow-hidden">
          {titulo && (
            <div className="poppins-font-600 text-[1.25rem] bg-[#F8FAFB] p-6 m-4 text-left">
              {titulo}
            </div>
          )}
          <div className="w-full">{children}</div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
