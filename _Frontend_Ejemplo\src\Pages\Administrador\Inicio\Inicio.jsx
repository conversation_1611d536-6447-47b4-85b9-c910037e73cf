import React, { useState, useEffect } from "react";
import Card from "../../../Components/Card/Card";
import IconoAjustes from "../../../assets/SVG/IconoAjustes.jsx";
import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import Input from "../../../Components/Input/Input.jsx";
import Button from "../../../Components/Button/Button.jsx";
import IconoBuscar from "../../../assets/SVG/IconoBuscar";
import IconoAgregar from "../../../assets/SVG/IconoAgregar";
import CustomPaginator from "../../../Components/Paginator/Paginator.jsx";
import { RoutesPrivate } from "../../../Routes/ProtectedRoute.js";
import { useGoTo } from "../../../Services/Globales.jsx";
import { ConfigSolid } from "../../../assets/SVG/ConfigSolid.jsx";
import FiltroModal from "./FiltroModal.jsx";
import ProgressBar from "../../../Components/ProgressBar/ProgressBar.jsx";
import foto from "../../../assets/avatars/300-1.jpg";
import CrearProyectoModal from "./CrearProyectoModal";
import ProyectosService from "../../../Services/ProyectosService";

// Función para formatear fechas en formato DD/MM/YYYY sin usar Date
const formatearFecha = (fechaString) => {
  if (!fechaString) return "-";

  try {
    // Verificar si la fecha tiene el formato esperado (YYYY-MM-DD)
    if (!/^\d{4}-\d{2}-\d{2}$/.test(fechaString)) return "-";

    // Dividir la fecha en sus componentes
    const [anio, mes, dia] = fechaString.split('-');

    // Retornar en formato DD/MM/YYYY
    return `${dia}/${mes}/${anio}`;
  } catch (error) {
    console.error("Error al formatear fecha:", error);
    return "-";
  }
};

const Proyectos = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [openModal, setOpenModal] = useState(false);
  const [open, setOpen] = useState(false);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [clearFiltersFlag, setClearFiltersFlag] = useState(false);
  const { goTo } = useGoTo();

  const handleCloseModal = () => {
    setOpenModal(!openModal);
  };

  const handleClose = () => {
    setOpen(!open);
  };

  const handleSubmit = async (filters) => {
    setError(null); // Limpiar errores anteriores
    try {
      setLoading(true);
      const data = await ProyectosService.filterProyectos(filters);
      setProjects(data);
      setTotalPages(Math.ceil(data.length / 5)); // Changed from 10 to 5 projects per page
      setCurrentPage(1); // Reset to first page after filtering
      // Clear search input when applying filters
      setSearchTerm("");
      // El modal se cierra desde el componente FiltroModal
    } catch (err) {
      console.error("Error filtering projects:", err);

      // Si es un error 404 (no se encontraron proyectos), mostramos el mensaje personalizado
      if (err.isNotFound) {
        setError(err.message);
        setProjects([]); // Aseguramos que la lista de proyectos esté vacía
        setTotalPages(0);
        // El modal se cierra desde el componente FiltroModal
      } else {
        // Para otros errores, mostramos un mensaje genérico
        setError("Ha ocurrido un error al filtrar los proyectos. Por favor, inténtalo de nuevo.");
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Manejar cambios en el input de búsqueda
  const handleSearchInputChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);

    // Si el campo está vacío, cargar todos los proyectos
    if (value.trim() === '') {
      fetchProjects();
    }
  };

  // Ejecutar la búsqueda
  const executeSearch = async () => {
    setError(null); // Limpiar errores anteriores

    // Clear filters when executing a search
    if (window.clearFilterModal) {
      window.clearFilterModal();
    }
    setClearFiltersFlag(prev => !prev); // Toggle flag to trigger useEffect in FiltroModal

    if (searchTerm.trim() === '') {
      // Si la búsqueda está vacía, cargar todos los proyectos
      fetchProjects();
    } else if (searchTerm.trim().length > 0) {
      // Solo buscar si hay al menos 1 caracter
      try {
        setLoading(true);
        const data = await ProyectosService.buscarProyectos(searchTerm);
        setProjects(data);
        setTotalPages(Math.ceil(data.length / 5)); // Changed from 10 to 5 projects per page
      } catch (err) {
        console.error("Error searching projects:", err);

        // Si es un error 404 (no se encontraron proyectos), mostramos el mensaje personalizado
        if (err.isNotFound) {
          setError(err.message);
          setProjects([]); // Aseguramos que la lista de proyectos esté vacía
          setTotalPages(0);
        } else {
          // Para otros errores, mostramos un mensaje genérico
          setError("Ha ocurrido un error al buscar proyectos. Por favor, inténtalo de nuevo.");
        }
      } finally {
        setLoading(false);
      }
    } else {
      // Mostrar mensaje si la búsqueda tiene menos de 3 caracteres
      setError("Ingresa al menos 3 caracteres para buscar");
    }
  };

  // Manejar evento de tecla Enter
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      executeSearch();
    }
  };

  // Fetch projects from API
  const fetchProjects = async () => {
    setLoading(true);
    setError(null); // Limpiar errores anteriores
    try {
      const data = await ProyectosService.getProyectosByUsuario();
      setProjects(data);
      // Calculate total pages based on 5 items per page
      setTotalPages(Math.ceil(data.length / 5));
    } catch (err) {
      console.error("Error fetching projects:", err);

      // Si es un error 404 (no se encontraron proyectos), mostramos el mensaje personalizado
      if (err.isNotFound) {
        setError(err.message);
        setProjects([]); // Aseguramos que la lista de proyectos esté vacía
        setTotalPages(0);
      } else {
        // Para otros errores, mostramos un mensaje genérico
        setError("Ha ocurrido un error al cargar los proyectos. Por favor, inténtalo de nuevo más tarde.");
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch projects on component mount
  useEffect(() => {
    fetchProjects();
  }, []);

  // Paginate projects - 5 projects per page
  const paginatedProjects = projects.slice(
    (currentPage - 1) * 5,
    currentPage * 5
  );

  const columns = [
    {
      key: "proyecto",
      header: "Proyecto",
      render: (value) => <strong>{value}</strong>,
    },
    {
      key: "origen",
      header: "Origen",
    },
    {
      key: "responsable",
      header: "Responsable",
    },
    {
      key: "fecha_inicio",
      header: "Fecha de Inicio",
    },
    {
      key: "fecha_fin",
      header: "Fecha de Fin",
    },
    {
      key: "presupuesto",
      header: "Presupuesto",
    },
    {
      key: "estado",
      header: "Estado",
    },
    {
      key: "avance",
      header: "Avance",
    },
    {
      key: "opciones",
      header: "",
    },
  ];

  ChartJS.register(ArcElement, Tooltip, Legend);

  // Calculate project status counts for chart
  const getProjectStatusCounts = () => {
    const completed = projects.filter(project => project.int_idEstado === 1).length;
    const inProgress = projects.filter(project => project.int_idEstado === 2).length;
    const new_projects = projects.filter(project => project.int_idEstado === 3).length;
    return [completed, inProgress, new_projects];
  };

  const data = {
    labels: ["Terminado", "En Proceso", "Nuevos"],
    datasets: [
      {
        data: getProjectStatusCounts(),
        backgroundColor: ["#A7FF90", "#FFCE80", "#1890FF"],
        hoverBackgroundColor: ["#A7FF90", "#FFCE80", "#1890FF"],
      },
    ],
  };

  const options = {
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: "#FFFFFF",
        borderColor: "#D0D5DD",
        borderWidth: 1,
        cornerRadius: 8,
        titleColor: "#101828",
        bodyColor: "#475467",
        padding: 12,
        boxWidth: 0,
        boxHeight: 0,
        titleFont: {
          weight: "bold",
        },
        callbacks: {
          label: function (context) {
            return ` ${context.label}: ${context.formattedValue}`;
          },
        },
      },
    },
  };

  return (
    <div className="p-3 sm:p-4 md:p-5 w-full max-w-full">
      {/* Título */}
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-3 sm:gap-4 md:gap-8 mb-3 sm:mb-0">
        <div className="text-black text-xl sm:text-2xl md:text-[1.75rem] font-normal poppins-font pb-2 sm:pb-3 md:pb-5">
          <div>Proyectos</div>
        </div>
        <div className="w-full sm:w-auto">
          <Button
            text_button="Nuevo Proyecto"
            accion={() => {
              handleCloseModal();
            }}
            className="w-full sm:w-[10rem] md:w-[11.0625rem] h-[2.5rem] font-bold border-[#D0D5DD] poppins-font-500 cursor-pointer border-1 rounded-xl bg-[#1890FF] text-white text-center text-sm sm:text-base"
            icon={<IconoAgregar size="1.5rem" />}
          />
        </div>
      </div>

      {/* CARDS */}
      <div className="flex flex-col lg:flex-row gap-3 sm:gap-4 lg:gap-7">
        <div className="w-full lg:w-[36.9375rem] lg:h-full">
          <Card bg_color={"#F8FAFB"} className="h-full">
            <div className="flex flex-col sm:flex-row justify-between items-center">
              <div className="w-full sm:w-auto">
                <div className="text-black font-bold poppins-font-600 pb-2 sm:pb-3 md:pb-5 text-base sm:text-lg md:text-[1.25rem] text-center sm:text-left">
                  Avance de Proyectos
                </div>
                {/* Lista de estados */}
                <div className="flex flex-col gap-2 w-max mx-auto sm:mx-0">
                  <div className="flex items-center gap-2">
                    <div className="bg-[#A7FF90] w-[0.6875rem] h-[0.6875rem]" />
                    <span className="text-gray-900 text-xs sm:text-sm">Terminado</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="bg-[#FFCE80] w-[0.6875rem] h-[0.6875rem]" />
                    <span className="text-gray-900 text-xs sm:text-sm">En Proceso</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="bg-[#1890FF] w-[0.6875rem] h-[0.6875rem]" />
                    <span className="text-gray-900 text-xs sm:text-sm">Nuevos</span>
                  </div>
                </div>
              </div>
              <div className="w-[6rem] sm:w-[7rem] md:w-[8rem] h-[6rem] sm:h-[7rem] md:h-[8rem] mt-3 sm:mt-0 flex justify-center items-center">
                <Doughnut data={data} options={options} />
              </div>
            </div>
          </Card>
        </div>

        <div className="w-full lg:w-[68.125rem]">
          <Card
            shadow={""}
            bg_color={"#FFFFFF"}
            className="border-1 border-[#D0D5DD] overflow-x-auto h-full"
            padding="p-3"
          >
            {/* Commented section removed for brevity */}
          </Card>
        </div>
      </div>

      {/* Search y Filtrar */}
      <div className="flex flex-col sm:flex-row items-center gap-2 pt-3 sm:pt-4 md:pt-5">
        <div className="w-full sm:w-[18rem] md:w-[22.3125rem] mb-2 sm:mb-0">
          <Input
            className="w-full border-2 py-1 border-[#D0D5DD] rounded-md pr-10 px-4 poppins-font outline-0 text-sm sm:text-base"
            placeholder="Buscar"
            icon={<IconoBuscar size="1rem" color="#000000" />}
            onChange={handleSearchInputChange}
            onIconClick={executeSearch}
            onKeyDown={handleKeyDown}
            value={searchTerm}
          />
        </div>
        <div
          className="w-full sm:w-[5.6875rem] cursor-pointer"
          onClick={handleClose}
        >
          <Button
            text_button="Filtrar"
            className="w-full h-[2.3rem] px-2 border-[#D0D5DD] poppins-font cursor-pointer border-1 rounded-md text-sm sm:text-base"
            icon={<IconoAjustes size="1rem" color="#000000" />}
          />
        </div>
      </div>

      {/* Tabla */}
      <div className="pt-3 sm:pt-4 md:pt-5">
        <div className="border-1 border-[#D0D5DD] rounded-[0.5rem] sm:rounded-[0.75rem] poppins-font">
          <div className="overflow-x-auto">
            <table className="w-full min-w-[800px]">
              <thead>
                <tr>
                  {columns.map((col) => (
                    <th
                      key={col.key}
                      className="px-2 sm:px-4 md:px-10 py-3 sm:py-4 md:py-5 cursor-pointer bg-[#FBFCFF] text-xs sm:text-sm md:text-base border-r-1 border-[#f9f0f0] text-left poppins-font-600 text-[#272727]"
                    >
                      {col.header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={columns.length} className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 md:px-10 text-center">
                      <div className="flex flex-col items-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#1890FF] mx-auto"></div>
                        <p className="mt-2 text-gray-600 text-sm">Cargando proyectos...</p>
                      </div>
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan={columns.length} className="py-4 sm:py-6 md:py-8 px-2 sm:px-4 md:px-10 text-center">
                      <div className="flex flex-col items-center justify-center">
                        <div className="text-blue-500 text-sm sm:text-base md:text-lg font-semibold mb-2">
                          {error}
                        </div>
                        <div className="text-gray-500 text-xs sm:text-sm">
                          Puedes crear un nuevo proyecto utilizando el botón "Nuevo Proyecto" en la parte superior.
                        </div>
                      </div>
                    </td>
                  </tr>
                ) : paginatedProjects.length === 0 ? (
                  <tr>
                    <td colSpan={columns.length} className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 md:px-10 text-center">
                      No se encontraron proyectos que coincidan con los criterios de búsqueda
                    </td>
                  </tr>
                ) : (
                  paginatedProjects.map((project, index) => (
                    <tr
                      key={project.int_idProyecto || index}
                      className="border-y-1 border-[#D0D5DD] hover:bg-gray-50"
                    >
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 md:px-10 text-xs sm:text-sm border-r-1 border-[#f9f0f0] poppins-font-600 text-[#272727]">
                        {project.str_nombre}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 md:px-10 text-xs sm:text-sm border-r-1 border-[#f9f0f0]">
                        {project.str_origen || "-"}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 md:px-10 text-xs sm:text-sm border-r-1 border-[#f9f0f0]">
                        <div className="flex gap-1 items-center">
                          <div className="h-[1.8rem] sm:h-[2.3125rem] w-[1.8rem] sm:w-[2.25rem]">
                            <img
                              src={foto}
                              className="rounded-full"
                              alt="User"
                            />
                          </div>
                          <div className="truncate max-w-[100px] sm:max-w-full">
                            {project.usuario
                              ? `${project.usuario.str_Nombres} ${project.usuario.str_Apellidos}`
                              : "No asignado"}
                          </div>
                        </div>
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 md:px-10 text-xs sm:text-sm border-r-1 border-[#f9f0f0]">
                        {formatearFecha(project.dt_fechaInicio)}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 md:px-10 text-xs sm:text-sm border-r-1 border-[#f9f0f0]">
                        {formatearFecha(project.dt_fechaFin)}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 md:px-10 text-xs sm:text-sm border-r-1 border-[#f9f0f0] text-right">
                        {project.str_presupuesto ? `S/. ${project.str_presupuesto}` : "-"}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 md:px-10 border-r-1 border-[#f9f0f0]">
                        <div className="flex gap-1 sm:gap-2 items-center text-xs sm:text-sm">
                          <div
                            className={`rounded-full ${
                              project.int_idEstado === 3
                                ? "bg-[#1890FF]"
                                : project.int_idEstado === 2
                                ? "bg-[#FFCE80]"
                                : project.int_idEstado === 1
                                ? "bg-[#47D691]"
                                : "-"
                            } w-[0.6rem] sm:w-[0.875rem] h-[0.6rem] sm:h-[0.875rem]`}
                          />
                          {project.int_idEstado === 3
                            ? "Nuevo"
                            : project.int_idEstado === 2
                            ? "En Proceso"
                            : project.int_idEstado === 1
                            ? "Terminada"
                            : "-"}
                        </div>
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 md:px-10 border-r-1 border-[#f9f0f0]">
                        {project.str_avance ? (
                          <ProgressBar
                            percentage={parseInt(project.str_avance)}
                            color={
                              parseInt(project.str_avance) <= 30
                                ? "red"
                                : parseInt(project.str_avance) >= 31 && parseInt(project.str_avance) <= 70
                                ? "yellow"
                                : parseInt(project.str_avance) > 70
                                ? "green"
                                : ""
                            }
                          />
                        ) : (
                          "-"
                        )}
                      </td>

                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-4 md:px-10 text-xs sm:text-sm">
                        <div
                          onClick={() => goTo(RoutesPrivate.TAREAS, {
                            proyecto: {
                              id: project.int_idProyecto,
                              proyecto: project.str_nombre,
                              origen: project.str_origen,
                              responsable: {
                                nombre: project.usuario
                                  ? `${project.usuario.str_Nombres} ${project.usuario.str_Apellidos}`
                                  : "No asignado",
                                foto: foto,
                                id: project.usuario ? project.usuario.int_idUsuarios : null,
                              },
                              fecha_inicio: formatearFecha(project.dt_fechaInicio),
                              fecha_fin: formatearFecha(project.dt_fechaFin),
                              presupuesto: project.str_presupuesto ? `S/. ${project.str_presupuesto}` : "-",
                              estado: project.int_idEstado,
                              avance: parseInt(project.str_avance || "0"),
                              gasto: project.str_gasto ? `S/. ${project.str_gasto}` : "-",
                              empresa_id: project.int_idEmpresa,
                            }
                          })}
                          className="cursor-pointer w-[2rem] sm:w-[2.5rem] h-[2rem] sm:h-[2.5rem] border-1 border-[#C1C0C0] bg-[#F8FAFB] rounded-[0.5rem] flex items-center justify-center">
                          <ConfigSolid color="#C1C0C0" />
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          <div className="flex justify-end px-2 sm:px-5 py-2 sm:py-3">
            <CustomPaginator
              totalPages={totalPages}
              currentPage={currentPage}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      </div>
      <FiltroModal
        open={open}
        handleClose={handleClose}
        handleSubmit={handleSubmit}
        clearFiltersExternal={clearFiltersFlag}
      />
      <CrearProyectoModal
        isOpen={openModal}
        onClose={() => {
          handleCloseModal();
          fetchProjects(); // Refresh projects after creating a new one
        }}
      />
    </div>
  );
};

export default Proyectos;
// Example usage
