from src.utils.classes import Response
from .models import Categoria


class CategoriaController:
    def __init__(self): ...

    def get_by_suscripcion(self, suscripcion_id: str):
        try:
            categorias = Categoria.objects.filter(
                str_idSuscripcion_id=suscripcion_id
            ).all()
            if not categorias:
                return Response("No se encontraron categorias", state=False)
            return Response(data=categorias, state=True)

        except Exception as e:
            return Response(str(e), e)