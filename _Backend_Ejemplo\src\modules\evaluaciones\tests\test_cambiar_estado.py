from django.test import TestCase
from src.modules.evaluaciones.controller import <PERSON>luacionController
from src.modules.evaluaciones.models import Evaluacion
from src.modules.evaluados.models import Evaluado
from src.modules.detalles_evaluacion.models import DetalleEvaluacion
from src.modules.frameworks.models import Framework, Dominio, SubDominio, Control
from src.modules.usuarios.models import Usuario
from src.modules.empresas.models import Empresa
from unittest.mock import patch, MagicMock

class TestCambiarEstado(TestCase):
    def setUp(self):
        # Crear objetos necesarios para las pruebas
        self.usuario = Usuario.objects.create(
            str_nombre="Test User",
            str_apellido="Test",
            str_email="<EMAIL>",
            str_password="password"
        )

        self.empresa = Empresa.objects.create(
            str_nombre="Test Company",
            str_ruc="12345678901",
            str_direccion="Test Address"
        )

        self.framework = Framework.objects.create(
            str_nombre="Test Framework",
            str_descripcion="Test Description"
        )

        self.evaluacion = Evaluacion.objects.create(
            str_nombre="Test Evaluacion",
            str_descripcion="Test Description",
            bool_estado=True,  # En proceso
            bool_tobe=True,    # ToBe completado
            bool_asis=True,    # AsIs completado
            int_idUsuarios=self.usuario,
            int_idEmpresa=self.empresa,
            int_idFramework=self.framework
        )

        # Crear dominio, subdominio y control
        self.dominio = Dominio.objects.create(
            str_nombre="Test Dominio",
            int_idFramework=self.framework
        )

        self.subdominio = SubDominio.objects.create(
            str_nombre="Test Subdominio",
            int_idDominio=self.dominio
        )

        self.control = Control.objects.create(
            str_descripcion="Test Control",
            str_valorIndustria="3",
            int_idSubDominio=self.subdominio
        )

        # Crear evaluados (ToBe y AsIs)
        self.evaluado_tobe = Evaluado.objects.create(
            int_idUsuarios=self.usuario,
            int_idEvaluacion=self.evaluacion,
            int_idTipoEvaluado=1,  # ToBe
            bool_estado=True
        )

        self.evaluado_asis = Evaluado.objects.create(
            int_idUsuarios=self.usuario,
            int_idEvaluacion=self.evaluacion,
            int_idTipoEvaluado=2,  # AsIs
            bool_estado=True
        )

        # Crear detalles de evaluación
        self.detalle_tobe = DetalleEvaluacion.objects.create(
            int_idEvaluacion=self.evaluacion,
            int_idEvaluado=self.evaluado_tobe,
            int_idControl=self.control,
            str_valor_tobe="3.5"  # Debería redondearse a 4
        )

        self.detalle_asis = DetalleEvaluacion.objects.create(
            int_idEvaluacion=self.evaluacion,
            int_idEvaluado=self.evaluado_asis,
            int_idControl=self.control,
            str_valor_asis="1.5"  # Debería truncarse a 1
        )

        self.controller = EvaluacionController()

    @patch('src.modules.evaluaciones.controller.EvaluacionController.calcular_resultado_promedio')
    def test_cambiar_estado_evaluacion_completa(self, mock_calcular_resultado):
        # Configurar el mock para calcular_resultado_promedio
        mock_response = MagicMock()
        mock_response.state = True
        mock_response.data = {"asis": 1.5, "tobe": 3.5}
        mock_response.message = "Resultado promedio calculado exitosamente"
        mock_calcular_resultado.return_value = mock_response

        # Llamar al método cambiar_estado
        response = self.controller.cambiar_estado(self.evaluacion.int_idEvaluacion)

        # Verificar que el estado se cambió correctamente
        self.assertTrue(response.state)

        # Recargar la evaluación desde la base de datos
        self.evaluacion.refresh_from_db()

        # Verificar que los campos se actualizaron correctamente
        self.assertFalse(self.evaluacion.bool_estado)  # Ahora está terminada
        self.assertEqual(self.evaluacion.str_nota, "1")  # AsIs truncado
        self.assertIn("oportunidades de mejora", self.evaluacion.str_resultado)  # Mensaje de brecha

    def test_cambiar_estado_evaluacion_incompleta(self):
        # Cambiar bool_asis a False para simular una evaluación incompleta
        self.evaluacion.bool_asis = False
        self.evaluacion.save()

        # Llamar al método cambiar_estado
        response = self.controller.cambiar_estado(self.evaluacion.int_idEvaluacion)

        # Verificar que no se permitió el cambio
        self.assertFalse(response.state)
        self.assertIn("no se han completado todas las evaluaciones", response.message)

        # Recargar la evaluación desde la base de datos
        self.evaluacion.refresh_from_db()

        # Verificar que el estado no cambió
        self.assertTrue(self.evaluacion.bool_estado)  # Sigue en proceso

    def test_cambiar_estado_evaluacion_inactiva(self):
        # Cambiar bool_estado a False para simular una evaluación inactiva
        self.evaluacion.bool_estado = False
        self.evaluacion.save()

        # Llamar al método cambiar_estado
        response = self.controller.cambiar_estado(self.evaluacion.int_idEvaluacion)

        # Verificar que el estado se cambió correctamente
        self.assertTrue(response.state)

        # Recargar la evaluación desde la base de datos
        self.evaluacion.refresh_from_db()

        # Verificar que el estado cambió a activo
        self.assertTrue(self.evaluacion.bool_estado)
