# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('empresas', '0001_initial'),
        ('frameworks', '0001_initial'),
        ('usuarios', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Evaluacion',
            fields=[
                ('int_idEvaluacion', models.AutoField(primary_key=True, serialize=False)),
                ('str_nombre', models.CharField(blank=True, max_length=255)),
                ('dt_fechaInicio', models.DateTimeField(auto_now_add=True)),
                ('dt_fechaFin', models.DateTimeField(null=True)),
                ('str_resultado', models.TextField()),
                ('bool_estado', models.BooleanField(default=False)),
                ('str_descripcion', models.TextField()),
                ('str_nota', models.CharField(blank=True, max_length=3)),
                ('int_idEmpresa', models.ForeignKey(db_column='int_idEmpresa', on_delete=django.db.models.deletion.CASCADE, related_name='evaluaciones_empresa', to='empresas.empresa')),
                ('int_idFramework', models.ForeignKey(db_column='int_idFramework', on_delete=django.db.models.deletion.CASCADE, related_name='evaluaciones_framework', to='frameworks.framework')),
                ('int_idUsuarios', models.ForeignKey(db_column='int_idUsuarios', on_delete=django.db.models.deletion.CASCADE, related_name='evaluaciones_responsable', to='usuarios.usuario')),
            ],
            options={
                'verbose_name': 'evaluacion',
                'verbose_name_plural': 'evaluaciones',
                'db_table': 'tr_evaluaciones',
                'managed': True,
            },
        ),
    ]
