# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('usuarios', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Log',
            fields=[
                ('int_idLog', models.AutoField(primary_key=True, serialize=False)),
                ('str_operacion', models.CharField(max_length=255)),
                ('dt_fechaOperacion', models.DateTimeField(auto_now_add=True)),
                ('str_tabla', models.CharField(max_length=255)),
                ('str_idObjeto', models.IntegerField()),
                ('str_objeto', models.TextField()),
                ('int_idUsuarios', models.ForeignKey(db_column='int_idUsuarios', on_delete=django.db.models.deletion.CASCADE, related_name='th_logs', to='usuarios.usuario')),
            ],
            options={
                'verbose_name': 'log',
                'verbose_name_plural': 'logs',
                'db_table': 'th_logs',
                'managed': True,
            },
        ),
    ]
