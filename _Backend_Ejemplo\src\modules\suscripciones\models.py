from django.db import models

# Create your models here.
class Suscripcion(models.Model):
    class Meta:
        db_table = 'tm_suscripcion'
        managed = True
        verbose_name = 'suscripcion'
        verbose_name_plural = 'suscripciones'
        
    str_idSuscripcion = models.CharField(max_length=10, primary_key=True)
    str_Nombre = models.CharField(max_length=255)
    dt_FechaCreacion = models.DateTimeField()
    dt_FechaModificacion = models.DateTimeField(null=True)
    int_idUsuarioCreacion = models.IntegerField()
    int_idUsuarioModificacion = models.IntegerField(null=True)
    