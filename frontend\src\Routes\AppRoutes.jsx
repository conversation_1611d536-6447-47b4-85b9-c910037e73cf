import { FC, useEffect, useState } from "react"; 
import { Routes, Route, BrowserRouter, Navigate } from "react-router-dom";
import { PrivateRoutes } from "./PrivateRoutes";
import { decrypt, encrypt,validateToken, checkSesion, logout } from "../Services/TokenService";
import Cookies from "js-cookie";
import axios from "axios";
import Swal from "sweetalert2";
import { isAxiosError } from "axios";
import API_GESTOR from "../assets/APIS/ApisAdministrador";


const AppRoutes = () => {
  const [verificado, setVerificado] = useState(false);
  const [rol, setRol] = useState("");
  const [ipAddress, setIpAddress] = useState("");

  const getCookieOrParam = (
    param ,
    cookieName 
  ) => {
    const params = new URLSearchParams(window.location.search);
    const value =
      params.get(param) ||
      (Cookies.get(cookieName) ? decrypt(Cookies.get(cookieName)) : null);
    if (value) {
      Cookies.set(cookieName, encrypt(value));
    }
    return value;
  };

  const idAplicacion = getCookieOrParam(
    "idAplicacion",
    "idAplicacionFinanciera"
  );
  const suscriptor = getCookieOrParam("Suscriptor", "suscriptorFinanciera");
  const correoUser = getCookieOrParam("correoUser", "correoUserFinanciera");
  const app = getCookieOrParam("app", "appFinanciera");
  const sesion = getCookieOrParam("sesion", "sesionFinanciera");
  const suscripcion = getCookieOrParam(
    "Suscripcion",
    "suscripcionFinanciera"
  );
  const usuario = getCookieOrParam("hora_llegada", ("hora_llegadaFinanciera"));

  const proceso = getCookieOrParam("proceso", "procesoFinanciera");
  const params = new URLSearchParams(window.location.search);
  const value =  params.get("sesion")
  if (value) {
    localStorage.removeItem("isVerifiedFinanciera");
  } 
  const baseSEGURIDAD=import.meta.env.VITE_SEGURIDAD_URL
  const ContratosURL = import.meta.env.VITE_BASE_URL_PRISMA;

   useEffect(() => {
    const fetchIpAddress = async () => {
      try {
        const response = await fetch("https://api.ipify.org?format=json");
        const data = await response.json();
        setIpAddress(data.ip);
      } catch (error) {
        console.error("Error al obtener la IP:", error);
      }
    };
    fetchIpAddress();
  }, []);

  useEffect(() => {
    const VerificacionSeguridad = async () => {
      try {
        if (localStorage.getItem("isVerifiedFinanciera")) {
          await validateToken();
          await VerificarSesion()




          setVerificado(true);
          setRol(decrypt(Cookies.get("rolFinanciera")) || "");
          return

        }

        const response = await axios.get(
          `${baseSEGURIDAD}seguridad/consultar/sesion/${sesion}/`
        );
        if (isAxiosError(response)) {
          logout();
          return;
        }
        if (response.status >= 200 && response.status < 300) {
          const responseSeguridad = await axios.get(
            `${baseSEGURIDAD}seguridad/token/`,
            {
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${response.data.str_token}`,
              },
            }
          );

          if (
            responseSeguridad.status >= 200 &&
            responseSeguridad.status < 300
          ) {
            Cookies.set("TokenFinanciera", responseSeguridad.data.token);
            Cookies.set(
              "refreshTokenFinanciera",
              responseSeguridad.data.refresh_token
            );

            await validateToken();

            try {
              const respuestaAppsAsignadas = await axios.get(
                `${baseSEGURIDAD}asignacion_aplicacion/suscriptor/${suscriptor}/`,
                {
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${responseSeguridad.data.token}`,
                  },
                }
              );
              const dataRespuestaAppsAsignadas = respuestaAppsAsignadas.data;
              
              if (
                !dataRespuestaAppsAsignadas.some(
                  (app) => app.int_idAplicacion === Number(idAplicacion)
                )
              ) {
                return;
              }

              const { data: userData } = await axios.get(
                `${baseSEGURIDAD}usuarios/correo/${correoUser}/`,
                {
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${responseSeguridad.data.token}`,
                  },
                }
              );

              if (!userData) {
                logout();
                return;
              }

              Cookies.set("nombresFinanciera", encrypt(userData.str_Nombres));
              Cookies.set("apellidosFinanciera", encrypt(userData.str_Apellidos));
              Cookies.set("correoFinanciera", encrypt(userData.str_Correo));
              Cookies.set(
                "hora_llegadaFinanciera",
                encrypt(userData.int_idUsuarios.toString()
              ));
         
              localStorage.setItem(
                "fotoPerfil",
                userData.str_RutaFoto
              );
              const { data: perfilData } = await axios.get(
                `${baseSEGURIDAD}perfiles/asignado/aplicacion/${idAplicacion}/usuario/${userData.int_idUsuarios}/`,
                {
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${responseSeguridad.data.token}`,
                  },
                }
              );

              if (perfilData) {
                setRol(perfilData.str_Nombre);
                Cookies.set("rolFinanciera",encrypt(perfilData.str_Nombre));
                 
                setVerificado(true);
                  localStorage.setItem("isVerifiedFinanciera", "true");
              } else {
                logout();
              }
            } catch (error) {
              if (error.response?.status === 401) {
                localStorage.removeItem("isVerifiedFinanciera");
                VerificacionSeguridad();
              } else {
                console.error("Error al verificar aplicaciones:", error);
              }
            }
          }
        }
        // Obtener Empresa y guardar en cookie
        const responseEmpresa = await axios.get(API_GESTOR.ObtenerEmpresas(idAplicacion, suscriptor));
        console.log("AppRoutes responseEmpresa:",responseEmpresa)
        Cookies.set("busFinanciera", encrypt(responseEmpresa.data[0].int_idEmpresa.toString()));
        // Guardar en cookies moneda y simbolo
        Cookies.set("monedaFinanciera", encrypt(responseEmpresa.data[0].str_Moneda));
        Cookies.set("simbMonedaFinanciera", encrypt(responseEmpresa.data[0].str_SimboloMoneda));
        console.log("AppRoutes responseEmpresa.data[0]:",responseEmpresa.data[0])
      } catch (error) {
        console.error(error);

        // window.location.replace("https://qaseguridad.greta.pe/");
      }
    };

    VerificacionSeguridad();
  }, [ipAddress]);

  const VerificarSesion = async () => {
    const response = await checkSesion(sesion);

    if (response.data.dt_FechaCierre != null) {
      Swal.fire({
        title: "Fin de sesión",
        text: "Su sesión se ha cerrado",
        icon: "info",
        allowOutsideClick: false,
        confirmButtonText: "OK",
      }).then(() => {
        logout();
      });
    }else{
      console.log("ok")
    }
  };
  useEffect(() => {
    const interval = setInterval(() => {
      VerificarSesion();
    }, 5000);

    return () => clearInterval(interval);
  }, []);
  return (
    <BrowserRouter>
      <Routes>
        {verificado && (
          <>
            <Route path="/*" element={<PrivateRoutes perfil={rol} />} />
            <Route index element={<Navigate to="/Financiera/Inicio" />} />
          </>
        )}
      </Routes>
    </BrowserRouter>
  );
};

export { AppRoutes };