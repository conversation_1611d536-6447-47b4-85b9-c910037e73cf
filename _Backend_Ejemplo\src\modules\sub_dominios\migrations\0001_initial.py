# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('dominios', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SubDominio',
            fields=[
                ('int_idSubDominio', models.AutoField(primary_key=True, serialize=False)),
                ('str_nombre', models.CharField(max_length=255)),
                ('int_idDominio', models.ForeignKey(db_column='int_idDominio', on_delete=django.db.models.deletion.CASCADE, related_name='sub_dominios', to='dominios.dominio')),
            ],
            options={
                'verbose_name': 'sub_dominio',
                'verbose_name_plural': 'sub_dominios',
                'db_table': 'tr_sub_dominios',
                'managed': True,
            },
        ),
    ]
