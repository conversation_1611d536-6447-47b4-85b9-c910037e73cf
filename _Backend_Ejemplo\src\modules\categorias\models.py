from django.db import models
from src.modules.suscripciones.models import Suscripcion


# Create your models here.
class Categoria(models.Model):
    class Meta:
        db_table = "tm_categoria"
        managed = True
        verbose_name = "categoria"
        verbose_name_plural = "categorias"

    int_idCategoria = models.AutoField(primary_key=True)
    str_nombreCategoria = models.CharField(max_length=200)
    str_idSuscripcion = models.ForeignKey(
        Suscripcion,
        on_delete=models.CASCADE,
        db_column="str_idSuscripcion",
        related_name="categorias",
    )
