# Generated by Django 5.1 on 2025-03-31 17:04

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Usuario',
            fields=[
                ('int_idUsuarios', models.AutoField(primary_key=True, serialize=False)),
                ('str_Nombres', models.Char<PERSON>ield(max_length=254)),
                ('str_Apellidos', models.Char<PERSON>ield(max_length=255)),
                ('str_Correo', models.Char<PERSON>ield(max_length=150)),
                ('str_Documento', models.CharField(max_length=20)),
                ('str_UnidadNegocio', models.Char<PERSON>ield(max_length=255)),
                ('str_Clave', models.Char<PERSON>ield(max_length=255)),
                ('int_idEspecialidad', models.IntegerField()),
                ('int_Estado', models.BooleanField()),
                ('str_Codigo', models.<PERSON><PERSON><PERSON><PERSON>(max_length=8)),
                ('dt_FechaCreacion', models.DateTimeField()),
                ('dt_FechaModificacion', models.DateTimeField(null=True)),
                ('int_idUsuarioCreacion', models.IntegerField()),
                ('int_idUsuarioModificacion', models.IntegerField(null=True)),
            ],
            options={
                'verbose_name': 'Usuario',
                'verbose_name_plural': 'Usuarios',
                'db_table': 'tm_usuarios',
                'managed': True,
            },
        ),
    ]
