from rest_framework import serializers
from src.modules.ratios_ef.models import RatioEF

class RatioEFSerializer(serializers.ModelSerializer):
    class Meta:
        model = RatioEF
        fields = '__all__'

class RatioEFSimpleSerializer(serializers.Serializer):
    nombre_ratio = serializers.CharField()
    valor_ratio = serializers.FloatField()
    formula_ratio = serializers.CharField()
    variacion = serializers.CharField(allow_null=True, required=False)
