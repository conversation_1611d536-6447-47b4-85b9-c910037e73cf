from django.db import models
from src.modules.estado_financiero.models import EstadoFinanciero
from src.modules.cuentas.models import Cuenta

class EFCuenta(models.Model):
    class Meta:
        db_table = 'tr_ef_cuentas'
        managed = True
        verbose_name = 'ef cuenta'
        verbose_name_plural = 'ef cuentas'

    int_idValor = models.AutoField(primary_key=True)
    int_idEstadoFinanciero = models.ForeignKey(EstadoFinanciero, on_delete=models.CASCADE, db_column='int_idEstadoFinanciero', related_name='valores')
    db_valor = models.DecimalField(max_digits=18, decimal_places=2)
    int_idCuenta = models.ForeignKey(Cuenta, on_delete=models.CASCADE, db_column='int_idCuenta', related_name='valores')

    def __str__(self):
        return f"{self.int_idEstadoFinanciero} - {self.int_idCuenta}: {self.db_valor}"