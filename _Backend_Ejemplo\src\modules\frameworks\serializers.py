from rest_framework import serializers
from src.modules.evaluaciones.models import Evaluacion
from src.modules.suscripciones.serializers import SuscripcionSerializer
from src.modules.categorias.serializers import CategoriaSerializer
from src.modules.suscripciones.models import Suscripcion
from .models import Framework


class FrameworkSerializer(serializers.ModelSerializer):
    
    str_idSuscripcion = serializers.CharField(allow_blank=True, required=False, allow_null=True)
    class Meta:
        model = Framework
        fields = "__all__"
        
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["int_idCategoria"] = CategoriaSerializer(instance.int_idCategoria).data
        representation["str_idSuscripcion"] = SuscripcionSerializer(instance.str_idSuscripcion).data
        evaluacion = Evaluacion.objects.filter(int_idFramework_id=instance.int_idFramework)
        representation["bool_editar"] = False if evaluacion else True
        return representation

    def update(self, instance, validated_data):
        # Si viene str_idSuscripcion en los datos, lo procesamos
        if 'str_idSuscripcion' in validated_data:
            suscripcion_id = validated_data.pop('str_idSuscripcion')
            if suscripcion_id:
                try:
                    suscripcion = Suscripcion.objects.get(str_idSuscripcion=suscripcion_id)
                    instance.str_idSuscripcion = suscripcion
                except Suscripcion.DoesNotExist:
                    raise serializers.ValidationError(
                        {"str_idSuscripcion": f"Suscripción con ID {suscripcion_id} no encontrada"}
                    )

        # Actualizamos el resto de los campos
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        instance.save()
        return instance
        
class FrameworkPlantillaSerializer(serializers.Serializer):
    str_idSuscripcion = serializers.CharField()
    str_nombre = serializers.CharField()
    int_idCategoria = serializers.CharField()
    str_descripcion = serializers.CharField()   
