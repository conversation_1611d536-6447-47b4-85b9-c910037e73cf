/**
 * Utilidades para el manejo de fechas sin usar la clase Date
 * Evita problemas de zona horaria que pueden mostrar fechas con un día anterior
 */

/**
 * Convierte una fecha en formato ISO o Date a formato DD/MM/YYYY
 * @param {string|Date} fechaInput - Fecha en formato ISO string o objeto Date
 * @returns {string} Fecha en formato DD/MM/YYYY
 */
export const formatearFechaDDMMYYYY = (fechaInput) => {
  try {
    let fechaString = fechaInput;

    // Si recibimos un objeto Date, convertirlo a string ISO
    if (fechaInput instanceof Date) {
      fechaString = fechaInput.toISOString();
    }

    // Si la fecha viene en formato ISO (YYYY-MM-DD o YYYY-MM-DDTHH:mm:ss.sssZ)
    if (typeof fechaString === 'string') {
      // Extraer solo la parte de la fecha (YYYY-MM-DD)
      const fechaSolo = fechaString.split('T')[0];

      // Dividir la fecha en partes
      const partes = fechaSolo.split('-');

      if (partes.length === 3) {
        const año = partes[0];
        const mes = partes[1].padStart(2, '0');
        const dia = partes[2].padStart(2, '0');
        // console.log("Fecha formateada ISO formatearFechaDDMMYYYY:", `${dia}/${mes}/${año}`);
        return `${dia}/${mes}/${año}`;
      }
    }

    // Si no se puede procesar, devolver fecha recibida en formato DD/MM/YYYY
    let partes = fechaString.split('/');
    if (partes.length === 3) {
      const dia = partes[0].padStart(2, '0');
      const mes = partes[1].padStart(2, '0');
      const año = partes[2];
      // console.log("Fecha formateada DD/MM/YYYY formatearFechaDDMMYYYY:", `${dia}/${mes}/${año}`);
      return `${dia}/${mes}/${año}`;
    } else if (fechaString.includes('-')) {
      // verificar si el separador es un guion 
      partes = fechaString.split('-');
      if (partes.length === 3) {
        const dia = partes[0].padStart(2, '0');
        const mes = partes[1].padStart(2, '0');
        const año = partes[2];
        // console.log("Fecha formateada YYYY-MM-DD formatearFechaDDMMYYYY:", `${dia}/${mes}/${año}`);
        return `${dia}/${mes}/${año}`;
      }
    }

  } catch (error) {
    console.error("Error al formatear fecha:", error);
    // En caso de error, devolver fecha actual
    const hoy = new Date();
    const dia = hoy.getDate().toString().padStart(2, '0');
    const mes = (hoy.getMonth() + 1).toString().padStart(2, '0');
    const año = hoy.getFullYear();
    return `${dia}/${mes}/${año}`;
  }
};

/**
 * Obtiene el nombre del mes en español desde un string de fecha
 * @param {string} fechaString - Fecha en formato ISO string
 * @returns {string} Nombre del mes en español
 */
export const obtenerNombreMesDesdeFecha = (fechaString) => {
  try {
    const meses = [
      "Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio",
      "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"
    ];

    // Extraer solo la parte de la fecha (YYYY-MM-DD)
    const fechaSolo = fechaString.split('T')[0];

    // Dividir la fecha en partes
    const partes = fechaSolo.split('-');

    if (partes.length === 3) {
      const numeroMes = parseInt(partes[1], 10) - 1; // Restar 1 porque los arrays empiezan en 0
      return meses[numeroMes] || "Mes desconocido";
    }

    return "Mes desconocido";
  } catch (error) {
    console.error("Error al obtener nombre del mes:", error);
    return "Mes desconocido";
  }
};

/**
 * Obtiene el nombre del mes en español desde un número (0-11)
 * @param {number} numeroMes - Número del mes (0 = Enero, 11 = Diciembre)
 * @returns {string} Nombre del mes en español
 */
export const obtenerNombreMes = (numeroMes) => {
  const meses = [
    "Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio",
    "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"
  ];
  return meses[numeroMes] || "Mes desconocido";
};

/**
 * Convierte una fecha en formato DD/MM/YYYY a formato ISO (YYYY-MM-DD)
 * @param {string} fechaDDMMYYYY - Fecha en formato DD/MM/YYYY
 * @returns {string} Fecha en formato ISO (YYYY-MM-DD)
 */
export const convertirDDMMYYYYaISO = (fechaDDMMYYYY) => {
  try {
    const partes = fechaDDMMYYYY.split('/');

    if (partes.length === 3) {
      const dia = partes[0].padStart(2, '0');
      const mes = partes[1].padStart(2, '0');
      const año = partes[2];

      return `${año}-${mes}-${dia}`;
    }

    return null;
  } catch (error) {
    console.error("Error al convertir fecha DD/MM/YYYY a ISO:", error);
    return null;
  }
};

/**
 * Valida si una fecha en formato DD/MM/YYYY es válida
 * @param {string} fechaDDMMYYYY - Fecha en formato DD/MM/YYYY
 * @returns {boolean} true si la fecha es válida, false en caso contrario
 */
export const validarFechaDDMMYYYY = (fechaDDMMYYYY) => {
  try {
    const partes = fechaDDMMYYYY.split('/');

    if (partes.length !== 3) return false;

    const dia = parseInt(partes[0], 10);
    const mes = parseInt(partes[1], 10);
    const año = parseInt(partes[2], 10);

    // Validar rangos básicos
    if (dia < 1 || dia > 31) return false;
    if (mes < 1 || mes > 12) return false;
    if (año < 1900 || año > 2100) return false;

    // Validar días por mes
    const diasPorMes = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

    // Verificar año bisiesto
    if (mes === 2 && esAñoBisiesto(año)) {
      diasPorMes[1] = 29;
    }

    return dia <= diasPorMes[mes - 1];
  } catch (error) {
    console.error("Error al validar fecha:", error);
    return false;
  }
};

/**
 * Verifica si un año es bisiesto
 * @param {number} año - Año a verificar
 * @returns {boolean} true si es bisiesto, false en caso contrario
 */
export const esAñoBisiesto = (año) => {
  return (año % 4 === 0 && año % 100 !== 0) || (año % 400 === 0);
};

/**
 * Obtiene el año desde un string de fecha
 * @param {string} fechaString - Fecha en formato ISO string
 * @returns {number} Año extraído de la fecha
 */
export const obtenerAñoDesdeFecha = (fechaString) => {
  try {
    // Extraer solo la parte de la fecha (YYYY-MM-DD)
    const fechaSolo = fechaString.split('T')[0];

    // Dividir la fecha en partes
    const partes = fechaSolo.split('-');

    if (partes.length === 3) {
      return parseInt(partes[0], 10);
    }

    // Si no se puede extraer, devolver año actual
    return new Date().getFullYear();
  } catch (error) {
    console.error("Error al obtener año desde fecha:", error);
    return new Date().getFullYear();
  }
};

/**
 * Obtiene la fecha actual en formato DD/MM/YYYY
 * @returns {string} Fecha actual en formato DD/MM/YYYY
 */
export const obtenerFechaActualDDMMYYYY = () => {
  const hoy = new Date();
  const dia = hoy.getDate().toString().padStart(2, '0');
  const mes = (hoy.getMonth() + 1).toString().padStart(2, '0');
  const año = hoy.getFullYear();
  return `${dia}/${mes}/${año}`;
};
