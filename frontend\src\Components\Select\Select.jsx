
import { ChevronDownIcon } from "@heroicons/react/16/solid";
import React, { useState } from "react";

const Select = ({
  options,
  placeholder = "Seleccionar...",
  onChange,
  className = "",
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);

  const handleSelect = (option) => {
    setSelectedOption(option);
    setIsOpen(false);
    if (onChange) {
      onChange(option);
    }
  };

  return (
    <div className={`relative w-full ${className}`}>
      <button
        type="button"
        disabled={disabled}
        onClick={() => setIsOpen(!isOpen)}
        className={`
          w-full flex items-center justify-between 
          px-4 py-2 text-left 
          border rounded-md 
          poppins-font
          border-[#7BACFF]
          h-[2.5rem]
          ${
            disabled
              ? "bg-gray-100 text-gray-500 cursor-not-allowed"
              : "bg-white hover:bg-gray-50"
          }
        `}
      >
        <span className={`text-[#91A8E2]`}>
          {selectedOption ? selectedOption.label : placeholder}
        </span>

        <ChevronDownIcon
          className={`w-5 h-5 transition-transform text-[#91A8E2]
            ${isOpen ? "rotate-180" : ""} 
            ${disabled ? "opacity-50" : ""}`}
        />
      </button>

      {isOpen && !disabled && (
        <div
          className="absolute z-10 w-full mt-1 
          bg-white border border-[#D0D5DD] rounded-md 
          shadow-lg max-h-60 overflow-y-auto"
        >
          {options.map((option) => (
            <div
              key={option.value}
              onClick={() => handleSelect(option)}
              className={`
                px-4 py-2 
                text-[#91A8E2]
                cursor-pointer 
                hover:bg-[#F8FAFB]
                poppins-font
              `}
            >
              {option.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Select;
