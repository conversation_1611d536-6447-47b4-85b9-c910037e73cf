# Generated by Django 5.2.1 on 2025-05-14 12:34

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Usuario',
            fields=[
                ('int_idUsuarios', models.AutoField(primary_key=True, serialize=False)),
                ('str_Nombres', models.Char<PERSON>ield(max_length=255)),
                ('str_Apellidos', models.Char<PERSON>ield(max_length=255)),
                ('str_Correo', models.Char<PERSON>ield(max_length=150)),
                ('str_Documento', models.Char<PERSON>ield(max_length=20)),
                ('str_UnidadNegocio', models.Char<PERSON><PERSON>(max_length=255)),
                ('str_Clave', models.CharField(max_length=255)),
                ('int_idEspecialidad', models.IntegerField()),
                ('int_Estado', models.IntegerField(default=1)),
                ('str_Codigo', models.<PERSON><PERSON><PERSON><PERSON>(max_length=8, null=True)),
                ('dt_FechaCreacion', models.DateTimeField()),
                ('dt_FechaModificacion', models.DateTimeField(null=True)),
                ('int_idUsuarioCreacion', models.IntegerField()),
                ('int_idUsuarioModificacion', models.IntegerField(null=True)),
            ],
            options={
                'verbose_name': 'usuario',
                'verbose_name_plural': 'usuarios',
                'db_table': 'tm_usuarios',
                'managed': True,
            },
        ),
    ]
