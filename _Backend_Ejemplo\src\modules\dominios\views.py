import os
from typing import Any
from django.http import HttpResponse
from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from src.utils.classes import Response as APIResponse
from rest_framework.decorators import api_view
from drf_yasg.utils import swagger_auto_schema
from .models import Dominio
from .serializers import DominioSerializer
from .controller import DominioController
from rest_framework.decorators import action
from django.db.models import Q
import requests
from src.utils.classes import Config

from drf_yasg import openapi


class DominioView(viewsets.ModelViewSet):
    queryset = Dominio.objects.all()
    serializer_class = DominioSerializer
    controller = DominioController()
    permission_classes = [permissions.AllowAny]
    http_method_names = ["get", "post", "patch", "delete"]

    @swagger_auto_schema(
        operation_description="Endpoint para obtener dominios por framework",
        responses={200: DominioSerializer(many=True)},
    )
    @action(detail=False, methods=["get"], url_path="framework/<int:framework_id>")
    def dominio_by_framework(self, request, framework_id, *args, **kwargs):
        try:
            response: APIResponse = self.controller.get_by_framework(framework_id)
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(
                data=DominioSerializer(response.data, many=True).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)
  