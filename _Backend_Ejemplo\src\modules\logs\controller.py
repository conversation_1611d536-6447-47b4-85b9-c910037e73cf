import json
from .models import Log
from src.modules.usuarios.models import Usuario # Asegúrate que la ruta sea correcta
from src.utils.classes import Response as CustomResponse # Importamos la clase Response personalizada

class LogController:
    """
    Controlador lógico INTERNO para gestionar operaciones de Logs.
    Retorna objetos CustomResponse directamente.
    """

    def get_all_logs(self):
        """
        Obtiene todos los registros de logs como un QuerySet.
        """
        try:
            logs = Log.objects.all()
            return CustomResponse(data=logs, state=True, message="Logs obtenidos correctamente.")
        except Exception as e:
            return CustomResponse(message=f"Error al obtener todos los logs: {str(e)}", data=e, state=False)

    def get_log_by_id(self, id:int):
        """
        Obtiene un registro de log específico por su ID (id).
        """
        try:
            log = Log.objects.get(pk=id)
            return CustomResponse(message="Log obtenido correctamente", data=log, state=True)
        except Log.DoesNotExist:
            return CustomResponse(message=f"Log con ID {id} no encontrado.", state=False)
        except Exception as e:
            return CustomResponse(message=f"Error al obtener el log con ID {id}: {str(e)}", data=e, state=False)

    def create_log(self, log_data: dict):
        """
        Crea un nuevo registro de log a partir de un diccionario de datos.
        Espera un diccionario con las claves necesarias:
        'int_idUsuarios', 'str_operacion', 'str_tabla', 'str_idObjeto', 'str_objeto'
        """
        try:
            required_fields = ['int_idUsuarios', 'str_operacion', 'str_tabla', 'str_idObjeto', 'str_objeto']
            missing_fields = [field for field in required_fields if field not in log_data]
            if missing_fields:
                 return CustomResponse(message=f"Faltan campos requeridos en log_data: {', '.join(missing_fields)}", state=False)

            try:
                usuario_id = log_data['int_idUsuarios']
                # Validar si usuario_id es una instancia de Usuario o un ID
                if isinstance(usuario_id, Usuario):
                    usuario = usuario_id
                else:
                    usuario = Usuario.objects.get(pk=usuario_id)
            except Usuario.DoesNotExist:
                return CustomResponse(message=f"Usuario con ID {usuario_id} no encontrado.", state=False)
            except KeyError:
                 return CustomResponse(message="Falta 'int_idUsuarios' en log_data.", state=False)
            except ValueError:
                 return CustomResponse(message="'int_idUsuarios' debe ser un ID válido o una instancia de Usuario.", state=False)


            new_log = Log.objects.create(
                int_idUsuarios=usuario,
                str_operacion=log_data['str_operacion'],
                str_tabla=log_data['str_tabla'],
                str_idObjeto=log_data['str_idObjeto'],
                str_objeto=log_data['str_objeto']
            )
            return CustomResponse(message="Log creado correctamente", data=new_log, state=True)

        except Exception as e:
            # Captura errores más generales durante la creación o validación
            return CustomResponse(message=f"Error general al crear el log: {str(e)}", data=e, state=False)

# Ejemplo de uso interno (esto no iría en el controller.py usualmente):
# if __name__ == '__main__': # Solo para demostración
#     # Necesitarías configurar Django settings para que esto funcione fuera del runserver
#     controller = LogController()
#
#     # Obtener todos
#     response_all = controller.get_all_logs()
#     if response_all.state:
#         print("Todos los logs:", response_all.data)
#     else:
#         print("Error:", response_all.message)
#
#     # Crear uno nuevo (asumiendo que existe un usuario con ID 1)
#     new_log_data = {
#         'int_idUsuarios': 1,
#         'str_operacion': 'CREATE',
#         'str_tabla': 'some_table',
#         'str_idObjeto': 123,
#         'str_objeto': '{"key": "value"}'
#     }
#     response_create = controller.create_log(new_log_data)
#     if response_create.state:
#         print("Log creado:", response_create.data)
#         log_id = response_create.data.int_idLog
#
#         # Obtener por ID
#         response_one = controller.get_log_by_id(log_id)
#         if response_one.state:
#             print("Log por ID:", response_one.data)
#         else:
#             print("Error:", response_one.message)
#     else:
#         print("Error al crear:", response_create.message)