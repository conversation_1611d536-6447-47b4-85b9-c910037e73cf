# Generated by Django 5.1 on 2025-04-01 10:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('suscripciones', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Categoria',
            fields=[
                ('int_idCategoria', models.AutoField(primary_key=True, serialize=False)),
                ('str_nombreCategoria', models.CharField(max_length=200)),
                ('str_idSuscripcion', models.ForeignKey(db_column='str_idSuscripcion', on_delete=django.db.models.deletion.CASCADE, related_name='categorias', to='suscripciones.suscripcion')),
            ],
            options={
                'verbose_name': 'categoria',
                'verbose_name_plural': 'categorias',
                'db_table': 'tm_categoria',
                'managed': True,
            },
        ),
    ]
