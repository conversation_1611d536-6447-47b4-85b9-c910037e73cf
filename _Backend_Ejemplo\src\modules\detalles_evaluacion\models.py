from django.db import models

from src.modules.evaluados.models import Evaluado
from src.modules.controles.models import Control
from src.modules.evaluaciones.models import Evaluacion


# Create your models here.
class DetalleEvaluacion(models.Model):

    class Meta:
        db_table = "tr_detalles_evaluacion"
        managed = True
        verbose_name = "detalle_evaluacion"
        verbose_name_plural = "detalle_evaluaciones"

    int_idDetalleEvaluacion = models.AutoField(primary_key=True)
    int_idEvaluacion = models.ForeignKey(
        Evaluacion,
        on_delete=models.CASCADE,
        db_column="int_idEvaluacion",
        related_name="evaluacion",
    )
    int_idControl = models.ForeignKey(
        Control,
        on_delete=models.CASCADE,
        db_column="int_idControl",
        related_name="control",
    )
    int_idEvaluado = models.ForeignKey(
        Evaluado,
        on_delete=models.CASCADE,
        db_column="int_idEvaluado",
        related_name="evaluado",
        null=True,
    )
    str_valor_tobe = models.CharField(max_length=3, null=True)
    str_valor_asis = models.CharField(max_length=3, null=True)
    # int_tipo = models.SmallIntegerField()
    # bool_estado = models.BooleanField()
