from datetime import datetime, timedelta
import re
import jwt

from .security import JWT
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin


class JWTMiddleware(MiddlewareMixin):

    def process_request(self, request):
        #Rutas excluídas del middleware
        EXCLUDED_PATHS = [
            "^/$",
            r"/monitor/$",
            r"/detalles_monitor/$",
            r"/cuentas_cobrar/upload/$",
            r"/extractor/upload/$",
            r"/stocks/upload/$",
            r"/clientes/subir_plantilla/$",
            r"/monitor/[\d]+/$",
            r"/monitor/[\d]+/estado/$",
            "^/swagger/$",
        ]
        current_path = request.path_info

        
        for pattern in EXCLUDED_PATHS:
            if re.match(pattern, current_path):
                return None

        jwt_obj = JWT()
        token = None
        auth_header = request.headers.get("Authorization", None)
        if auth_header:
            auth_parts = auth_header.split()
            if len(auth_parts) == 2 and auth_parts[0] == "Bearer":
                token = auth_parts[1]

        if not token:
            return JsonResponse({"message": "Token is missing!"}, status=401)

        # Decodificar y validar el token
        payload = jwt_obj.decode_token(token)
        if "error" in payload:
            return JsonResponse(payload, status=401)
        return None
