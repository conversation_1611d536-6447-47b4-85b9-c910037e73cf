const BASE_URL = import.meta.env.VITE_BASE_URL;
const BASE_URL8000 = import.meta.env.VITE_SEGURIDAD_URL;
const BASE_PROCESOS = import.meta.env.VITE_BASE_URL_PROCESOS;

type Suscripcion = string | number | undefined;
type IdUsuario = string | number;

type IdAplicacion = string | number;

const API_GESTOR = {
  ObtenerEmpresas: (
    idAplicacion: IdAplicacion,
    Suscriptor: Suscripcion
  ): string =>
    `${BASE_URL8000}empresas/asignadas/aplicacion/${idAplicacion}/suscriptor/${Suscriptor}/`,
  ObtenerUsuarios: (suscripcion: Suscripcion): string =>
    `${BASE_URL8000}usuarios/suscripcion/${suscripcion}/`,

  /**
   * API del Módulo de Estados Financieros
   */

  // Upload de archivos Excel de estados financieros
  UploadEstadosFinancierosExcel: (): string =>
    `${BASE_URL}estados_financieros/upload/excel/`,

  // Eliminar estado financiero por ID
  DeleteEstadoFinanciero: (id_estado_financiero: Suscripcion): string =>
    `${BASE_URL}estados_financieros/${id_estado_financiero}/`,

  // Obtener periodos de estados financieros por empresa y tipo de periodo
  EstadosFinancierosPeriodos: (id_empresa: Suscripcion, tipo_periodo: Suscripcion): string =>
    `${BASE_URL}estados_financieros/empresa/${id_empresa}/tipo-periodo/${tipo_periodo}/solo-periodos/`,

  // Guardar simulación de estado financiero
  GuardarSimulacion: (): string =>
    `${BASE_URL}estados_financieros/simulacion/`,

  /**
   * API del Módulo de Total Agrupador
   */

  // Total Agrupador. Tipo Periodo: 1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario
  TotalAgrupadorByEmpresaYPeriodo: (id_empresa: Suscripcion, tipo_periodo: Suscripcion): string =>
    `${BASE_URL}total_agrupador/ultimos-periodos/empresa/${id_empresa}/tipo-periodo/${tipo_periodo}/`,

  // Total Agrupador por mes específico en vista anual
  TotalAgrupadorPorMesAnual: (id_empresa: Suscripcion, mes_id: Suscripcion): string =>
    `${BASE_URL}total_agrupador/ultimos-periodos/empresa/${id_empresa}/periodo-mensual-anual/mes/${mes_id}/`,

  // Total Agrupador para periodo específico con últimos periodos
  TotalAgrupadorPeriodoEspecificoUltimosPeriodos: (id_empresa: Suscripcion, tipo_periodo: Suscripcion, fecha_fin: string): string =>
    `${BASE_URL}total_agrupador/periodo-especifico/ultimos-periodos/empresa/${id_empresa}/tipo-periodo/${tipo_periodo}/fecha-fin/${fecha_fin}/`,

  // Total Agrupador para periodo específico
  TotalAgrupadorPeriodoEspecifico: (id_empresa: Suscripcion, tipo_periodo: Suscripcion, fecha_fin: string): string =>
    `${BASE_URL}total_agrupador/periodo-especifico/empresa/${id_empresa}/tipo-periodo/${tipo_periodo}/fecha-fin/${fecha_fin}/`,


  /**
   * API del Módulo de Ratios Financieros
   */

  // Obtener ratios financieros por periodo específico
  RatiosEFPeriodoEspecifico: (id_empresa: Suscripcion, tipo_periodo: Suscripcion, fecha_fin: string): string =>
    `${BASE_URL}ratios_ef/periodo-especifico/empresa/${id_empresa}/tipo-periodo/${tipo_periodo}/fecha-fin/${fecha_fin}/`,

  /**
   * API del Módulo de Cuentas de los estados financieros
   */

  // Obtener cuentas de estados financieros por empresa, tipo de periodo y fecha fin
  EFCuentasPorEmpresaPeriodo: (id_empresa: Suscripcion, tipo_periodo: Suscripcion, fecha_fin: string): string =>
    `${BASE_URL}ef_cuentas/empresa/${id_empresa}/tipo-periodo/${tipo_periodo}/fecha-fin/${fecha_fin}/`,

};

export default API_GESTOR;
