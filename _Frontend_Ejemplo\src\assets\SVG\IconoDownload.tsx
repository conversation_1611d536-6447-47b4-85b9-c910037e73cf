import React from "react";


const IconoDownload = ({onClick,size,color} ) => {
  return (
    <svg onClick={onClick ? onClick : ()=> {}} viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" width={size} height={size}>
      <title />
      <g data-name="1" id="_1">
      <path d="M255.13,385.54a15,15,0,0,1-11.14-5L103.67,224.93a15,15,0,0,1,11.14-25H171V63a15,15,0,0,1,15-15H324.3a15,15,0,0,1,15,15V199.89h56.16a15,15,0,0,1,11.14,25L266.27,380.58A15,15,0,0,1,255.13,385.54ZM148.53,229.89l106.6,118.25L361.74,229.89H324.3a15,15,0,0,1-15-15V78H201V214.89a15,15,0,0,1-15,15Z" fill={color} />
      <path d="M390.84,450H119.43a65.37,65.37,0,0,1-65.3-65.29V346.54a15,15,0,0,1,30,0v38.17A35.34,35.34,0,0,0,119.43,420H390.84a35.33,35.33,0,0,0,35.29-35.29V346.54a15,15,0,0,1,30,0v38.17A65.37,65.37,0,0,1,390.84,450Z" fill={color}  />
      </g>
    </svg>
  );
};

export default IconoDownload;
