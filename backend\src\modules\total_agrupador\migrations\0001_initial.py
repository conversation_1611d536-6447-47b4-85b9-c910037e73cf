# Generated by Django 5.2.1 on 2025-05-14 12:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('agrupadores', '0001_initial'),
        ('estado_financiero', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TotalAgrupador',
            fields=[
                ('int_idTotalAgrupador', models.AutoField(primary_key=True, serialize=False)),
                ('db_resultadoAgrupador', models.DecimalField(decimal_places=2, max_digits=18)),
                ('int_idAgrupador', models.ForeignKey(db_column='int_idAgrupador', on_delete=django.db.models.deletion.CASCADE, related_name='totales', to='agrupadores.agrupador')),
                ('int_idEstadoFinanciero', models.ForeignKey(db_column='int_idEstadoFinanciero', on_delete=django.db.models.deletion.CASCADE, related_name='totales_agrupadores', to='estado_financiero.estadofinanciero')),
            ],
            options={
                'verbose_name': 'total agrupador',
                'verbose_name_plural': 'totales agrupadores',
                'db_table': 'tr_totalagrupador',
                'managed': True,
            },
        ),
    ]
