Quiero crear en el backend un nuevo endpoint GET /ef_cuentas/empresa/{id_empresa}/tipo-periodo/{tipo_periodo}/fecha-fin/{fecha_fin}/

Donde debe devolver por cada tipo de estado financiero y dentro de este por cada agrupador todas las cuentas (nombre cuenta, codigo cuenta, valor cuenta, variación y proporción) del periodo dado.

Buscar los estados financieros por el id empresa, tipo periodo y fecha fin periodo.
Con los estados financieros obtenidos buscar en la tabla ef_cuentas y obtener todas las cuentas. 
Agregar en el tipo Estado financiero 1 el agrupador activos totales (su valor se calcula sumando activo corriente y no corriente) y pasivos totales (su valor se calcula sumando pasivo corriente y no corriente).
Tambien calcular el "analisis horizontal" (que es variación) que es respecto al periodo anterior (si existe) y
Calcular el "analisis vertical" (que es proporción) que es respecto a un agrupador especial:
- Para el tipo de estado financiero = 1:
a) Activos: el "analisis vertical" se logra al obtener la proporcion porcentual del valor de cada cuenta respecto al valor de activos totales que es la suma del valor de los subagrupadores activo corriente y no corriente
b) Pasivos y Patrimonio: el "analisis vertical" se logra al obtener la proporcion porcentual del valor de cada cuenta (tanto de los pasivos y del patrimonio) respecto al valor de la suma del pasivo total (que es la suma del valor de los subagrupadores pasivo corriente y no corriente) y el patrimonio
- Para el tipo de estado financiero = 2:
a) El agrupador "ventas netas" es la refencia (el 100%). Se calcula la proporción de todas las cuentas y agrupadores respecto al valor del agrupador ventas netas.

Quiero que devuelva algo así:
[
        {
            "tipo_estado_financiero": 1,
            "id_estado_financiero":  1,
            "agrupadores": [
                 {
                     "nombre_agrupador" : "activos",
                     "nombre_subagrupador": "activo corriente",
                     "cuentas": [
                         {
                             "nombre_cuenta": "nombre",
                             "codigo_cuenta": "42115",
                             "valor_cuenta": 616.02,
                             "variacion": 1.2,
                             "proporcion": 1.2
                         },
                     ],
                     "valor": 1265.03,
                     "variacion": 1.2,
                     "proporcion": 1.2
                 },
                 {
                     "nombre_agrupador" : "activos totales",
                     "nombre_subagrupador": null,
                     "cuentas": null,
                     "valor": 12,
                     "variacion": 1.2,
                     "proporcion": 1.2
                 },
            ]
        },
        {
            "tipo_estadofinanciero": 2,
            "id_estado_financiero":  3,
            "agrupadores": [
                 {
                     "nombre_agrupador" : "ventas netas",
                     "nombre_subagrupador": null,
                     "cuentas": [
                         {
                             "nombre_cuenta": "nombre",
                             "codigo_cuenta": "42115",
                             "valor_cuenta": 616.02,
                             "variacion": 1.2,
                             "proporcion": 1.2
                         },
                     ],
                     "valor": 12945.20,
                     "variacion": 1.2,
                     "proporcion": 1.2
                 },
            ]
        },
]