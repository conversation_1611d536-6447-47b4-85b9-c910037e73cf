import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { RoutesPrivate } from "../../Routes/ProtectedRoute";
import { useGoTo } from "../../Services/Globales";

const Stepper = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const ruta = location.pathname;
  const { goTo } = useGoTo();
  return (
    <ol className="flex items-start justify-start md:w-[60%] w-full gap-4 text-xs font-medium text-center text-gray-500 dark:text-gray-400 sm:text-base">
      <li
        className={`flex md:w-full items-center  sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700  ${
          ruta === RoutesPrivate.CREAREVALUACION
            ? "after:border-orange-200 dark:after:border-orange-400 text-orange-400 dark:text-orange-400"
            : ruta === RoutesPrivate.ASIS ||
              ruta === RoutesPrivate.TOBE ||
              ruta === RoutesPrivate.RESULTADO
            ? "after:border-green-200 dark:after:border-green-400 text-green-400 dark:text-green-400"
            : " "
        } cursor-pointer`}
        onClick={() => goTo(RoutesPrivate.CREAREVALUACION)}
      >
        <span className="flex flex-col items-center gap-2 whitespace-nowrap   sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500">
          {ruta === RoutesPrivate.CREAREVALUACION ? (
            <span class="flex items-center justify-center w-8 h-8 border border-orange-400 rounded-full shrink-0 dark:border-orange-400  ">
              1
            </span>
          ) : ruta === RoutesPrivate.ASIS ||
            ruta === RoutesPrivate.RESULTADO ||
            ruta === RoutesPrivate.TOBE ? (
            <span class="flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full lg:h-8 lg:w-8 dark:bg-[#47D691] shrink-0">
              <svg
                class="w-3.5 h-3.5 text-white lg:w-4 lg:h-4 dark:text-white"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 16 12"
              >
                <path
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M1 5.917 5.724 10.5 15 1.5"
                />
              </svg>
            </span>
          ) : (
            <span class="flex items-center justify-center w-8 h-8 border border-gray-400 rounded-full shrink-0 dark:border-gray-400  ">
            1
          </span>
          )}
          Definición
        </span>
      </li>
      <li
        className={`flex md:w-full items-center  sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700  ${
          ruta === RoutesPrivate.TOBE
            ? "after:border-orange-200 dark:after:border-orange-400 text-orange-400 dark:text-orange-400"
            : ruta === RoutesPrivate.ASIS || ruta === RoutesPrivate.RESULTADO
            ? "after:border-green-200 dark:after:border-green-400 text-green-400 dark:text-green-400"
            : " "
        } cursor-pointer`}
        onClick={() => goTo(RoutesPrivate.TOBE)}
      >
        <span className="flex flex-col items-center gap-2 whitespace-nowrap   sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500">
          {ruta === RoutesPrivate.TOBE ? (
            <span class="flex items-center justify-center w-8 h-8 border border-orange-400 rounded-full shrink-0 dark:border-orange-400  ">
              2
            </span>
          ) : ruta === RoutesPrivate.ASIS ||
            ruta === RoutesPrivate.RESULTADO ? (
              <span class="flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full lg:h-8 lg:w-8 dark:bg-[#47D691] shrink-0">
              <svg
                class="w-3.5 h-3.5 text-white lg:w-4 lg:h-4 dark:text-white"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 16 12"
              >
                <path
                  stroke="currentColor"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M1 5.917 5.724 10.5 15 1.5"
                />
              </svg>
            </span>
          ) : (
            <span class="flex items-center justify-center w-8 h-8 border border-gray-400 rounded-full shrink-0 dark:border-gray-400  ">
            2
          </span>
          )}
          To Be
        </span>
      </li>
      <li
        className={`flex md:w-full items-center  sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700  ${
          ruta === RoutesPrivate.ASIS
            ? "after:border-orange-200 dark:after:border-orange-400 text-orange-400 dark:text-orange-400"
            : ruta === RoutesPrivate.RESULTADO
            ? "after:border-green-200 dark:after:border-green-400 text-green-400 dark:text-green-400"
            : " "
        } cursor-pointer`}
        onClick={() => goTo(RoutesPrivate.ASIS)}
      >
        <span className="flex flex-col items-center gap-2 whitespace-nowrap   sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500">
          {ruta === RoutesPrivate.ASIS ? (
            <span class="flex items-center justify-center w-8 h-8 border border-orange-400 rounded-full shrink-0 dark:border-orange-400  ">
              3
            </span>
          ) : ruta === RoutesPrivate.RESULTADO ? (
            <span class="flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full lg:h-8 lg:w-8 dark:bg-[#47D691] shrink-0">
            <svg
              class="w-3.5 h-3.5 text-white lg:w-4 lg:h-4 dark:text-white"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 16 12"
            >
              <path
                stroke="currentColor"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M1 5.917 5.724 10.5 15 1.5"
              />
            </svg>
          </span>
          ) : (
            <span class="flex items-center justify-center w-8 h-8 border border-gray-400 rounded-full shrink-0 dark:border-gray-400  ">
            3
          </span>
          )}
          As Is
        </span>
      </li>
      <li
        className={`flex flex-col items-center gap-2 whitespace-nowrap ${
          ruta === RoutesPrivate.RESULTADO
            ? "text-orange-400 dark:text-orange-400"
            : " "
        } cursor-pointer`}
        onClick={() => goTo(RoutesPrivate.RESULTADO)}
      >
        {ruta === RoutesPrivate.RESULTADO ? (
          <span class="flex items-center justify-center w-8 h-8 border border-orange-400 rounded-full shrink-0 dark:border-orange-400  ">
            4
          </span>
        ) : (
          <span class="flex items-center justify-center w-8 h-8 border border-gray-400 rounded-full shrink-0 dark:border-gray-400  ">
          4
        </span>
        )}
        Resultado
      </li>
    </ol>
  );
};

export default Stepper;
