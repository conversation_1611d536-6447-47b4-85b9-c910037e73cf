from django.db import models

from src.modules.detalles_evaluacion.models import DetalleEvaluacion


# Create your models here.
class Evidencia(models.Model):
    class Meta:
        db_table = "tr_evidencias"
        managed = True
        verbose_name = "evidencia"
        verbose_name_plural = "evidencias"

    int_idEvidencia = models.AutoField(primary_key=True)
    str_nombre = models.CharField(max_length=255)
    str_peso = models.CharField(max_length=255)
    str_ruta = models.CharField(max_length=255)
    str_extension = models.Char<PERSON>ield(max_length=255)
    int_idDetalleEvaluacion = models.ForeignKey(
        DetalleEvaluacion,
        models.CASCADE,
        related_name="evidencias",
        db_column="int_idDetalleEvaluacion",
    )
