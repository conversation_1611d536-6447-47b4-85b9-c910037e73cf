import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from datetime import datetime, timedelta

def crear_excel_estado_resultados():
    """
    Crea un archivo Excel con la estructura del Estado de Resultados
    que puede ser procesado por la función procesar_archivo_excel.
    """
    # Crear un nuevo libro de Excel
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Estado de Resultados"

    # Configurar estilos
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
    agrupador_fill = PatternFill(start_color="000000", end_color="000000", fill_type="solid")
    cuenta_header_fill = PatternFill(start_color="70AD47", end_color="70AD47", fill_type="solid")
    cuenta_fill = PatternFill(start_color="FFCC99", end_color="FFCC99", fill_type="solid")
    
    center_alignment = Alignment(horizontal='center', vertical='center')
    border = Border(
        top=Side(style="thin", color="000000"),
        bottom=Side(style="thin", color="000000"),
        left=Side(style="thin", color="000000"),
        right=Side(style="thin", color="000000"),
    )

    # Encabezados de la primera sección
    ws["A1"] = "[Tipo Estado Financiero]"
    ws["B1"] = "2"  # 2 para Estado de Resultados

    ws["A2"] = "[Tipo Periodo]"
    ws["B2"] = "3"  # 3 para Mensual

    # Fechas para el periodo (mes actual)
    fecha_fin = datetime.now().replace(day=1) - timedelta(days=1)  # Último día del mes anterior
    fecha_inicio = fecha_fin.replace(day=1)  # Primer día del mes anterior
    
    ws["A3"] = "[Fecha Inicio Periodo]"
    ws["B3"] = fecha_inicio.strftime("%d/%m/%Y")
    
    ws["A4"] = "[Fecha Fin Periodo]"
    ws["B4"] = fecha_fin.strftime("%d/%m/%Y")

    # Aplicar estilos a los encabezados
    for row in range(1, 5):
        ws[f"A{row}"].font = header_font
        ws[f"A{row}"].fill = header_fill
        ws[f"A{row}"].alignment = center_alignment
        ws[f"A{row}"].border = border
        ws[f"B{row}"].border = border

    # Sección de agrupador - Venta Total
    ws["A6"] = "[# Agrupador]"
    ws["A6"].font = header_font
    ws["A6"].fill = agrupador_fill
    ws["A6"].alignment = center_alignment
    ws["A6"].border = border
    
    ws["A7"] = "Venta Total"
    ws["A7"].fill = cuenta_fill
    ws["A7"].border = border
    
    # Sección de cuentas de Venta Total
    ws["A8"] = "[Nombre Cuenta]"
    ws["B8"] = "[Codigo Cuenta]"
    ws["C8"] = "[Valor Monetario]"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}8"].font = header_font
        ws[f"{col}8"].fill = cuenta_header_fill
        ws[f"{col}8"].alignment = center_alignment
        ws[f"{col}8"].border = border
    
    ws["A9"] = "Ingreso de actividades ordinarias"
    ws["B9"] = "70000"
    ws["C9"] = "26,822,881.00"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}9"].fill = cuenta_fill
        ws[f"{col}9"].border = border
    
    ws["A10"] = "[# Total - Agrupador]"
    ws["C10"] = "26,822,881.00"
    
    for col in ['A', 'C']:
        ws[f"{col}10"].font = Font(bold=True)
        ws[f"{col}10"].border = border
    
    # Sección de agrupador - Costo de Ventas
    ws["A12"] = "[# Agrupador]"
    ws["A12"].font = header_font
    ws["A12"].fill = agrupador_fill
    ws["A12"].alignment = center_alignment
    ws["A12"].border = border
    
    ws["A13"] = "Costo de Ventas"
    ws["A13"].fill = cuenta_fill
    ws["A13"].border = border
    
    # Sección de cuentas de Costo de Ventas
    ws["A14"] = "[Nombre Cuenta]"
    ws["B14"] = "[Codigo Cuenta]"
    ws["C14"] = "[Valor Monetario]"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}14"].font = header_font
        ws[f"{col}14"].fill = cuenta_header_fill
        ws[f"{col}14"].alignment = center_alignment
        ws[f"{col}14"].border = border
    
    ws["A15"] = "Costos de ventas"
    ws["B15"] = "60100"
    ws["C15"] = "-22,910,671.00"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}15"].fill = cuenta_fill
        ws[f"{col}15"].border = border
    
    ws["A16"] = "[# Total - Agrupador]"
    ws["C16"] = "-22,910,671.00"
    
    for col in ['A', 'C']:
        ws[f"{col}16"].font = Font(bold=True)
        ws[f"{col}16"].border = border
    
    # Utilidad bruta
    ws["A18"] = "Utilidad bruta"
    ws["C18"] = "3,912,210.00"
    
    for col in ['A', 'C']:
        ws[f"{col}18"].font = Font(bold=True)
        ws[f"{col}18"].border = border
    
    # Sección de agrupador - Gastos Operacionales
    ws["A20"] = "[# Agrupador]"
    ws["A20"].font = header_font
    ws["A20"].fill = agrupador_fill
    ws["A20"].alignment = center_alignment
    ws["A20"].border = border
    
    ws["A21"] = "Gastos Operacionales"
    ws["A21"].fill = cuenta_fill
    ws["A21"].border = border
    
    # Sección de cuentas de Gastos Operacionales
    ws["A22"] = "[Nombre Cuenta]"
    ws["B22"] = "[Codigo Cuenta]"
    ws["C22"] = "[Valor Monetario]"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}22"].font = header_font
        ws[f"{col}22"].fill = cuenta_header_fill
        ws[f"{col}22"].alignment = center_alignment
        ws[f"{col}22"].border = border
    
    # Agregar cuentas de gastos operacionales
    cuentas_gastos = [
        ("Gastos operativos", "60101", "-1,657,514.00"),
        ("Gastos de administración", "60200", "-713,311.00"),
        ("Gastos de ventas", "60300", "-91,757.00"),
        ("(Pérdida)/Ganancia neto por deterioro de cuentas por cobrar", "60400", "36,541.00"),
        ("Otros ingresos", "60500", "1,763.00"),
        ("Otros gastos", "60600", "-1,173.00"),
        ("Ganancia/(pérdida) Venta de Activo fijo", "60700", "-"),
        ("Variación en el valor de participación patrimonial", "60800", "-")
    ]
    
    row = 23
    for nombre, codigo, valor in cuentas_gastos:
        ws[f"A{row}"] = nombre
        ws[f"B{row}"] = codigo
        ws[f"C{row}"] = valor
        
        for col in ['A', 'B', 'C']:
            ws[f"{col}{row}"].fill = cuenta_fill
            ws[f"{col}{row}"].border = border
        
        row += 1
    
    ws[f"A{row}"] = "[# Total - Agrupador]"
    ws[f"C{row}"] = "-2,425,451.00"
    
    for col in ['A', 'C']:
        ws[f"{col}{row}"].font = Font(bold=True)
        ws[f"{col}{row}"].border = border
    
    # Sección de agrupador - Ingresos y Gastos Financieros
    row += 2
    ws[f"A{row}"] = "[# Agrupador]"
    ws[f"A{row}"].font = header_font
    ws[f"A{row}"].fill = agrupador_fill
    ws[f"A{row}"].alignment = center_alignment
    ws[f"A{row}"].border = border
    
    row += 1
    ws[f"A{row}"] = "Ingresos y Gastos Financieros"
    ws[f"A{row}"].fill = cuenta_fill
    ws[f"A{row}"].border = border
    
    # Sección de cuentas de Ingresos y Gastos Financieros
    row += 1
    ws[f"A{row}"] = "[Nombre Cuenta]"
    ws[f"B{row}"] = "[Codigo Cuenta]"
    ws[f"C{row}"] = "[Valor Monetario]"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{row}"].font = header_font
        ws[f"{col}{row}"].fill = cuenta_header_fill
        ws[f"{col}{row}"].alignment = center_alignment
        ws[f"{col}{row}"].border = border
    
    # Agregar cuentas financieras
    cuentas_financieras = [
        ("Ingresos financieros", "70107", "378,847.00"),
        ("Gastos financieros", "60201", "-105,901.00"),
        ("Diferencia en cambio, neta", "60301", "-19,051.00")
    ]
    
    row += 1
    start_row = row
    for nombre, codigo, valor in cuentas_financieras:
        ws[f"A{row}"] = nombre
        ws[f"B{row}"] = codigo
        ws[f"C{row}"] = valor
        
        for col in ['A', 'B', 'C']:
            ws[f"{col}{row}"].fill = cuenta_fill
            ws[f"{col}{row}"].border = border
        
        row += 1
    
    ws[f"A{row}"] = "[# Total - Agrupador]"
    ws[f"C{row}"] = "253,895.00"
    
    for col in ['A', 'C']:
        ws[f"{col}{row}"].font = Font(bold=True)
        ws[f"{col}{row}"].border = border
    
    # Utilidad antes de impuesto a la renta
    row += 2
    ws[f"A{row}"] = "Utilidad antes de impuesto a la renta"
    ws[f"C{row}"] = "1,740,654.00"
    
    for col in ['A', 'C']:
        ws[f"{col}{row}"].font = Font(bold=True)
        ws[f"{col}{row}"].border = border
    
    # Sección de agrupador - Impuestos
    row += 2
    ws[f"A{row}"] = "[# Agrupador]"
    ws[f"A{row}"].font = header_font
    ws[f"A{row}"].fill = agrupador_fill
    ws[f"A{row}"].alignment = center_alignment
    ws[f"A{row}"].border = border
    
    row += 1
    ws[f"A{row}"] = "Impuestos"
    ws[f"A{row}"].fill = cuenta_fill
    ws[f"A{row}"].border = border
    
    # Sección de cuentas de Impuestos
    row += 1
    ws[f"A{row}"] = "[Nombre Cuenta]"
    ws[f"B{row}"] = "[Codigo Cuenta]"
    ws[f"C{row}"] = "[Valor Monetario]"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{row}"].font = header_font
        ws[f"{col}{row}"].fill = cuenta_header_fill
        ws[f"{col}{row}"].alignment = center_alignment
        ws[f"{col}{row}"].border = border
    
    # Agregar cuentas de impuestos
    cuentas_impuestos = [
        ("Participación de los trabajadores", "60103", "-"),
        ("Impuesto a la renta", "60203", "-527,491.00")
    ]
    
    row += 1
    for nombre, codigo, valor in cuentas_impuestos:
        ws[f"A{row}"] = nombre
        ws[f"B{row}"] = codigo
        ws[f"C{row}"] = valor
        
        for col in ['A', 'B', 'C']:
            ws[f"{col}{row}"].fill = cuenta_fill
            ws[f"{col}{row}"].border = border
        
        row += 1
    
    ws[f"A{row}"] = "[# Total - Agrupador]"
    ws[f"C{row}"] = "-527,491.00"
    
    for col in ['A', 'C']:
        ws[f"{col}{row}"].font = Font(bold=True)
        ws[f"{col}{row}"].border = border
    
    # Utilidad neta
    row += 2
    ws[f"A{row}"] = "Utilidad neta"
    ws[f"C{row}"] = "1,213,163.00"
    
    for col in ['A', 'C']:
        ws[f"{col}{row}"].font = Font(bold=True)
        ws[f"{col}{row}"].border = border
    
    # Ajustar el ancho de las columnas
    for col in range(1, 4):
        column_letter = get_column_letter(col)
        if col == 1:
            ws.column_dimensions[column_letter].width = 50
        else:
            ws.column_dimensions[column_letter].width = 20

    # Guardar el archivo
    filename = "Estado_Resultados.xlsx"
    wb.save(filename)
    print(f"Archivo '{filename}' creado exitosamente.")
    return filename

if __name__ == "__main__":
    crear_excel_estado_resultados()
