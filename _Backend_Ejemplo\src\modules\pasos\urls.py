from django.urls import path, include
from rest_framework.routers import DefaultRouter

from src.modules.pasos.views import PasoView


router = DefaultRouter()
router.register("", PasoView, basename="pasos")

urlpatterns = [
    path("", include(router.urls)),
    path("nivel/<int:nivel_id>/", PasoView.as_view({"get": "pasos_by_nivel"})),
    path("evaluacion/<int:evaluacion_id>/asis-to-tobe/", PasoView.as_view({"get": "pasos_asis_to_tobe"})),
]