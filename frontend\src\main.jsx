import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import { ErrorBoundary, errorHandlers } from './errorConfig'
import { AppRoutes } from './Routes/AppRoutes.jsx'

createRoot(document.getElementById('root'), {
  onUncaughtError: errorHandlers.onUncaughtError,
  onCaughtError: errorHandlers.onCaughtError
}).render(
  <StrictMode>
    <ErrorBoundary>
      <AppRoutes />
    </ErrorBoundary>
  </StrictMode>
)

