from django.db import models
from src.modules.empresas.models import Empresa
from src.modules.agrupadores.models import Agrupador

class Cuenta(models.Model):
    class Meta:
        db_table = 'tm_cuentas'
        managed = True
        verbose_name = 'cuenta'
        verbose_name_plural = 'cuentas'

    int_idCuenta = models.AutoField(primary_key=True)
    str_nombre = models.CharField(max_length=255)
    str_codigo = models.CharField(max_length=12)
    int_idAgrupador = models.ForeignKey(Agrupador, on_delete=models.CASCADE, db_column='int_idAgrupador', related_name='cuentas')
    int_idEmpresa = models.ForeignKey(Empresa, on_delete=models.CASCADE, db_column='int_idEmpresa', related_name='cuentas')

    def __str__(self):
        return f"{self.str_codigo} - {self.str_nombre}"