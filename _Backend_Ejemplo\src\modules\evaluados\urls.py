from django.urls import path, include
from rest_framework.routers import DefaultRouter

from src.modules.evaluados.views import EvaluadoView


router = DefaultRouter()
router.register("", EvaluadoView, basename="evaluado")

urlpatterns = [
    path("", include(router.urls)),
    path("evaluacion/<int:evaluacion_id>/", EvaluadoView.as_view({"get": "evaluado_by_evaluacion"})),
    path("usuario/<int:id_usuario>/", EvaluadoView.as_view({"get": "evaluado_by_usuario"})),
]