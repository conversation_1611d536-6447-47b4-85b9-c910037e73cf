from django.db import models
from src.modules.estado_financiero.models import EstadoFinanciero
from src.modules.agrupadores.models import Agrupador

class TotalAgrupador(models.Model):
    class Meta:
        db_table = 'tr_totalagrupador'
        managed = True
        verbose_name = 'total agrupador'
        verbose_name_plural = 'totales agrupadores'

    int_idTotalAgrupador = models.AutoField(primary_key=True)
    db_resultadoAgrupador = models.DecimalField(max_digits=18, decimal_places=2)
    int_idEstadoFinanciero = models.ForeignKey(EstadoFinanciero, on_delete=models.CASCADE, db_column='int_idEstadoFinanciero', related_name='totales_agrupadores')
    int_idAgrupador = models.ForeignKey(Agrupador, on_delete=models.CASCADE, db_column='int_idAgrupador', related_name='totales')

    def __str__(self):
        return f"{self.int_idEstadoFinanciero} - {self.int_idAgrupador}: {self.db_resultadoAgrupador}"