import React, { useState, useEffect } from 'react'
import IconoRegresar from '../../../assets/SVG/IconoRegresar'
import { RoutesPrivate } from '../../../Routes/ProtectedRoute'
import { useNavigate } from 'react-router-dom';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import IconoInformacion from '../../../assets/SVG/IconoInformacion';
import IconoReestablecer from '../../../assets/SVG/IconoReestablecer';

const Comparativa = () => {
  const navigate = useNavigate();
  const [tipoSeleccionado, setTipoSeleccionado] = useState("PC");
  const [opcionesSecundarias, setOpcionesSecundarias] = useState([]);
  const [seleccionSecundaria, setSeleccionSecundaria] = useState("");
  const [utilidadNeta, setUtilidadNeta] = useState(32000000);
  const [ventas, setVentas] = useState(230342200);
  const [activos, setActivos] = useState(50000000);
  const [patrimonio, setPatrimonio] = useState(23400000);
  
  // Calcular componentes DuPont
  const margenNeto = utilidadNeta / ventas;
  const rotacionActivos = ventas / activos;
  const multiplicadorCapital = activos / patrimonio;
  const roeSimulado = margenNeto * rotacionActivos * multiplicadorCapital * 100;
  
  // Datos históricos para los gráficos
  const [datosDuPont, setDatosDuPont] = useState([
    { 
      periodo: 'Ene 2025', 
      margenNeto: 0.12, 
      rotacionActivos: 4.2, 
      multiplicadorCapital: 2.1, 
      roe: 10.6 
    },
    { 
      periodo: 'Feb 2025', 
      margenNeto: 0.13, 
      rotacionActivos: 4.3, 
      multiplicadorCapital: 2.2, 
      roe: 12.3 
    },
    { 
      periodo: 'Mar 2025', 
      margenNeto: 0.14, 
      rotacionActivos: 4.5, 
      multiplicadorCapital: 2.1, 
      roe: 13.2 
    },
    { 
      periodo: 'Abr 2025', 
      margenNeto: 0.139, 
      rotacionActivos: 4.6, 
      multiplicadorCapital: 2.14, 
      roe: 13.7 
    },
    { 
      periodo: 'Actual', 
      margenNeto: margenNeto, 
      rotacionActivos: rotacionActivos, 
      multiplicadorCapital: multiplicadorCapital, 
      roe: roeSimulado / 100 
    }
  ]);
  
  // Actualizar el último punto de datos cuando cambian los valores
  useEffect(() => {
    const nuevosDatos = [...datosDuPont];
    nuevosDatos[nuevosDatos.length - 1] = {
      periodo: 'Actual',
      margenNeto: margenNeto,
      rotacionActivos: rotacionActivos,
      multiplicadorCapital: multiplicadorCapital,
      roe: roeSimulado / 100
    };
    setDatosDuPont(nuevosDatos);
  }, [utilidadNeta, ventas, activos, patrimonio]);
  
   // Formatear números para mostrar
   const formatNumber = (num) => {
    return new Intl.NumberFormat('es-ES', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(num);
  };
  
  
  // Formatear porcentajes para tooltips
  const formatPorcentaje = (valor) => {
    return `${(valor * 100).toFixed(2)}%`;
  };
  
  // Formatear valores para tooltips
  const formatTooltipValue = (value, name) => {
    if (name === "roe") {
      return [`${(value * 100).toFixed(2)}%`, "ROE"];
    }
    if (name === "margenNeto") {
      return [`${(value * 100).toFixed(2)}%`, "Margen Neto"];
    }
    if (name === "rotacionActivos") {
      return [`${value.toFixed(2)}`, "Rotación de Activos"];
    }
    if (name === "multiplicadorCapital") {
      return [`${value.toFixed(2)}`, "Multiplicador de Capital"];
    }
    return [value, name];
  };
  
  // Datos de ejemplo para periodos contables
  const periodosContables = [
    { id: "pc1", nombre: "Abril - 2025", fecha: "31/04/2025" },
    { id: "pc2", nombre: "Marzo - 2025", fecha: "31/03/2025" },
    { id: "pc3", nombre: "Febrero - 2025", fecha: "28/02/2025" },
    { id: "pc4", nombre: "Enero - 2025", fecha: "31/01/2025" },
    { id: "pc5", nombre: "Diciembre - 2024", fecha: "31/12/2024" },
  ];
  
  // Datos de ejemplo para simulaciones
  const simulaciones = [
    { id: "sim1", nombre: "Simulación 1", fecha: "15/04/2025" },
    { id: "sim2", nombre: "Simulación 2", fecha: "10/04/2025" },
    { id: "sim3", nombre: "Simulación 3", fecha: "05/04/2025" },
    { id: "sim4", nombre: "Escenario optimista", fecha: "01/04/2025" },
    { id: "sim5", nombre: "Escenario pesimista", fecha: "01/04/2025" },
  ];
  
  // Actualizar opciones secundarias cuando cambia el tipo seleccionado
  useEffect(() => {
    if (tipoSeleccionado === "PC") {
      setOpcionesSecundarias(periodosContables);
    } else {
      setOpcionesSecundarias(simulaciones);
    }
    // Resetear la selección secundaria
    setSeleccionSecundaria("");
  }, [tipoSeleccionado]);
  
  // Manejar cambio en el primer select
  const handleTipoChange = (e) => {
    setTipoSeleccionado(e.target.value);
  };
  
  // Manejar cambio en el segundo select
  const handleSeleccionSecundariaChange = (e) => {
    setSeleccionSecundaria(e.target.value);
  };
  
  return (
    <div className="w-full h-full flex flex-col gap-4">
      <div className="flex w-full flex-col gap-4 justify-start items-start">
        <div
          className="flex gap-1 justify-start items-center cursor-pointer"
          onClick={() => navigate(RoutesPrivate.INICIO)}
        >
          <IconoRegresar
            size={"1.8rem"}
            color={"#909090"}
            salir={true}
            pagina={RoutesPrivate.INICIO}
          />
          <span className=" text-sm font-semibold text-[#909090]">
            Dashboard
          </span>
        </div>
        <div className="flex justify-between w-full">
          <div className="flex flex-col gap-1">
            <span className="text-[#1F263E] text-2xl font-semibold">
              Comparación de estados financieros
            </span>
            <span className="text-[#C3C0C0] text-sm font-base">
              Ajuste los ratios financieros para simular el Retorno sobre el Patrimonio (ROE) y comparar con el rendimiento real.
            </span>
          </div>
          <div className="flex gap-4">
            <select
              name="tipoComparacion"
              id="tipoComparacion"
              className="border border-gray-400 rounded-md p-1 min-w-[13rem] outline-none h-[2.5rem]"
              value={tipoSeleccionado}
              onChange={handleTipoChange}
            >
              <option value="PC">
                Periodo Contable
              </option>
              <option value="Simulacion">
                Simulación
              </option>
            </select>
            <select
              name="seleccionSecundaria"
              id="seleccionSecundaria"
              className="border border-gray-400 rounded-md p-1 min-w-[13rem] outline-none h-[2.5rem]"
              value={seleccionSecundaria}
              onChange={handleSeleccionSecundariaChange}
              disabled={opcionesSecundarias.length === 0}
            >
              <option value="" disabled>
                {tipoSeleccionado === "PC" ? "Seleccione un periodo" : "Seleccione una simulación"}
              </option>
              {opcionesSecundarias.map((opcion) => (
                <option key={opcion.id} value={opcion.id}>
                  {opcion.nombre} {opcion.fecha && `(${opcion.fecha})`}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
      
      <div className="w-full h-full flex gap-4">
      <div className="w-[50%]   border-1 border-[#EFEFEF] rounded-lg mt-4">
      <h2 className="text-[#1F263E] text-xl font-medium mb-4 bg-[#F8FAFB] p-4">  
      Detalles del periodo Actual
      </h2>
      <div className="p-5 flex flex-col gap-6 justify-start items-start">
            <div className="flex border-1 border-[#EFEFEF] rounded-lg p-5 w-full">
              {/* Fórmula DuPont visual */}
              <div className="w-full flex flex-col gap-6">
                <div className="flex items-center justify-between w-full">
                  <div className="flex flex-col items-center">
                    <div className="bg-[#F0F0FF] px-4 py-2 rounded-md text-[#5D5FEF] font-medium text-sm">
                      {formatNumber(utilidadNeta)}
                    </div>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#F0FFF0] px-4 py-2 rounded-md text-[#34A853] font-medium text-sm">
                      {formatNumber(ventas)}
                    </div>
                  </div>

                  <span className="mx-2">×</span>

                  <div className="flex flex-col items-center">
                    <div className="bg-[#F0FFF0] px-4 py-2 rounded-md text-[#34A853] font-medium text-sm">
                      {formatNumber(ventas)}
                    </div>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#F0F0FF] px-4 py-2 rounded-md text-[#5D5FEF] font-medium text-sm">
                      {formatNumber(activos)}
                    </div>
                  </div>

                  <span className="mx-2">×</span>

                  <div className="flex flex-col items-center">
                    <div className="bg-[#F0F0FF] px-4 py-2 rounded-md text-[#5D5FEF] font-medium text-sm">
                      {formatNumber(activos)}
                    </div>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#FFF8E0] px-4 py-2 rounded-md text-[#FF6B00] font-medium text-sm">
                      {formatNumber(patrimonio)}
                    </div>
                  </div>

                  <span className="mx-2">=</span>

                  <div className=" px-4 py-2 rounded-md text-[#FF9500] font-base text-sm flex flex-col justify-center items-center">
                   <span className="text-[#1F263E] text-sm mb-2">
                    Retorno sobre el Patrimonio (ROE) Real
                  </span>
                  <span className="text-[#FF4D8D] text-xl font-medium" >
                  {roeSimulado.toFixed(2)}%
                  </span>
                   </div>
                </div>
              </div>
            </div>
 
          </div>
          <div className="flex flex-col gap-2 w-full border-1 border-[#EFEFEF] rounded-lg">
          <div className="p-5 flex flex-col gap-2 bg-[#F8FAF8] justify-start items-start">
            <span className="text-[#1F263E] text-xl font-medium">Ratios</span>
            <span className="text-[#C3C0C0] text-sm font-base">
              Impacto en el rendimiento financiero
            </span>
          </div>
          <div className="p-5 flex  gap-4 w-full">
            <div className="flex flex-col w-[50%] gap-6">
              <div className="flex flex-col w-full gap-2">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Ratio de Liquidez
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#D9D9D9] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Capital del Trabajo
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#D9D9D9] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Ratio de Endeudamiento
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#D9D9D9] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Endeudamiento a corto plazo
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#D9D9D9] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Endeudamiento a largo plazo
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#D9D9D9] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Rentabilidad del Activo (ROA)
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#D9D9D9] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-col w-[50%] gap-6">
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Prueba Ácida
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#D9D9D9] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Rentabilidad Financiera (ROE)
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#D9D9D9] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Rentabilidad de las ventas
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#D9D9D9] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
     {/* Gráfico Lineal */}
     <div className="w-full border-1 border-[#EFEFEF] rounded-lg overflow-hidden">
          <div className="p-4 bg-[#F8FAFB]">
            <span className="text-[#1F263E] text-md font-semibold">
              Evolución de Componentes DuPont
            </span>
          </div>
          
          <div className="p-4">
            <div className="w-full h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={datosDuPont}
                  margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#EFEFEF" />
                  <XAxis 
                    dataKey="periodo" 
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                  />
                  <YAxis 
                    yAxisId="left"
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                    domain={[0, 'dataMax + 1']}
                  />
                  <YAxis 
                    yAxisId="right"
                    orientation="right"
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                    domain={[0, 'dataMax + 0.05']}
                  />
                  <Tooltip 
                    formatter={formatTooltipValue}
                    labelFormatter={(label) => `Periodo: ${label}`}
                    contentStyle={{ 
                      backgroundColor: '#fff', 
                      border: '1px solid #E5E7EB',
                      borderRadius: '8px',
                      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
                    }}
                  />
                  <Legend 
                    verticalAlign="bottom" 
                    height={36} 
                    iconType="circle"
                    wrapperStyle={{ paddingTop: '10px' }}
                  />
                  <Line 
                    yAxisId="right"
                    type="monotone" 
                    dataKey="margenNeto" 
                    name="Margen Neto" 
                    stroke="#5D5FEF" 
                    strokeWidth={2}
                    dot={{ r: 4, fill: "#5D5FEF" }}
                    activeDot={{ r: 6 }}
                  />
                  <Line 
                    yAxisId="left"
                    type="monotone" 
                    dataKey="rotacionActivos" 
                    name="Rotación de Activos" 
                    stroke="#34A853" 
                    strokeWidth={2}
                    dot={{ r: 4, fill: "#34A853" }}
                    activeDot={{ r: 6 }}
                  />
                  <Line 
                    yAxisId="left"
                    type="monotone" 
                    dataKey="multiplicadorCapital" 
                    name="Multiplicador de Capital" 
                    stroke="#FF6B00" 
                    strokeWidth={2}
                    dot={{ r: 4, fill: "#FF6B00" }}
                    activeDot={{ r: 6 }}
                  />
                  <Line 
                    yAxisId="right"
                    type="monotone" 
                    dataKey="roe" 
                    name="ROE" 
                    stroke="#FF4D8D" 
                    strokeWidth={3}
                    dot={{ r: 5, fill: "#FF4D8D" }}
                    activeDot={{ r: 7 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
        
        {/* Gráfico de Barras */}
        <div className="w-full border-1 border-[#EFEFEF] rounded-lg overflow-hidden">
          <div className="p-4 bg-[#F8FAFB]">
            <span className="text-[#1F263E] text-md font-semibold">
              Comparación de Componentes DuPont
            </span>
          </div>
          
          <div className="p-4">
            <div className="w-full h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={datosDuPont}
                  margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                  barGap={8}
                  barSize={20}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#EFEFEF" />
                  <XAxis 
                    dataKey="periodo" 
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                  />
                  <YAxis 
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                    domain={[0, 'dataMax + 1']}
                  />
                  <Tooltip 
                    formatter={formatTooltipValue}
                    labelFormatter={(label) => `Periodo: ${label}`}
                    contentStyle={{ 
                      backgroundColor: '#fff', 
                      border: '1px solid #E5E7EB',
                      borderRadius: '8px',
                      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
                    }}
                  />
                  <Legend 
                    verticalAlign="bottom" 
                    height={36} 
                    iconType="circle"
                    wrapperStyle={{ paddingTop: '10px' }}
                  />
                  <Bar 
                    dataKey="margenNeto" 
                    name="Margen Neto"
                    fill="#5D5FEF" 
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar 
                    dataKey="rotacionActivos" 
                    name="Rotación de Activos"
                    fill="#34A853" 
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar 
                    dataKey="multiplicadorCapital" 
                    name="Multiplicador de Capital"
                    fill="#FF6B00" 
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar 
                    dataKey="roe" 
                    name="ROE"
                    fill="#FF4D8D" 
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
            
            <div className="mt-4 p-4 bg-[#F8FAFB] rounded-lg">
              <h3 className="text-[#1F263E] font-medium mb-2">Interpretación del Análisis DuPont</h3>
              <p className="text-sm text-[#6B7280]">
                El análisis DuPont descompone el ROE en tres componentes principales: Margen Neto (Utilidad/Ventas), 
                Rotación de Activos (Ventas/Activos) y Multiplicador de Capital (Activos/Patrimonio). 
                Esta descomposición permite identificar qué factores están impulsando o limitando el rendimiento financiero.
              </p>
            </div>
          </div>
        </div>
        </div>
      </div>

      {seleccionSecundaria && (
        <div className="w-[50%]  border-1 border-[#EFEFEF] rounded-lg mt-4">
          <h2 className="text-[#1F263E] text-xl font-medium mb-4  bg-[#F8FAFB] p-4">
            {tipoSeleccionado === "PC" 
              ? `Detalles del periodo: ${opcionesSecundarias.find(p => p.id === seleccionSecundaria)?.nombre}` 
              : `Detalles de la simulación: ${opcionesSecundarias.find(s => s.id === seleccionSecundaria)?.nombre}`}
          </h2>
          <div className="p-5 flex flex-col gap-6 justify-start items-start">
            <div className="flex border-1 border-[#EFEFEF] rounded-lg p-5 w-full">
              {/* Fórmula DuPont visual */}
              <div className="w-full flex flex-col gap-6">
                <div className="flex items-center justify-between w-full">
                  <div className="flex flex-col items-center">
                    <div className="bg-[#F0F0FF] px-4 py-2 rounded-md text-[#5D5FEF] font-medium text-sm">
                      {formatNumber(utilidadNeta)}
                    </div>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#F0FFF0] px-4 py-2 rounded-md text-[#34A853] font-medium text-sm">
                      {formatNumber(ventas)}
                    </div>
                  </div>

                  <span className="mx-2">×</span>

                  <div className="flex flex-col items-center">
                    <div className="bg-[#F0FFF0] px-4 py-2 rounded-md text-[#34A853] font-medium text-sm">
                      {formatNumber(ventas)}
                    </div>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#F0F0FF] px-4 py-2 rounded-md text-[#5D5FEF] font-medium text-sm">
                      {formatNumber(activos)}
                    </div>
                  </div>

                  <span className="mx-2">×</span>

                  <div className="flex flex-col items-center">
                    <div className="bg-[#F0F0FF] px-4 py-2 rounded-md text-[#5D5FEF] font-medium text-sm">
                      {formatNumber(activos)}
                    </div>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#FFF8E0] px-4 py-2 rounded-md text-[#FF6B00] font-medium text-sm">
                      {formatNumber(patrimonio)}
                    </div>
                  </div>

                  <span className="mx-2">=</span>

                  <div className=" px-4 py-2 rounded-md text-[#FF9500] font-base text-sm flex flex-col justify-center items-center">
                   <span className="text-[#1F263E] text-sm mb-2">
                    Retorno sobre el Patrimonio (ROE) Real
                  </span>
                  <span className="text-[#FF4D8D] text-xl font-medium" >
                  {roeSimulado.toFixed(2)}%
                  </span>
                   </div>
                </div>
              </div>
            </div>
 
          </div>
          <div className="flex flex-col gap-2 w-full border-1 border-[#EFEFEF] rounded-lg">
          <div className="p-5 flex flex-col gap-2 bg-[#F8FAF8] justify-start items-start">
            <span className="text-[#1F263E] text-xl font-medium">Ratios</span>
            <span className="text-[#C3C0C0] text-sm font-base">
              Impacto en el rendimiento financiero
            </span>
          </div>
          <div className="p-5 flex  gap-4 w-full">
            <div className="flex flex-col w-[50%] gap-6">
              <div className="flex flex-col w-full gap-2">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Ratio de Liquidez
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#fd9a9a] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Capital del Trabajo
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#21DDB8] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Ratio de Endeudamiento
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#21DDB8] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Endeudamiento a corto plazo
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#21DDB8] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Endeudamiento a largo plazo
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#fd9a9a] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Rentabilidad del Activo (ROA)
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#21DDB8] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-col w-[50%] gap-6">
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Prueba Ácida
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#21DDB8] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Rentabilidad Financiera (ROE)
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#fd9a9a] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
              <div className="flex flex-col w-full gap-1">
                <div className="flex justify-between w-full">
                  <div className="flex justify-start items-center gap-2">
                    <span className="text-[#1F263E] text-md font-base">
                      Rentabilidad de las ventas
                    </span>
                    <IconoInformacion size={"1rem"} color={"#979797"} />
                  </div>
                  <span className="text-[#1F263E] text-md font-base">5.2%</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-[#21DDB8] h-2 rounded-full"
                      style={{ width: `20%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
     {/* Gráfico Lineal */}
     <div className="w-full border-1 border-[#EFEFEF] rounded-lg overflow-hidden">
          <div className="p-4 bg-[#F8FAFB]">
            <span className="text-[#1F263E] text-md font-semibold">
              Evolución de Componentes DuPont
            </span>
          </div>
          
          <div className="p-4">
            <div className="w-full h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={datosDuPont}
                  margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#EFEFEF" />
                  <XAxis 
                    dataKey="periodo" 
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                  />
                  <YAxis 
                    yAxisId="left"
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                    domain={[0, 'dataMax + 1']}
                  />
                  <YAxis 
                    yAxisId="right"
                    orientation="right"
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                    domain={[0, 'dataMax + 0.05']}
                  />
                  <Tooltip 
                    formatter={formatTooltipValue}
                    labelFormatter={(label) => `Periodo: ${label}`}
                    contentStyle={{ 
                      backgroundColor: '#fff', 
                      border: '1px solid #E5E7EB',
                      borderRadius: '8px',
                      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
                    }}
                  />
                  <Legend 
                    verticalAlign="bottom" 
                    height={36} 
                    iconType="circle"
                    wrapperStyle={{ paddingTop: '10px' }}
                  />
                  <Line 
                    yAxisId="right"
                    type="monotone" 
                    dataKey="margenNeto" 
                    name="Margen Neto" 
                    stroke="#5D5FEF" 
                    strokeWidth={2}
                    dot={{ r: 4, fill: "#5D5FEF" }}
                    activeDot={{ r: 6 }}
                  />
                  <Line 
                    yAxisId="left"
                    type="monotone" 
                    dataKey="rotacionActivos" 
                    name="Rotación de Activos" 
                    stroke="#34A853" 
                    strokeWidth={2}
                    dot={{ r: 4, fill: "#34A853" }}
                    activeDot={{ r: 6 }}
                  />
                  <Line 
                    yAxisId="left"
                    type="monotone" 
                    dataKey="multiplicadorCapital" 
                    name="Multiplicador de Capital" 
                    stroke="#FF6B00" 
                    strokeWidth={2}
                    dot={{ r: 4, fill: "#FF6B00" }}
                    activeDot={{ r: 6 }}
                  />
                  <Line 
                    yAxisId="right"
                    type="monotone" 
                    dataKey="roe" 
                    name="ROE" 
                    stroke="#FF4D8D" 
                    strokeWidth={3}
                    dot={{ r: 5, fill: "#FF4D8D" }}
                    activeDot={{ r: 7 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
        
        {/* Gráfico de Barras */}
        <div className="w-full border-1 border-[#EFEFEF] rounded-lg overflow-hidden">
          <div className="p-4 bg-[#F8FAFB]">
            <span className="text-[#1F263E] text-md font-semibold">
              Comparación de Componentes DuPont
            </span>
          </div>
          
          <div className="p-4">
            <div className="w-full h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={datosDuPont}
                  margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                  barGap={8}
                  barSize={20}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#EFEFEF" />
                  <XAxis 
                    dataKey="periodo" 
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                  />
                  <YAxis 
                    tick={{ fontSize: 12, fill: "#6B7280" }}
                    tickMargin={10}
                    axisLine={{ stroke: "#E5E7EB" }}
                    domain={[0, 'dataMax + 1']}
                  />
                  <Tooltip 
                    formatter={formatTooltipValue}
                    labelFormatter={(label) => `Periodo: ${label}`}
                    contentStyle={{ 
                      backgroundColor: '#fff', 
                      border: '1px solid #E5E7EB',
                      borderRadius: '8px',
                      boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
                    }}
                  />
                  <Legend 
                    verticalAlign="bottom" 
                    height={36} 
                    iconType="circle"
                    wrapperStyle={{ paddingTop: '10px' }}
                  />
                  <Bar 
                    dataKey="margenNeto" 
                    name="Margen Neto"
                    fill="#5D5FEF" 
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar 
                    dataKey="rotacionActivos" 
                    name="Rotación de Activos"
                    fill="#34A853" 
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar 
                    dataKey="multiplicadorCapital" 
                    name="Multiplicador de Capital"
                    fill="#FF6B00" 
                    radius={[4, 4, 0, 0]}
                  />
                  <Bar 
                    dataKey="roe" 
                    name="ROE"
                    fill="#FF4D8D" 
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
            
            <div className="mt-4 p-4 bg-[#F8FAFB] rounded-lg">
              <h3 className="text-[#1F263E] font-medium mb-2">Interpretación del Análisis DuPont</h3>
              <p className="text-sm text-[#6B7280]">
                El análisis DuPont descompone el ROE en tres componentes principales: Margen Neto (Utilidad/Ventas), 
                Rotación de Activos (Ventas/Activos) y Multiplicador de Capital (Activos/Patrimonio). 
                Esta descomposición permite identificar qué factores están impulsando o limitando el rendimiento financiero.
              </p>
            </div>
          </div>
        </div>
        </div>
         </div>
      )}
      </div>
    </div>
  )
}

export default Comparativa
