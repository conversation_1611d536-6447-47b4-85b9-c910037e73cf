from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from src.modules.estado_financiero.models import EstadoFinanciero
from src.modules.estado_financiero.serializers import EstadoFinancieroSerializer
from src.modules.estado_financiero.controller import EstadoFinancieroController

class EstadoFinancieroView(viewsets.ModelViewSet):
    serializer_class = EstadoFinancieroSerializer
    queryset = EstadoFinanciero.objects.all()
    permission_classes = [permissions.AllowAny]
    controller = EstadoFinancieroController()

    @swagger_auto_schema(
        method='get',
        operation_description="Obtiene todos los periodos contables para una empresa específica, agrupando los estados financieros por periodo.",
        manual_parameters=[
            openapi.Parameter(
                name='id_empresa',
                in_=openapi.IN_PATH,
                description='ID de la empresa',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
            openapi.Parameter(
                name='tipo_periodo',
                in_=openapi.IN_PATH,
                description='Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
        ],
        responses={
            200: "OK",
            400: "Bad Request",
            404: "Not Found"
        }
    )
    @action(detail=False, methods=['get'], url_path='empresa/(?P<id_empresa>[^/.]+)/tipo-periodo/(?P<tipo_periodo>[^/.]+)/solo-periodos')
    def periodos_por_empresa(self, request, id_empresa, tipo_periodo):
        """
        Obtiene todos los periodos contables para una empresa específica, agrupando los estados financieros por periodo.
        Solo obtiene los periodos contables, no las simulaciones.

        Parámetros de consulta:
        - id_empresa: ID de la empresa
        - tipo_periodo: ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
        """

        # Validar parámetros
        if not tipo_periodo:
            return Response(
                {"message": "El parámetro 'tipo_periodo' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not id_empresa:
            return Response(
                {"message": "El parámetro 'id_empresa' es requerido", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            id_empresa = int(id_empresa)
        except ValueError:
            return Response(
                {"message": "El parámetro 'id_empresa' debe ser un número entero", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )
        try:
            tipo_periodo = int(tipo_periodo)
        except ValueError:
            return Response(
                {"message": "El parámetro 'tipo_periodo' debe ser un número entero", "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Llamar al controlador
        result = self.controller.obtener_periodos_por_empresa(id_empresa, tipo_periodo)

        if not result.state:
            return Response(
                {"message": result.message, "state": False},
                status=status.HTTP_404_NOT_FOUND
            )

        return Response(
            {"message": result.message, "data": result.data, "state": True},
            status=status.HTTP_200_OK
        )

    @swagger_auto_schema(
        operation_description="Procesa un archivo Excel con la estructura de la plantilla de estados financieros",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'int_idEmpresa': openapi.Schema(type=openapi.TYPE_INTEGER, description='ID de la empresa'),
                'int_idUsuarios': openapi.Schema(type=openapi.TYPE_INTEGER, description='ID del usuario'),
                'ef_nombre': openapi.Schema(type=openapi.TYPE_STRING, description='Nombre del estado financiero (opcional)'),
                'archivo': openapi.Schema(type=openapi.TYPE_FILE, description='Archivo Excel con la estructura de la plantilla'),
            },
            required=['int_idEmpresa', 'int_idUsuarios', 'archivo'],
        ),
        responses={
            200: openapi.Response(
                description="Archivo procesado correctamente",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'int_idEstadoFinanciero': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'str_nombre': openapi.Schema(type=openapi.TYPE_STRING),
                            }
                        ),
                        'state': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                    }
                )
            ),
            400: openapi.Response(
                description="Error en el procesamiento del archivo",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'state': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                    }
                )
            ),
        }
    )
    @action(methods=["post"], detail=False, url_path="upload/excel")
    def upload_excel(self, request, *args, **kwargs):
        """
        Procesa un archivo Excel con la estructura de la plantilla de estados financieros.
        """
        try:
            # Validar que se haya enviado el archivo
            if "archivo" not in request.FILES:
                return Response(
                    data={"message": "No se ha enviado el archivo Excel", "data": None, "state": False},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Validar que se hayan enviado los datos requeridos
            if "int_idEmpresa" not in request.data:
                return Response(
                    data={"message": "No se ha enviado el ID de la empresa", "data": None, "state": False},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if "int_idUsuarios" not in request.data:
                return Response(
                    data={"message": "No se ha enviado el ID del usuario", "data": None, "state": False},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Obtener el archivo y los datos
            archivo = request.FILES["archivo"]
            data = {
                "int_idEmpresa": request.data.get("int_idEmpresa"),
                "int_idUsuarios": request.data.get("int_idUsuarios"),
                "ef_nombre": request.data.get("ef_nombre"),
            }

            # Procesar el archivo
            response = self.controller.procesar_archivo_excel(archivo, data)

            if not response.state:
                return Response(
                    data={"message": response.message, "data": response.data, "state": response.state},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                data={"message": response.message, "data": response.data, "state": response.state},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                data={"message": f"Error al procesar el archivo: {str(e)}", "data": None, "state": False},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        operation_description="Crea una simulación de estado financiero basada en un estado financiero de referencia",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'id_estado_financiero_ref': openapi.Schema(type=openapi.TYPE_INTEGER, description='ID del estado financiero de referencia'),
                'id_usuario': openapi.Schema(type=openapi.TYPE_INTEGER, description='ID del usuario que crea la simulación'),
                'agrupadores': openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description='Diccionario con los valores de los agrupadores',
                    properties={
                        'ventas': openapi.Schema(type=openapi.TYPE_NUMBER, description='Valor para el agrupador ventas netas'),
                        'utilidad neta': openapi.Schema(type=openapi.TYPE_NUMBER, description='Valor para el agrupador ER-UN'),
                        'activo total': openapi.Schema(type=openapi.TYPE_NUMBER, description='Valor para el agrupador activo total'),
                        'patrimonio': openapi.Schema(type=openapi.TYPE_NUMBER, description='Valor para el agrupador patrimonio'),
                    }
                ),
                'ratios': openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description='Diccionario con los valores de los ratios',
                    properties={
                        'margen neto': openapi.Schema(type=openapi.TYPE_NUMBER, description='Valor para el ratio Margen Neto'),
                        'rotacion': openapi.Schema(type=openapi.TYPE_NUMBER, description='Valor para el ratio Rotación'),
                        'apalancamiento': openapi.Schema(type=openapi.TYPE_NUMBER, description='Valor para el ratio Apalancamiento'),
                        'ROE': openapi.Schema(type=openapi.TYPE_NUMBER, description='Valor para el ratio ROE'),
                        'ROA': openapi.Schema(type=openapi.TYPE_NUMBER, description='Valor para el ratio ROA'),
                    }
                ),
            },
            required=['id_estado_financiero_ref', 'id_usuario'],
        ),
        responses={
            200: openapi.Response(
                description="Simulación creada correctamente",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'int_idEstadoFinanciero': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'str_nombre': openapi.Schema(type=openapi.TYPE_STRING),
                                'agrupadores_guardados': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_STRING)),
                                'ratios_guardados': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_STRING)),
                            }
                        ),
                        'state': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                    }
                )
            ),
            400: openapi.Response(
                description="Error en la creación de la simulación",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'state': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                    }
                )
            ),
        }
    )
    @action(methods=["post"], detail=False, url_path="simulacion")
    def crear_simulacion(self, request, *args, **kwargs):
        """
        Crea una simulación de estado financiero basada en un estado financiero de referencia.
        """
        try:
            # Validar que se hayan enviado los datos requeridos
            if "id_estado_financiero_ref" not in request.data:
                return Response(
                    data={"message": "No se ha enviado el ID del estado financiero de referencia", "data": None, "state": False},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if "id_usuario" not in request.data:
                return Response(
                    data={"message": "No se ha enviado el ID del usuario", "data": None, "state": False},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Procesar la solicitud
            response = self.controller.crear_simulacion(request.data)

            if not response.state:
                return Response(
                    data={"message": response.message, "data": response.data, "state": response.state},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                data={"message": response.message, "data": response.data, "state": response.state},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                data={"message": f"Error al crear la simulación: {str(e)}", "data": None, "state": False},
                status=status.HTTP_400_BAD_REQUEST,
            )

    @swagger_auto_schema(
        method='get',
        operation_description="Obtiene todas las simulaciones que tienen como estado financiero de referencia al ID proporcionado.",
        manual_parameters=[
            openapi.Parameter(
                name='id_estado_financiero',
                in_=openapi.IN_PATH,
                description='ID del estado financiero de referencia',
                type=openapi.TYPE_INTEGER,
                required=True
            ),
        ],
        responses={
            200: openapi.Response(
                description="Simulaciones encontradas exitosamente",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'data': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'int_idEstadoFinanciero': openapi.Schema(type=openapi.TYPE_INTEGER),
                                    'str_nombre': openapi.Schema(type=openapi.TYPE_STRING),
                                    'dt_fechaRegistro': openapi.Schema(type=openapi.TYPE_STRING),
                                    'int_idEmpresa': openapi.Schema(type=openapi.TYPE_INTEGER),
                                    'str_nombreEmpresa': openapi.Schema(type=openapi.TYPE_STRING),
                                    'int_idUsuarios': openapi.Schema(type=openapi.TYPE_INTEGER),
                                    'str_nombreUsuario': openapi.Schema(type=openapi.TYPE_STRING),
                                    'dt_fechaFinPeriodo': openapi.Schema(type=openapi.TYPE_STRING),
                                }
                            )
                        ),
                        'state': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                    }
                )
            ),
            400: "Bad Request",
            404: "Not Found"
        }
    )
    @action(detail=False, methods=['get'], url_path='simulaciones/estado-financiero/(?P<id_estado_financiero>[^/.]+)')
    def simulaciones_por_estado_financiero(self, request, id_estado_financiero):  # request is required by DRF
        """
        Obtiene todas las simulaciones que tienen como estado financiero de referencia al ID proporcionado.

        Parámetros de consulta:
        - id_estado_financiero: ID del estado financiero de referencia
        """
        # Validar parámetros
        if not id_estado_financiero:
            return Response(
                {"message": "El parámetro 'id_estado_financiero' es requerido", "data": None, "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            id_estado_financiero = int(id_estado_financiero)
        except ValueError:
            return Response(
                {"message": "El parámetro 'id_estado_financiero' debe ser un número entero", "data": None, "state": False},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Llamar al controlador
        result = self.controller.obtener_simulaciones_por_estado_financiero(id_estado_financiero)

        if not result.state:
            return Response(
                {"message": result.message, "data": None, "state": False},
                status=status.HTTP_404_NOT_FOUND
            )

        return Response(
            {"message": result.message, "data": result.data, "state": True},
            status=status.HTTP_200_OK
        )