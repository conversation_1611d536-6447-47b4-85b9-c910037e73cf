import pandas as pd
import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from datetime import datetime, timedelta

def crear_plantilla_estado_resultados():
    """
    Crea una plantilla de Excel para el Estado de Resultados que puede ser procesada
    por la función procesar_archivo_excel del módulo estado_financiero.
    """
    # Crear un nuevo libro de Excel
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Estado de Resultados"

    # Configurar estilos
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
    subheader_fill = PatternFill(start_color="5B9BD5", end_color="5B9BD5", fill_type="solid")
    agrupador_fill = PatternFill(start_color="ED7D31", end_color="ED7D31", fill_type="solid")
    subagrupador_fill = PatternFill(start_color="70AD47", end_color="70AD47", fill_type="solid")
    
    center_alignment = Alignment(horizontal='center', vertical='center')
    border = Border(
        top=Side(style="thin", color="000000"),
        bottom=Side(style="thin", color="000000"),
        left=Side(style="thin", color="000000"),
        right=Side(style="thin", color="000000"),
    )

    # Configurar encabezados
    # Sección 1: Información del Estado Financiero
    ws["A1"] = "[Tipo Estado Financiero]"
    ws["B1"] = "2"  # 2 para Estado de Resultados
    
    ws["A2"] = "[Tipo Periodo]"
    ws["B2"] = "3"  # 3 para Mensual (puede ser 1: Anual, 2: Trimestral, 3: Mensual)
    
    # Fechas para el periodo (mes actual)
    fecha_fin = datetime.now().replace(day=1) - timedelta(days=1)  # Último día del mes anterior
    fecha_inicio = fecha_fin.replace(day=1)  # Primer día del mes anterior
    
    ws["A3"] = "[Fecha Inicio Periodo]"
    ws["B3"] = fecha_inicio.strftime("%d/%m/%Y")
    
    ws["A4"] = "[Fecha Fin Periodo]"
    ws["B4"] = fecha_fin.strftime("%d/%m/%Y")

    # Aplicar estilos a los encabezados
    for row in range(1, 5):
        ws[f"A{row}"].font = header_font
        ws[f"A{row}"].fill = header_fill
        ws[f"A{row}"].alignment = center_alignment
        ws[f"A{row}"].border = border
        ws[f"B{row}"].border = border
        ws[f"B{row}"].alignment = Alignment(horizontal='left', vertical='center')

    # Sección 2: Estructura del Estado de Resultados
    # Fila actual para seguir agregando contenido
    current_row = 6

    # Agrupador: Ingresos
    ws[f"A{current_row}"] = "[# Agrupador]"
    ws[f"A{current_row}"].font = header_font
    ws[f"A{current_row}"].fill = agrupador_fill
    ws[f"A{current_row}"].alignment = center_alignment
    ws[f"A{current_row}"].border = border
    current_row += 1
    
    ws[f"A{current_row}"] = "Ingresos"
    ws[f"A{current_row}"].border = border
    current_row += 1
    
    # Cuentas de Ingresos
    ws[f"A{current_row}"] = "[Nombre Cuenta]"
    ws[f"B{current_row}"] = "[Codigo Cuenta]"
    ws[f"C{current_row}"] = "[Valor Monetario]"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].font = Font(bold=True)
        ws[f"{col}{current_row}"].fill = subheader_fill
        ws[f"{col}{current_row}"].alignment = center_alignment
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Ingresos operacionales
    ws[f"A{current_row}"] = "Ingreso de actividades ordinarias"
    ws[f"B{current_row}"] = "10000"
    ws[f"C{current_row}"] = "26,822,881"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Costos de ventas
    ws[f"A{current_row}"] = "Costos de ventas"
    ws[f"B{current_row}"] = "10100"
    ws[f"C{current_row}"] = "-22,910,671"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Línea separadora
    ws[f"A{current_row}"] = "----------------"
    ws[f"B{current_row}"] = ""
    ws[f"C{current_row}"] = "----------------"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Utilidad bruta
    ws[f"A{current_row}"] = "Utilidad bruta"
    ws[f"B{current_row}"] = "10200"
    ws[f"C{current_row}"] = "3,912,210"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
        ws[f"{col}{current_row}"].font = Font(bold=True)
    
    current_row += 1
    
    # Línea separadora
    ws[f"A{current_row}"] = ""
    ws[f"B{current_row}"] = ""
    ws[f"C{current_row}"] = ""
    current_row += 1
    
    # Agrupador: Gastos Operacionales
    ws[f"A{current_row}"] = "[# Agrupador]"
    ws[f"A{current_row}"].font = header_font
    ws[f"A{current_row}"].fill = agrupador_fill
    ws[f"A{current_row}"].alignment = center_alignment
    ws[f"A{current_row}"].border = border
    current_row += 1
    
    ws[f"A{current_row}"] = "Gastos Operacionales"
    ws[f"A{current_row}"].border = border
    current_row += 1
    
    # Cuentas de Gastos Operacionales
    ws[f"A{current_row}"] = "[Nombre Cuenta]"
    ws[f"B{current_row}"] = "[Codigo Cuenta]"
    ws[f"C{current_row}"] = "[Valor Monetario]"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].font = Font(bold=True)
        ws[f"{col}{current_row}"].fill = subheader_fill
        ws[f"{col}{current_row}"].alignment = center_alignment
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Gastos operativos
    ws[f"A{current_row}"] = "Gastos operativos"
    ws[f"B{current_row}"] = "20100"
    ws[f"C{current_row}"] = "-1,657,514"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Gastos de administración
    ws[f"A{current_row}"] = "Gastos de administración"
    ws[f"B{current_row}"] = "20200"
    ws[f"C{current_row}"] = "-713,311"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Gastos de ventas
    ws[f"A{current_row}"] = "Gastos de ventas"
    ws[f"B{current_row}"] = "20300"
    ws[f"C{current_row}"] = "-91,757"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Deterioro de cuentas por cobrar
    ws[f"A{current_row}"] = "(Pérdida)/Ganancia neto por deterioro de cuentas por cobrar"
    ws[f"B{current_row}"] = "20400"
    ws[f"C{current_row}"] = "36,541"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Otros ingresos
    ws[f"A{current_row}"] = "Otros ingresos"
    ws[f"B{current_row}"] = "20500"
    ws[f"C{current_row}"] = "1,763"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Otros gastos
    ws[f"A{current_row}"] = "Otros gastos"
    ws[f"B{current_row}"] = "20600"
    ws[f"C{current_row}"] = "-1,173"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Ganancia/pérdida venta de activo fijo
    ws[f"A{current_row}"] = "Ganancia/(pérdida) Venta de Activo fijo"
    ws[f"B{current_row}"] = "20700"
    ws[f"C{current_row}"] = "-"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Variación en el valor de participación patrimonial
    ws[f"A{current_row}"] = "Variación en el valor de participación patrimonial"
    ws[f"B{current_row}"] = "20800"
    ws[f"C{current_row}"] = "-"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Línea separadora
    ws[f"A{current_row}"] = "----------------"
    ws[f"B{current_row}"] = ""
    ws[f"C{current_row}"] = "----------------"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Total Gastos Operacionales
    ws[f"A{current_row}"] = "Total Gastos Operacionales"
    ws[f"B{current_row}"] = "20900"
    ws[f"C{current_row}"] = "-2,425,451"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
        ws[f"{col}{current_row}"].font = Font(bold=True)
    
    current_row += 1
    
    # Línea separadora
    ws[f"A{current_row}"] = ""
    ws[f"B{current_row}"] = ""
    ws[f"C{current_row}"] = ""
    current_row += 1
    
    # Agrupador: Ingresos y Gastos Financieros
    ws[f"A{current_row}"] = "[# Agrupador]"
    ws[f"A{current_row}"].font = header_font
    ws[f"A{current_row}"].fill = agrupador_fill
    ws[f"A{current_row}"].alignment = center_alignment
    ws[f"A{current_row}"].border = border
    current_row += 1
    
    ws[f"A{current_row}"] = "Ingresos y Gastos Financieros"
    ws[f"A{current_row}"].border = border
    current_row += 1
    
    # Cuentas de Ingresos y Gastos Financieros
    ws[f"A{current_row}"] = "[Nombre Cuenta]"
    ws[f"B{current_row}"] = "[Codigo Cuenta]"
    ws[f"C{current_row}"] = "[Valor Monetario]"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].font = Font(bold=True)
        ws[f"{col}{current_row}"].fill = subheader_fill
        ws[f"{col}{current_row}"].alignment = center_alignment
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Ingresos financieros
    ws[f"A{current_row}"] = "Ingresos financieros"
    ws[f"B{current_row}"] = "30100"
    ws[f"C{current_row}"] = "378,847"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Gastos financieros
    ws[f"A{current_row}"] = "Gastos financieros"
    ws[f"B{current_row}"] = "30200"
    ws[f"C{current_row}"] = "-105,901"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Diferencia en cambio
    ws[f"A{current_row}"] = "Diferencia en cambio, neta"
    ws[f"B{current_row}"] = "30300"
    ws[f"C{current_row}"] = "-19,051"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Línea separadora
    ws[f"A{current_row}"] = "----------------"
    ws[f"B{current_row}"] = ""
    ws[f"C{current_row}"] = "----------------"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Total Ingresos y Gastos Financieros
    ws[f"A{current_row}"] = "Total Ingresos y Gastos Financieros"
    ws[f"B{current_row}"] = "30400"
    ws[f"C{current_row}"] = "253,895"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
        ws[f"{col}{current_row}"].font = Font(bold=True)
    
    current_row += 1
    
    # Línea separadora
    ws[f"A{current_row}"] = "----------------"
    ws[f"B{current_row}"] = ""
    ws[f"C{current_row}"] = "----------------"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Utilidad antes de impuesto a la renta
    ws[f"A{current_row}"] = "Utilidad antes de impuesto a la renta"
    ws[f"B{current_row}"] = "40100"
    ws[f"C{current_row}"] = "1,740,654"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
        ws[f"{col}{current_row}"].font = Font(bold=True)
    
    current_row += 1
    
    # Línea separadora
    ws[f"A{current_row}"] = "----------------"
    ws[f"B{current_row}"] = ""
    ws[f"C{current_row}"] = "----------------"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Agrupador: Impuestos
    ws[f"A{current_row}"] = "[# Agrupador]"
    ws[f"A{current_row}"].font = header_font
    ws[f"A{current_row}"].fill = agrupador_fill
    ws[f"A{current_row}"].alignment = center_alignment
    ws[f"A{current_row}"].border = border
    current_row += 1
    
    ws[f"A{current_row}"] = "Impuestos"
    ws[f"A{current_row}"].border = border
    current_row += 1
    
    # Cuentas de Impuestos
    ws[f"A{current_row}"] = "[Nombre Cuenta]"
    ws[f"B{current_row}"] = "[Codigo Cuenta]"
    ws[f"C{current_row}"] = "[Valor Monetario]"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].font = Font(bold=True)
        ws[f"{col}{current_row}"].fill = subheader_fill
        ws[f"{col}{current_row}"].alignment = center_alignment
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Participación de los trabajadores
    ws[f"A{current_row}"] = "Participación de los trabajadores"
    ws[f"B{current_row}"] = "50100"
    ws[f"C{current_row}"] = "-"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Impuesto a la renta
    ws[f"A{current_row}"] = "Impuesto a la renta"
    ws[f"B{current_row}"] = "50200"
    ws[f"C{current_row}"] = "-527,491"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Línea separadora
    ws[f"A{current_row}"] = "================"
    ws[f"B{current_row}"] = ""
    ws[f"C{current_row}"] = "================"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    current_row += 1
    
    # Utilidad neta
    ws[f"A{current_row}"] = "Utilidad neta"
    ws[f"B{current_row}"] = "60100"
    ws[f"C{current_row}"] = "1,213,163"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
        ws[f"{col}{current_row}"].font = Font(bold=True)
    
    current_row += 1
    
    # Línea separadora
    ws[f"A{current_row}"] = "================"
    ws[f"B{current_row}"] = ""
    ws[f"C{current_row}"] = "================"
    
    for col in ['A', 'B', 'C']:
        ws[f"{col}{current_row}"].border = border
    
    # Ajustar el ancho de las columnas
    for col in range(1, 4):
        column_letter = get_column_letter(col)
        if col == 1:
            ws.column_dimensions[column_letter].width = 50
        else:
            ws.column_dimensions[column_letter].width = 20

    # Guardar el archivo
    filename = "Plantilla_Estado_Resultados.xlsx"
    wb.save(filename)
    print(f"Archivo '{filename}' creado exitosamente.")
    return filename

if __name__ == "__main__":
    crear_plantilla_estado_resultados()
