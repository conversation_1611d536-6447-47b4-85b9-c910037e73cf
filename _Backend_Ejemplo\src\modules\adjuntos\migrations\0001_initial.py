# Generated by Django 5.1 on 2025-04-03 10:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('pasos', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Adjunto',
            fields=[
                ('int_idAdjunto', models.AutoField(primary_key=True, serialize=False)),
                ('str_nombre', models.CharField(max_length=255)),
                ('str_peso', models.Char<PERSON>ield(max_length=255)),
                ('str_ruta', models.CharField(max_length=255)),
                ('str_extension', models.CharField(max_length=255)),
                ('int_idPaso', models.ForeignKey(db_column='int_idPaso', on_delete=django.db.models.deletion.CASCADE, related_name='adjuntos', to='pasos.paso')),
            ],
            options={
                'verbose_name': 'adjunto',
                'verbose_name_plural': 'adjuntos',
                'db_table': 'tr_adjuntos',
                'managed': True,
            },
        ),
    ]
