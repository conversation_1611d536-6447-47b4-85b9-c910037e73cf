from src.utils.classes import Response
from .models import Paso
from src.modules.niveles.models import Nivel
from src.modules.evaluaciones.models import Evaluacion
from src.modules.evaluaciones.controller import EvaluacionController
from src.modules.controles.models import Control
from .serializers import PasoSerializer


class PasoController:
    def __init__(self):
        self.evaluacion_controller = EvaluacionController()

    def get_by_nivel(self, nivel_id: str):
        try:
            pasos = Paso.objects.filter(
                int_idNivel_id=nivel_id
            ).all()
            if not pasos:
                return Response("No se encontraron pasos", state=False)
            return Response(data=pasos, state=True)

        except Exception as e:
            return Response(str(e), e)

    def get_pasos_asis_to_tobe(self, evaluacion_id: int):
        """
        Obtiene todos los pasos necesarios para ir del nivel Asis al nivel Tobe
        para una evaluación específica, utilizando los valores promedio por control.

        Args:
            evaluacion_id (int): ID de la evaluación

        Returns:
            Response: Objeto Response con los pasos necesarios
        """
        try:
            # Verificar que la evaluación existe
            if not Evaluacion.objects.filter(int_idEvaluacion=evaluacion_id).exists():
                return Response(f"No se encontró la evaluación con ID {evaluacion_id}", state=False)

            # Obtener los resultados promedio por control usando el método del EvaluacionController
            resultados_response = self.evaluacion_controller.listar_resultados_control_promedio(evaluacion_id)

            if not resultados_response.state or not resultados_response.data:
                return Response(f"No se pudieron obtener los resultados promedio: {resultados_response.message}", state=False)

            # Lista para almacenar todos los pasos necesarios
            pasos_necesarios = []

            # Para cada control en los resultados, obtener los pasos necesarios
            for control_data in resultados_response.data:
                # Verificar que tenga valores Asis y Tobe
                if control_data.get('asis') is None or control_data.get('tobe') is None:
                    continue

                # Convertir valores a float para comparación (vienen como strings)
                valor_asis = float(control_data['asis'])
                valor_tobe = float(control_data['tobe'])

                # Convertir a enteros para buscar niveles
                valor_asis_int = int(valor_asis)  # Truncar valor Asis

                # Para Tobe: si el primer decimal es de 5 a 9, redondear hacia arriba; si es de 0 a 4, truncar
                decimal_tobe = valor_tobe - int(valor_tobe)
                if decimal_tobe >= 0.5:
                    valor_tobe_int = int(valor_tobe) + 1  # Redondear hacia arriba
                else:
                    valor_tobe_int = int(valor_tobe)  # Truncar

                # Si Asis ya es igual o mayor que Tobe, no se necesitan pasos
                if valor_asis_int >= valor_tobe_int:
                    continue

                # Obtener el control
                control_id = control_data['id_control']
                try:
                    control = Control.objects.get(int_idControl=control_id)
                except Control.DoesNotExist:
                    continue

                # Obtener todos los niveles para este control, ordenados por valor
                niveles = Nivel.objects.filter(
                    int_idControl_id=control_id
                ).order_by('int_valor')

                if not niveles:
                    continue

                # Encontrar los niveles entre Asis y Tobe
                niveles_intermedios = []
                for nivel in niveles:
                    if valor_asis_int <= nivel.int_valor < valor_tobe_int:
                        niveles_intermedios.append(nivel)

                # Para cada nivel intermedio, obtener sus pasos
                for nivel in niveles_intermedios:
                    pasos = Paso.objects.filter(int_idNivel_id=nivel.int_idNivel)
                    for paso in pasos:
                        # Agregar información del control y nivel al paso
                        paso_data = {
                            'paso': paso,
                            'control': control,
                            'nivel': nivel,
                            'valor_asis': valor_asis,
                            'valor_tobe': valor_tobe
                        }
                        pasos_necesarios.append(paso_data)

            # Si no hay pasos necesarios
            if not pasos_necesarios:
                return Response("No se encontraron pasos necesarios para ir del nivel Asis al Tobe", state=False)

            # Formatear la respuesta
            resultado = []
            for item in pasos_necesarios:
                paso_serializado = {}
                paso_serializado['paso'] = PasoSerializer(item['paso']).data
                paso_serializado['control_id'] = item['control'].int_idControl
                paso_serializado['control_nombre'] = item['control'].str_descripcion
                paso_serializado['nivel_valor'] = item['nivel'].int_valor
                paso_serializado['nivel_descripcion'] = item['nivel'].str_descripcion
                paso_serializado['valor_asis'] = item['valor_asis']
                paso_serializado['valor_tobe'] = item['valor_tobe']
                resultado.append(paso_serializado)

            return Response(data=resultado, state=True)

        except Exception as e:
            return Response(str(e), e)