from django.db import models

from src.modules.usuarios.models import Usuario


# Create your models here.
class Log(models.Model):
    class Meta:
        db_table = "th_logs"
        managed = True
        verbose_name = "log"
        verbose_name_plural = "logs"

    int_idLog = models.AutoField(primary_key=True)
    int_idUsuarios = models.ForeignKey(
        Usuario,
        on_delete=models.CASCADE,
        related_name="th_logs",
        db_column="int_idUsuarios",
    )
    str_operacion = models.CharField(max_length=255)
    dt_fechaOperacion = models.DateTimeField(auto_now_add=True)
    str_tabla = models.CharField(max_length=255)
    str_idObjeto = models.IntegerField()
    str_objeto = models.TextField()
    
