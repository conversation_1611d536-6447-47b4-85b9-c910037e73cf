import React from "react";

const IconoCiercleSelected = ({size,color}) => {
  return (
    <svg
      height={size}
      id="Layer_1"
      version="1.1"
      viewBox="0 0 512 512"
      width={size}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g>
        <g>
          <path d="M256,48C141.1,48,48,141.1,48,256s93.1,208,208,208c114.9,0,208-93.1,208-208S370.9,48,256,48z M256,446.7    c-105.1,0-190.7-85.5-190.7-190.7c0-105.1,85.5-190.7,190.7-190.7c105.1,0,190.7,85.5,190.7,190.7    C446.7,361.1,361.1,446.7,256,446.7z" fill= {color}/>
        </g>
      </g>
      <g>
        <g>
          <path d="M256,96c-88.4,0-160,71.6-160,160c0,88.4,71.6,160,160,160c88.4,0,160-71.6,160-160C416,167.6,344.4,96,256,96z" fill= {color}/>
        </g>
      </g>
    </svg>
  );
};

export default IconoCiercleSelected;
