import os
from typing import Any
from django.http import HttpResponse
from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from src.utils.classes import Response as APIResponse
from rest_framework.decorators import api_view
from drf_yasg.utils import swagger_auto_schema
from src.modules.frameworks.serializers import FrameworkPlantillaSerializer
from .models import Nivel
from .serializers import NivelSerializer
from rest_framework.decorators import action
from django.db.models import Q
import requests
from .controller import NivelController
from src.utils.classes import Config

from drf_yasg import openapi


class NivelesView(viewsets.ModelViewSet):
    queryset = Nivel.objects.all()
    serializer_class = NivelSerializer
    permission_classes = [permissions.AllowAny]
    controller = NivelController()
    http_method_names = ["get","patch"]

    @swagger_auto_schema(
        operation_description="Obtiene todos los niveles de un control"
    )
    @action(methods=["GET"], detail=False, url_path="control/<int:control_id>")
    def get_by_control(self, request, control_id, *args, **kwargs):
        try:
            response: APIResponse = self.controller.get_by_control_id(control_id)
            if not response.state:
                return Response(response.data, status=status.HTTP_400_BAD_REQUEST)
            return Response(
                data=NivelSerializer(response.data, many=True).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)
