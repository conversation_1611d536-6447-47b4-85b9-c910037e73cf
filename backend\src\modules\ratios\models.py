from django.db import models
from src.modules.empresas.models import Empresa

class Ratio(models.Model):
    class Meta:
        db_table = 'tr_ratios'
        managed = True
        verbose_name = 'ratio'
        verbose_name_plural = 'ratios'

    int_idRatios = models.AutoField(primary_key=True)
    str_descripcion = models.CharField(max_length=255)
    str_formula = models.CharField(max_length=255)
    int_idEmpresa = models.ForeignKey(Empresa, on_delete=models.CASCADE, db_column='int_idEmpresa', related_name='ratios')
    str_idSuscripcion = models.Char<PERSON>ield(max_length=255, null=True)

    def __str__(self):
        return self.str_descripcion