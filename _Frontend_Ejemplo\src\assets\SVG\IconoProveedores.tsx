import React from "react";

const IconoProveedores = ({size,active}) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" id="Layer_2" viewBox="0 0 32 32" width={size} height={size}>
      <defs> </defs>
      <g id="icons">
        <g id="Person">
          <g id="User">
            <path
              class="cls-2"
              d="m12,4c2.76,0,5,2.24,5,5s-2.24,5-5,5-5-2.24-5-5c0-2.76,2.24-5,5-5m0-2c-3.87,0-7,3.13-7,7s3.13,7,7,7,7-3.13,7-7-3.13-7-7-7Z"
             fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
            <path
              class="cls-2"
              d="m22,30h-2v-5c0-2.76-2.24-5-5-5h-6c-2.76,0-5,2.24-5,5v5h-2v-5c0-3.86,3.14-7,7-7h6c3.86,0,7,3.14,7,7v5Z"
             fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
            <polygon
              class="cls-2"
              points="25 16.18 22.41 13.59 21 15 25 19 32 12 30.59 10.59 25 16.18"
             fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
            <rect
              id="_Transparent_Rectangle_"
              class="cls-1"
              
             fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
          </g>
        </g>
      </g>
    </svg>
  );
};

export default IconoProveedores;
