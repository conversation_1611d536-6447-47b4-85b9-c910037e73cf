import logging
from src.modules.evaluaciones.controller import EvaluacionController
from src.utils.classes import Response
from .models import Evaluado
from src.modules.detalles_evaluacion.models import DetalleEvaluacion
from src.modules.evaluaciones.models import Evaluacion

# Configurar logger
logger = logging.getLogger(__name__)

class EvaluadoController:
    def __init__(self):
        pass

    def get_by_evaluacion(self, evaluacion_id: str):
        try:
            evaluados = Evaluado.objects.filter(
                int_idEvaluacion_id=evaluacion_id
            ).all()
            if not evaluados:
                return Response("No se encontraron evaluados", state=False)
            return Response(data=evaluados, state=True)

        except Exception as e:
            logger.error(f"Error en get_by_evaluacion: {str(e)}", exc_info=True)
            return Response(str(e), state=False)

    def get_by_usuario(self, usuario_id: str):
        """
        Obtiene todos los evaluados asociados a un usuario específico.

        Args:
            usuario_id (str): ID del usuario

        Returns:
            Response: Objeto con los evaluados encontrados
                - state (bool): True si la operación fue exitosa, False en caso contrario
                - data (list): Lista de evaluados encontrados
                - message (str): Mensaje descriptivo del resultado
        """
        try:
            evaluados = Evaluado.objects.filter(
                int_idUsuarios_id=usuario_id
            ).all()
            if not evaluados:
                return Response("No se encontraron evaluados para este usuario", state=False)
            return Response(data=evaluados, state=True)

        except Exception as e:
            logger.error(f"Error en get_by_usuario: {str(e)}", exc_info=True)
            return Response(str(e), state=False)

    def actualizar_parcial(self, evaluado_id: int, datos: dict, serializer_class):
        """
        Actualiza parcialmente un evaluado con los datos proporcionados.

        Args:
            evaluado_id (int): ID del evaluado a actualizar
            datos (dict): Datos para actualizar el evaluado
            serializer_class: Clase del serializador a utilizar

        Returns:
            Response: Objeto con el resultado de la operación
                - state (bool): True si la operación fue exitosa, False en caso contrario
                - data (object): Evaluado actualizado
                - message (str): Mensaje descriptivo del resultado
        """
        try:
            # Obtener el evaluado
            try:
                evaluado = Evaluado.objects.get(int_idEvaluado=evaluado_id)
            except Evaluado.DoesNotExist:
                logger.error(f"Evaluado con ID {evaluado_id} no encontrado")
                return Response("Evaluado no encontrado", state=False)

            # Actualizar el evaluado con los datos proporcionados
            if datos:
                logger.info(f"Actualizando evaluado ID {evaluado_id} con datos: {datos}")
                serializer = serializer_class(evaluado, data=datos, partial=True)
                if serializer.is_valid(raise_exception=True):
                    evaluado = serializer.save()
                    logger.info(f"Evaluado ID {evaluado_id} actualizado correctamente")
                    return Response("Evaluado actualizado correctamente", data=evaluado, state=True)
                else:
                    logger.warning(f"Error de validación al actualizar evaluado ID {evaluado_id}: {serializer.errors}")
                    return Response(f"Error de validación: {serializer.errors}", state=False)
            else:
                # Si no hay datos para actualizar, simplemente devolver el evaluado
                return Response("No se proporcionaron datos para actualizar", data=evaluado, state=True)

        except Exception as e:
            logger.error(f"Error en actualizar_parcial: {str(e)}", exc_info=True)
            return Response(str(e), state=False)

    def verificar_completitud_y_actualizar_estado(self, evaluado_id: int):
        """
        Verifica si todos los valores de los detalles de evaluación están completos
        para un evaluado específico y actualiza su estado si es necesario.

        Args:
            evaluado_id (int): ID del evaluado a verificar

        Returns:
            Response: Objeto con el resultado de la operación
                - state (bool): True si la operación fue exitosa, False en caso contrario
                - data (dict): Datos del evaluado actualizado
                - message (str): Mensaje descriptivo del resultado
        """
        try:
            # 1. Obtener el evaluado
            logger.info(f"Verificando completitud para evaluado ID: {evaluado_id}")
            try:
                evaluado = Evaluado.objects.get(int_idEvaluado=evaluado_id)
            except Evaluado.DoesNotExist:
                logger.error(f"Evaluado con ID {evaluado_id} no encontrado")
                return Response("Evaluado no encontrado", state=False)

            # 2. Determinar el tipo de evaluado (ToBe o AsIs)
            tipo_evaluado = evaluado.int_idTipoEvaluado  # 1: ToBe, 2: AsIs
            logger.info(f"Tipo de evaluado: {tipo_evaluado} (1:ToBe, 2:AsIs)")

            # 3. Obtener todos los detalles de evaluación para la evaluación del evaluado
            evaluacion_id = evaluado.int_idEvaluacion.int_idEvaluacion
            logger.info(f"Obteniendo detalles para evaluación ID: {evaluacion_id}")

            # 4. Filtrar los detalles por el evaluado específico
            detalles_evaluado = DetalleEvaluacion.objects.filter(
                int_idEvaluacion_id=evaluacion_id,
                int_idEvaluado=evaluado
            )
            logger.info(f"Encontrados {detalles_evaluado.count()} detalles para este evaluado")

            if not detalles_evaluado.exists():
                logger.warning(f"No se encontraron detalles para el evaluado ID: {evaluado_id}")
                return Response(
                    "No se encontraron detalles de evaluación para este evaluado",
                    state=False
                )

            # 5. Verificar si todos los valores están completos según el tipo de evaluado
            todos_completos = True
            detalles_incompletos = []

            if tipo_evaluado == 1:  # ToBe
                for detalle in detalles_evaluado:
                    if detalle.str_valor_tobe is None or detalle.str_valor_tobe == '':
                        todos_completos = False
                        detalles_incompletos.append(detalle.int_idDetalleEvaluacion)
            elif tipo_evaluado == 2:  # AsIs
                for detalle in detalles_evaluado:
                    if detalle.str_valor_asis is None or detalle.str_valor_asis == '':
                        todos_completos = False
                        detalles_incompletos.append(detalle.int_idDetalleEvaluacion)
            else:
                logger.error(f"Tipo de evaluado desconocido: {tipo_evaluado}")
                return Response(f"Tipo de evaluado desconocido: {tipo_evaluado}", state=False)

            # 6. Si todos están completos, modificar el estado del evaluado a true
            estado_anterior = evaluado.bool_estado
            if todos_completos:
                logger.info(f"Todos los valores están completos para el evaluado ID: {evaluado_id}. Actualizando estado a true.")
                evaluado.bool_estado = True
                evaluado.save()
                mensaje = "Todos los valores están completos. Estado actualizado a true."
            else:
                logger.info(f"Valores incompletos para el evaluado ID: {evaluado_id}. Detalles incompletos: {detalles_incompletos}")
                mensaje = "Algunos valores aún no están completos. El estado no ha sido actualizado."

            # 7. Verificar si todos los evaluados del mismo tipo tienen bool_estado = true
            # y actualizar el campo correspondiente en la evaluación
            if todos_completos and evaluado.bool_estado:
                self.__verificar_y_actualizar_evaluacion(tipo_evaluado, evaluacion_id)

            # 8. Verificar que los campos bool_asis y bool_tobe de la evaluacion sean True
            evaluacion = Evaluacion.objects.get(int_idEvaluacion=evaluacion_id)

            if evaluacion.bool_asis and evaluacion.bool_tobe:
                # Calcular resultado promedio
                print(f"Evaluacion ID: {evaluacion_id}")
                evaluacion_controller = EvaluacionController()
                resultado = evaluacion_controller.calcular_resultado_promedio(evaluacion_id=evaluacion_id)

                if resultado.state:
                    # Obtener valores asis y tobe
                    asis_value = resultado.data.get("asis")
                    tobe_value = resultado.data.get("tobe")

                    if not (asis_value is None or tobe_value is None):
                        # Convertir a float si son strings
                        if isinstance(asis_value, str):
                            asis_value = float(asis_value)
                        if isinstance(tobe_value, str):
                            tobe_value = float(tobe_value)

                        # Truncar asis al entero anterior
                        asis_truncated = int(asis_value)

                        # Aproximar tobe según el primer decimal
                        tobe_decimal = tobe_value - int(tobe_value)
                        if tobe_decimal >= 0.5:
                            tobe_rounded = int(tobe_value) + 1
                        else:
                            tobe_rounded = int(tobe_value)

                        # Calcular la brecha
                        brecha = tobe_rounded - asis_truncated

                        # Establecer mensaje según la brecha
                        if brecha <= 0:
                            mensaje = "¡Excelente! El estado actual cumple o supera el nivel deseado."
                        else:
                            mensaje = "Hay oportunidades de mejora para alcanzar el nivel deseado."

                        # Actualizar campos de la evaluación
                        evaluacion.str_nota = str(asis_truncated)
                        evaluacion.str_resultado = mensaje
                        evaluacion.save()


            # 9. Preparar respuesta
            logger.info(f"Completada verificación del evaluado ID: {evaluado_id}. Estado final: {evaluado.bool_estado}")
            return Response(
                mensaje,
                data={
                    "evaluado": evaluado,
                    "estado_anterior": estado_anterior,
                    "estado_actual": evaluado.bool_estado,
                    "todos_completos": todos_completos,
                    "detalles_incompletos": detalles_incompletos if not todos_completos else []
                },
                state=True
            )

        except Exception as e:
            logger.error(f"Error en verificar_completitud_y_actualizar_estado: {str(e)}", exc_info=True)
            return Response(str(e), state=False)

    def __verificar_y_actualizar_evaluacion(self, tipo_evaluado: int, evaluacion_id: int):
        """
        Verifica si todos los evaluados de un tipo específico tienen bool_estado = true
        y actualiza el campo correspondiente en la evaluación.

        Args:
            tipo_evaluado (int): Tipo de evaluado (1: ToBe, 2: AsIs)
            evaluacion_id (int): ID de la evaluación
        """
        try:
            # 1. Obtener todos los evaluados de la evaluación del tipo especificado
            logger.info(f"Verificando todos los evaluados de tipo {tipo_evaluado} para la evaluación ID: {evaluacion_id}")
            evaluados_mismo_tipo = Evaluado.objects.filter(
                int_idEvaluacion_id=evaluacion_id,
                int_idTipoEvaluado=tipo_evaluado
            )

            # Si no hay evaluados de este tipo, no hay nada que hacer
            if not evaluados_mismo_tipo.exists():
                logger.info(f"No se encontraron evaluados de tipo {tipo_evaluado} para la evaluación ID: {evaluacion_id}")
                return

            # 2. Verificar si todos tienen bool_estado = true
            todos_completos = True
            evaluados_incompletos = []

            for evaluado in evaluados_mismo_tipo:
                if not evaluado.bool_estado:
                    todos_completos = False
                    evaluados_incompletos.append(evaluado.int_idEvaluado)

            # 3. Si todos están completos, actualizar el campo correspondiente en la evaluación
            if todos_completos:
                try:
                    evaluacion = Evaluacion.objects.get(int_idEvaluacion=evaluacion_id)

                    if tipo_evaluado == 1:  # ToBe
                        logger.info(f"Todos los evaluados ToBe de la evaluación ID: {evaluacion_id} están completos. Actualizando bool_tobe a true.")
                        evaluacion.bool_tobe = True
                    elif tipo_evaluado == 2:  # AsIs
                        logger.info(f"Todos los evaluados AsIs de la evaluación ID: {evaluacion_id} están completos. Actualizando bool_asis a true.")
                        evaluacion.bool_asis = True

                    evaluacion.save()
                    logger.info(f"Evaluación ID: {evaluacion_id} actualizada correctamente. bool_tobe: {evaluacion.bool_tobe}, bool_asis: {evaluacion.bool_asis}")
                except Evaluacion.DoesNotExist:
                    logger.error(f"Evaluación con ID {evaluacion_id} no encontrada")
            else:
                logger.info(f"No todos los evaluados de tipo {tipo_evaluado} para la evaluación ID: {evaluacion_id} están completos. Evaluados incompletos: {evaluados_incompletos}")

        except Exception as e:
            logger.error(f"Error en __verificar_y_actualizar_evaluacion: {str(e)}", exc_info=True)

    def delete_evaluado(self, evaluado_id: int):
        """
        Elimina un evaluado y verifica si todos los evaluados del mismo tipo tienen bool_estado = true
        para actualizar el campo correspondiente en la evaluación.

        Args:
            evaluado_id (int): ID del evaluado a eliminar

        Returns:
            Response: Objeto con el resultado de la operación
                - state (bool): True si la operación fue exitosa, False en caso contrario
                - message (str): Mensaje descriptivo del resultado
        """
        try:
            # 1. Obtener el evaluado
            logger.info(f"Eliminando evaluado ID: {evaluado_id}")
            try:
                evaluado = Evaluado.objects.get(int_idEvaluado=evaluado_id)
            except Evaluado.DoesNotExist:
                logger.error(f"Evaluado con ID {evaluado_id} no encontrado")
                return Response("Evaluado no encontrado", state=False)

            # 2. Guardar información necesaria antes de eliminar
            tipo_evaluado = evaluado.int_idTipoEvaluado  # 1: ToBe, 2: AsIs
            evaluacion_id = evaluado.int_idEvaluacion.int_idEvaluacion

            # 3. Eliminar el evaluado
            evaluado.delete()
            logger.info(f"Evaluado ID: {evaluado_id} eliminado correctamente")

            # 4. Verificar si todos los evaluados del mismo tipo tienen bool_estado = true
            # y actualizar el campo correspondiente en la evaluación
            evaluados_mismo_tipo = Evaluado.objects.filter(
                int_idEvaluacion_id=evaluacion_id,
                int_idTipoEvaluado=tipo_evaluado
            )

            # Si no hay más evaluados de este tipo, no hay nada que verificar
            if not evaluados_mismo_tipo.exists():
                logger.info(f"No quedan evaluados de tipo {tipo_evaluado} para la evaluación ID: {evaluacion_id}")
                return Response("Evaluado eliminado correctamente", state=True)

            # Verificar si todos tienen bool_estado = true
            todos_completos = True
            for ev in evaluados_mismo_tipo:
                if not ev.bool_estado:
                    todos_completos = False
                    break

            # Si todos están completos, actualizar el campo correspondiente en la evaluación
            if todos_completos:
                try:
                    evaluacion = Evaluacion.objects.get(int_idEvaluacion=evaluacion_id)

                    if tipo_evaluado == 1:  # ToBe
                        logger.info(f"Todos los evaluados ToBe de la evaluación ID: {evaluacion_id} están completos. Actualizando bool_tobe a true.")
                        evaluacion.bool_tobe = True
                    elif tipo_evaluado == 2:  # AsIs
                        logger.info(f"Todos los evaluados AsIs de la evaluación ID: {evaluacion_id} están completos. Actualizando bool_asis a true.")
                        evaluacion.bool_asis = True

                    evaluacion.save()
                    logger.info(f"Evaluación ID: {evaluacion_id} actualizada correctamente. bool_tobe: {evaluacion.bool_tobe}, bool_asis: {evaluacion.bool_asis}")
                except Evaluacion.DoesNotExist:
                    logger.error(f"Evaluación con ID {evaluacion_id} no encontrada")

            return Response("Evaluado eliminado correctamente", state=True)

        except Exception as e:
            logger.error(f"Error en delete_evaluado: {str(e)}", exc_info=True)
            return Response(str(e), state=False)