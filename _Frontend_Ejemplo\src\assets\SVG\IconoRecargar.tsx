import React from "react";

const IconoRecargar = ({size,color}) => {
  return (
    <svg
      enable-background="new 0 0 50 50"
      height={size}
      id="Layer_1"
      version="1.1"
      viewBox="0 0 50 50"
      width={size}
       xmlns="http://www.w3.org/2000/svg"
     >
       <polyline
        fill="none"
        points="40,7 40,16   31,15.999 "
        stroke={color}
        stroke-linecap="round"
        stroke-miterlimit="10"
        stroke-width="5"
      />
      <path
        d="M41.999,25  c0,9.39-7.61,17-17,17s-17-7.61-17-17s7.61-17,17-17c5.011,0,9.516,2.167,12.627,5.616c0.618,0.686,1.182,1.423,1.683,2.203"
        fill="none"
        stroke={color}
        stroke-linecap="round"
        stroke-miterlimit="10"
        stroke-width="5"
      />
    </svg>
  );
};

export default IconoRecargar;
