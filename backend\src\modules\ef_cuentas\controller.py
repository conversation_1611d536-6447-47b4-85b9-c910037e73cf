from src.utils.classes import Response as MiddleResponse
from src.modules.ef_cuentas.models import EFCuenta
from src.modules.estado_financiero.models import EstadoFinanciero
from src.modules.agrupadores.models import Agrupador
from src.modules.cuentas.models import Cuenta
from src.modules.total_agrupador.models import TotalAgrupador
from datetime import datetime, timedelta
from decimal import Decimal

class EFCuentasController:
    def __init__(self):
        pass

    def obtener_cuentas_por_empresa_periodo(self, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene todas las cuentas de los estados financieros para una empresa, tipo de periodo y fecha fin específicos.

        Args:
            id_empresa (int): ID de la empresa
            tipo_periodo (int): ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
            fecha_fin (str): Fecha de fin del periodo en formato DD-MM-YYYY

        Returns:
            Response: Objeto Response con los datos de las cuentas por agrupador y tipo de estado financiero
        """
        try:
            # Mapear el tipo de periodo a los valores en la base de datos
            tipo_periodo_map = {
                1: "Anual",
                2: "Trimestral",
                3: "Mensual",
                4: "Semanal",
                5: "Diario"
            }

            # Validar el tipo de periodo
            if tipo_periodo not in tipo_periodo_map.keys():
                return MiddleResponse(
                    message=f"El tipo de periodo {tipo_periodo} no es válido. "
                    f"Los tipos de periodo válidos son: {', '.join(tipo_periodo_map.values())}",
                    state=False
                )

            # Convertir la fecha del formato DD-MM-YYYY al formato YYYY-MM-DD
            try:
                # Parsear la fecha en formato DD-MM-YYYY
                fecha_obj = datetime.strptime(fecha_fin, '%d-%m-%Y')
                # Convertir al formato YYYY-MM-DD para la consulta en la base de datos
                fecha_fin_formateada = fecha_obj.strftime('%Y-%m-%d')
            except ValueError:
                return MiddleResponse(
                    message="Formato de fecha inválido. Debe ser DD-MM-YYYY",
                    state=False
                )

            # Buscar los estados financieros para la fecha dada
            estados_financieros = EstadoFinanciero.objects.filter(
                int_idEmpresa=id_empresa,
                int_tipoPeriodo=tipo_periodo,
                int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                dt_fechaFinPeriodo=fecha_fin_formateada
            )

            if not estados_financieros.exists():
                return MiddleResponse(
                    message=f"No se encontraron estados financieros para la empresa con ID {id_empresa}, tipo de periodo {tipo_periodo} y fecha {fecha_fin}",
                    state=False
                )

            resultado = []

            # Procesar cada estado financiero
            for ef in estados_financieros:
                # Estructura para este estado financiero
                ef_data = {
                    "tipo_estado_financiero": ef.int_tipoEstadoFinanciero,
                    "id_estado_financiero": ef.int_idEstadoFinanciero,
                    "agrupadores": []
                }

                # Obtener todos los agrupadores para este estado financiero
                totales_agrupadores = TotalAgrupador.objects.filter(
                    int_idEstadoFinanciero=ef.int_idEstadoFinanciero
                )

                # Obtener el estado financiero del periodo anterior para calcular variaciones
                ef_anterior = self._obtener_estado_financiero_anterior(ef, tipo_periodo, id_empresa)

                # Valores de referencia para el análisis vertical
                valor_activos_totales = None
                valor_pasivos_totales = None
                valor_patrimonio = None
                valor_ventas_netas = None
                valor_utilidad_bruta = None # Utilidad Bruta: ER-UB

                # Si es un Estado de Situación Financiera (tipo 1), calcular activos totales y pasivos totales
                if ef.int_tipoEstadoFinanciero == 1:
                    activo_corriente = None
                    activo_no_corriente = None
                    pasivo_corriente = None
                    pasivo_no_corriente = None

                    # Buscar los valores de activos y pasivos
                    for total in totales_agrupadores:
                        nombre_agrupador = total.int_idAgrupador.str_nombre.lower()
                        nombre_subagrupador = total.int_idAgrupador.str_nombre_subagrupador.lower() if total.int_idAgrupador.str_nombre_subagrupador else None

                        # Buscar activo corriente
                        if "activo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                            activo_corriente = total.db_resultadoAgrupador

                        # Buscar activo no corriente
                        elif "activo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                            activo_no_corriente = total.db_resultadoAgrupador

                        # Buscar pasivo corriente
                        elif "pasivo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                            pasivo_corriente = total.db_resultadoAgrupador

                        # Buscar pasivo no corriente
                        elif "pasivo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                            pasivo_no_corriente = total.db_resultadoAgrupador

                        # Buscar patrimonio
                        elif "patrimonio" in nombre_agrupador:
                            valor_patrimonio = total.db_resultadoAgrupador

                    # Calcular activos totales y pasivos totales si tenemos los componentes
                    if activo_corriente is not None and activo_no_corriente is not None:
                        valor_activos_totales = activo_corriente + activo_no_corriente

                    if pasivo_corriente is not None and pasivo_no_corriente is not None:
                        valor_pasivos_totales = pasivo_corriente + pasivo_no_corriente

                # Si es un Estado de Resultados (tipo 2), buscar ventas netas y agrupadores especiales
                elif ef.int_tipoEstadoFinanciero == 2:
                    # Lista de agrupadores especiales
                    agrupadores_especiales = ['ER-UB', 'ER-UO', 'ER-UAIR', 'ER-UN']
                    valores_agrupadores_especiales = {}

                    for total in totales_agrupadores:
                        nombre_agrupador = total.int_idAgrupador.str_nombre
                        nombre_agrupador_lower = nombre_agrupador.lower()

                        # Buscar ventas netas
                        if "ventas netas" in nombre_agrupador_lower:
                            valor_ventas_netas = total.db_resultadoAgrupador

                        # Buscar agrupadores especiales
                        if nombre_agrupador in agrupadores_especiales:
                            valores_agrupadores_especiales[nombre_agrupador] = total.db_resultadoAgrupador

                # Procesar cada agrupador
                agrupadores_procesados = {}

                # Primero, obtener todos los agrupadores únicos
                agrupadores_unicos = Agrupador.objects.filter(
                    cuentas__valores__int_idEstadoFinanciero=ef
                ).distinct()

                for agrupador in agrupadores_unicos:
                    nombre_agrupador = agrupador.str_nombre
                    nombre_subagrupador = agrupador.str_nombre_subagrupador

                    # Clave única para este agrupador
                    clave_agrupador = f"{nombre_agrupador}_{nombre_subagrupador}"

                    if clave_agrupador not in agrupadores_procesados:
                        # Buscar el total del agrupador
                        try:
                            total_agrupador = TotalAgrupador.objects.get(
                                int_idEstadoFinanciero=ef,
                                int_idAgrupador=agrupador
                            )
                            valor_agrupador = total_agrupador.db_resultadoAgrupador
                        except TotalAgrupador.DoesNotExist:
                            valor_agrupador = Decimal('0.0')

                        # Calcular la variación respecto al periodo anterior
                        variacion_agrupador = None
                        if ef_anterior:
                            try:
                                total_anterior = TotalAgrupador.objects.get(
                                    int_idEstadoFinanciero=ef_anterior,
                                    int_idAgrupador=agrupador
                                )

                                if total_anterior.db_resultadoAgrupador != 0:
                                    variacion_abs = (valor_agrupador - total_anterior.db_resultadoAgrupador)
                                    variacion = variacion_abs / total_anterior.db_resultadoAgrupador * 100
                                    variacion_agrupador = round(float(variacion),2)
                                elif total_anterior.db_resultadoAgrupador == 0 and valor_agrupador == 0:
                                    variacion_agrupador = 0.00
                            except TotalAgrupador.DoesNotExist:
                                pass

                        # Calcular la proporción según el tipo de estado financiero
                        proporcion_agrupador = None
                        if ef.int_tipoEstadoFinanciero == 1:
                            # Para activos
                            if "activo" in nombre_agrupador.lower() and valor_activos_totales:
                                proporcion_agrupador = round(abs(float(valor_agrupador / valor_activos_totales * 100)), 2)
                            # Para pasivos y patrimonio
                            elif ("pasivo" in nombre_agrupador.lower() or "patrimonio" in nombre_agrupador.lower()) and valor_pasivos_totales and valor_patrimonio:
                                proporcion_agrupador = round(abs(float(valor_agrupador / (valor_pasivos_totales + valor_patrimonio) * 100)), 2)
                        elif ef.int_tipoEstadoFinanciero == 2:
                            # Para estado de resultados
                            if valor_ventas_netas and (nombre_agrupador in ['ventas netas', 'costo de ventas'] or ('venta' in nombre_agrupador.lower() and ('costo' not in nombre_agrupador.lower() or 'netas' in nombre_agrupador.lower()))):
                                # Respecto a ventas netas
                                proporcion_agrupador = round(abs(float(valor_agrupador / valor_ventas_netas * 100)), 2)
                            elif valores_agrupadores_especiales and valores_agrupadores_especiales['ER-UB']:
                                # Respecto a utilidad bruta
                                proporcion_agrupador = round(abs(float(valor_agrupador / valores_agrupadores_especiales['ER-UB'] * 100)), 2)

                        # Obtener las cuentas para este agrupador
                        cuentas_data = []
                        cuentas = Cuenta.objects.filter(
                            int_idAgrupador=agrupador,
                            valores__int_idEstadoFinanciero=ef
                        ).distinct()

                        for cuenta in cuentas:
                            try:
                                ef_cuenta = EFCuenta.objects.get(
                                    int_idEstadoFinanciero=ef,
                                    int_idCuenta=cuenta
                                )
                                valor_cuenta = ef_cuenta.db_valor

                                # Calcular la variación de la cuenta respecto al periodo anterior
                                variacion_cuenta = None
                                if ef_anterior:
                                    try:
                                        ef_cuenta_anterior = EFCuenta.objects.get(
                                            int_idEstadoFinanciero=ef_anterior,
                                            int_idCuenta=cuenta
                                        )

                                        if ef_cuenta_anterior.db_valor != 0:
                                            variacion_abs = (valor_cuenta - ef_cuenta_anterior.db_valor)
                                            variacion = variacion_abs / ef_cuenta_anterior.db_valor * 100
                                            variacion_cuenta = round(float(variacion),2)
                                        elif ef_cuenta_anterior.db_valor == 0 and valor_cuenta == 0:
                                            variacion_cuenta = 0.00
                                        elif ef_cuenta_anterior.db_valor == 0 and valor_cuenta != 0:
                                            variacion_cuenta = 0.00
                                    except EFCuenta.DoesNotExist:
                                        pass

                                # Calcular la proporción según el tipo de estado financiero
                                proporcion_cuenta = None
                                if ef.int_tipoEstadoFinanciero == 1:
                                    # Para activos
                                    if "activo" in nombre_agrupador.lower() and valor_activos_totales:
                                        proporcion_cuenta = round(abs(float(valor_cuenta / valor_activos_totales * 100)), 2)
                                    # Para pasivos y patrimonio
                                    elif ("pasivo" in nombre_agrupador.lower() or "patrimonio" in nombre_agrupador.lower()) and valor_pasivos_totales and valor_patrimonio:
                                        proporcion_cuenta = round(abs(float(valor_cuenta / (valor_pasivos_totales + valor_patrimonio) * 100)), 2)
                                elif ef.int_tipoEstadoFinanciero == 2:
                                    # Para estado de resultados
                                    if valor_ventas_netas and (nombre_agrupador in ['ventas netas', 'costo de ventas'] or ('venta' in nombre_agrupador.lower() and ('costo' not in nombre_agrupador.lower() or 'netas' in nombre_agrupador.lower()))):
                                        # Respecto a ventas netas
                                        proporcion_cuenta = round(abs(float(valor_cuenta / valor_ventas_netas * 100)), 2)
                                    elif valores_agrupadores_especiales and valores_agrupadores_especiales['ER-UB']:
                                        # Respecto a utilidad bruta
                                        proporcion_cuenta = round(abs(float(valor_cuenta / valores_agrupadores_especiales['ER-UB'] * 100)), 2)

                                cuenta_data = {
                                    "nombre_cuenta": cuenta.str_nombre,
                                    "codigo_cuenta": cuenta.str_codigo,
                                    "valor_cuenta": float(valor_cuenta),
                                    "variacion": variacion_cuenta,
                                    "proporcion": proporcion_cuenta
                                }

                                cuentas_data.append(cuenta_data)
                            except EFCuenta.DoesNotExist:
                                continue

                        # Crear el objeto de datos para este agrupador
                        agrupador_data = {
                            # Mostra el nombre del subagrupador si existe, si no mostrar el nombre del agrupador
                            "nombre": nombre_subagrupador if nombre_subagrupador else nombre_agrupador,
                            "cuentas": cuentas_data,
                            "valor": float(valor_agrupador),
                            "variacion": variacion_agrupador,
                            "proporcion": proporcion_agrupador
                        }

                        ef_data["agrupadores"].append(agrupador_data)
                        agrupadores_procesados[clave_agrupador] = True

                # Agregar agrupadores especiales según el tipo de estado financiero
                if ef.int_tipoEstadoFinanciero == 1:
                    # Agregar activos totales para el tipo 1
                    if valor_activos_totales is not None:
                        # Calcular la variación respecto al periodo anterior
                        variacion_activos_totales = None
                        if ef_anterior:
                            activo_corriente_anterior = None
                            activo_no_corriente_anterior = None

                            totales_anteriores = TotalAgrupador.objects.filter(
                                int_idEstadoFinanciero=ef_anterior
                            )

                            for total_ant in totales_anteriores:
                                nombre_agrupador = total_ant.int_idAgrupador.str_nombre.lower()
                                nombre_subagrupador = total_ant.int_idAgrupador.str_nombre_subagrupador.lower() if total_ant.int_idAgrupador.str_nombre_subagrupador else None

                                # Buscar activo corriente
                                if "activo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                                    activo_corriente_anterior = total_ant.db_resultadoAgrupador

                                # Buscar activo no corriente
                                elif "activo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                                    activo_no_corriente_anterior = total_ant.db_resultadoAgrupador

                            # Calcular activos totales del periodo anterior
                            if activo_corriente_anterior is not None and activo_no_corriente_anterior is not None:
                                activos_totales_anterior = activo_corriente_anterior + activo_no_corriente_anterior

                                # Calcular la variación porcentual
                                if activos_totales_anterior != 0:
                                    variacion_abs = (valor_activos_totales - activos_totales_anterior)
                                    variacion = variacion_abs / activos_totales_anterior * 100
                                    variacion_activos_totales = round(float(variacion),2)
                                elif activos_totales_anterior == 0 and valor_activos_totales == 0:
                                    variacion_activos_totales = 0.00

                        # Agregar activos totales al resultado
                        ef_data["agrupadores"].append({
                            "nombre": "activos totales",
                            "cuentas": None,
                            "valor": float(valor_activos_totales),
                            "variacion": variacion_activos_totales,
                            "proporcion": 100.00  # Los activos totales representan el 100% de sí mismos
                        })

                    # Agregar pasivos totales
                    if valor_pasivos_totales is not None:
                        # Calcular la variación respecto al periodo anterior
                        variacion_pasivos_totales = None
                        if ef_anterior:
                            pasivo_corriente_anterior = None
                            pasivo_no_corriente_anterior = None

                            totales_anteriores = TotalAgrupador.objects.filter(
                                int_idEstadoFinanciero=ef_anterior
                            )

                            for total_ant in totales_anteriores:
                                nombre_agrupador = total_ant.int_idAgrupador.str_nombre.lower()
                                nombre_subagrupador = total_ant.int_idAgrupador.str_nombre_subagrupador.lower() if total_ant.int_idAgrupador.str_nombre_subagrupador else None

                                # Buscar pasivo corriente
                                if "pasivo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                                    pasivo_corriente_anterior = total_ant.db_resultadoAgrupador

                                # Buscar pasivo no corriente
                                elif "pasivo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                                    pasivo_no_corriente_anterior = total_ant.db_resultadoAgrupador

                            # Calcular pasivos totales del periodo anterior
                            if pasivo_corriente_anterior is not None and pasivo_no_corriente_anterior is not None:
                                pasivos_totales_anterior = pasivo_corriente_anterior + pasivo_no_corriente_anterior

                                # Calcular la variación porcentual
                                if pasivos_totales_anterior != 0:
                                    variacion_abs = (valor_pasivos_totales - pasivos_totales_anterior)
                                    variacion = variacion_abs / pasivos_totales_anterior * 100
                                    variacion_pasivos_totales = round(float(variacion),2)
                                elif pasivos_totales_anterior == 0 and valor_pasivos_totales == 0:
                                    variacion_pasivos_totales = 0.00

                        # Calcular la proporción respecto a pasivos totales + patrimonio
                        proporcion_pasivos_totales = None
                        if valor_pasivos_totales is not None and valor_patrimonio is not None:
                            proporcion_pasivos_totales = round(abs(float(valor_pasivos_totales / (valor_pasivos_totales + valor_patrimonio) * 100)), 2)

                        # Agregar pasivos totales al resultado
                        ef_data["agrupadores"].append({
                            "nombre": "pasivos totales",
                            "cuentas": None,
                            "valor": float(valor_pasivos_totales),
                            "variacion": variacion_pasivos_totales,
                            "proporcion": proporcion_pasivos_totales
                        })

                # Si es un Estado de Resultados (tipo 2), agregar los agrupadores especiales
                elif ef.int_tipoEstadoFinanciero == 2:
                    # Agregar los agrupadores especiales
                    agrupadores_especiales_nombres = {
                        'ER-UB': 'Utilidad Bruta',
                        'ER-UO': 'Utilidad Operativa',
                        'ER-UAIR': 'Utilidad antes de impuesto a la renta',
                        'ER-UN': 'Utilidad neta'
                    }

                    for codigo, valor in valores_agrupadores_especiales.items():
                        # Calcular la variación respecto al periodo anterior
                        variacion_agrupador_especial = None
                        if ef_anterior:
                            try:
                                # Buscar el agrupador en el periodo anterior
                                agrupador_anterior = Agrupador.objects.filter(str_nombre=codigo).first()
                                if agrupador_anterior:
                                    total_anterior = TotalAgrupador.objects.filter(
                                        int_idEstadoFinanciero=ef_anterior,
                                        int_idAgrupador=agrupador_anterior
                                    ).first()

                                    if total_anterior and total_anterior.db_resultadoAgrupador != 0:
                                        variacion_abs = (valor - total_anterior.db_resultadoAgrupador)
                                        variacion = variacion_abs / total_anterior.db_resultadoAgrupador * 100
                                        variacion_agrupador_especial = round(float(variacion),2)
                                    elif total_anterior and total_anterior.db_resultadoAgrupador == 0 and valor == 0:
                                        variacion_agrupador_especial = 0.00
                            except Exception:
                                pass

                        # Calcular la proporción respecto a ventas netas
                        proporcion_agrupador_especial = None
                        if valor_ventas_netas and codigo == 'ER-UB':
                            # Respecto a ventas netas
                            proporcion_agrupador_especial = round(abs(float(valor / valor_ventas_netas * 100)), 2)
                        else:
                            # Respecto a utilidad bruta
                            proporcion_agrupador_especial = round(abs(float(valor / valores_agrupadores_especiales['ER-UB'] * 100)), 2)

                        # Agregar el agrupador especial al resultado
                        ef_data["agrupadores"].append({
                            "nombre": agrupadores_especiales_nombres.get(codigo, codigo),
                            "cuentas": None,
                            "valor": float(valor),
                            "variacion": variacion_agrupador_especial,
                            "proporcion": proporcion_agrupador_especial
                        })

                resultado.append(ef_data)

            return MiddleResponse(
                message="Datos de cuentas por agrupador obtenidos exitosamente",
                data=resultado,
                state=True
            )

        except Exception as e:
            return MiddleResponse(
                message=f"Error al obtener las cuentas por agrupador: {str(e)}",
                state=False
            )

    def _obtener_estado_financiero_anterior(self, estado_financiero: EstadoFinanciero, tipo_periodo, id_empresa):
        """
        Obtiene el estado financiero del periodo anterior según el tipo de periodo.
        Por ejemplo, si es mensual, obtiene el mes anterior; si es anual, obtiene el año anterior.

        Args:
            estado_financiero (EstadoFinanciero): Estado financiero actual
            tipo_periodo (int): ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
            id_empresa (int): ID de la empresa

        Returns:
            EstadoFinanciero: Estado financiero del periodo anterior o None si no existe
        """
        try:
            fecha_actual = estado_financiero.dt_fechaFinPeriodo

            # Determinar la fecha del periodo anterior según el tipo de periodo
            if tipo_periodo == 1:  # Anual
                # Para periodos anuales, buscar el año anterior
                año_anterior = fecha_actual.year - 1
                # Filtrar por el año anterior manteniendo el mismo mes y día
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                    dt_fechaFinPeriodo__year=año_anterior,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero  # Mismo tipo de estado financiero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 2:  # Trimestral
                # Para periodos trimestrales, buscar el trimestre anterior
                # Determinar el trimestre actual (1-4)
                trimestre_actual = (fecha_actual.month - 1) // 3 + 1
                año = fecha_actual.year

                if trimestre_actual == 1:
                    # Si es el primer trimestre, el anterior es el cuarto del año pasado
                    trimestre_anterior = 4
                    año = año - 1
                else:
                    # Si no, es el trimestre anterior del mismo año
                    trimestre_anterior = trimestre_actual - 1

                # Calcular el mes de inicio del trimestre anterior
                mes_inicio = (trimestre_anterior - 1) * 3 + 1
                # Calcular el mes de fin del trimestre anterior
                mes_fin = trimestre_anterior * 3

                # Filtrar por el trimestre anterior
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=año,
                    dt_fechaFinPeriodo__month__gte=mes_inicio,
                    dt_fechaFinPeriodo__month__lte=mes_fin,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 3:  # Mensual
                # Para periodos mensuales, buscar el mes anterior
                if fecha_actual.month == 1:
                    # Si es enero, el mes anterior es diciembre del año pasado
                    mes_anterior = 12
                    año_anterior = fecha_actual.year - 1
                else:
                    # Si no, es el mes anterior del mismo año
                    mes_anterior = fecha_actual.month - 1
                    año_anterior = fecha_actual.year

                # Filtrar por el mes anterior
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=año_anterior,
                    dt_fechaFinPeriodo__month=mes_anterior,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 4:  # Semanal
                # Para periodos semanales, buscar la semana anterior
                # Restar 7 días a la fecha actual
                fecha_anterior = fecha_actual - timedelta(days=7)

                # Filtrar por la semana anterior (aproximadamente)
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=fecha_anterior.year,
                    dt_fechaFinPeriodo__month=fecha_anterior.month,
                    dt_fechaFinPeriodo__day__gte=max(1, fecha_anterior.day - 3),  # Aproximación
                    dt_fechaFinPeriodo__day__lte=min(31, fecha_anterior.day + 3),  # Aproximación
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 5:  # Diario
                # Para periodos diarios, buscar el día anterior
                fecha_anterior = fecha_actual - timedelta(days=1)

                # Filtrar por el día anterior
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo=fecha_anterior,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            else:
                # Para cualquier otro tipo de periodo, usar el método original
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__lt=fecha_actual,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            # Retornar el primer resultado si existe
            if efs_anteriores.exists():
                return efs_anteriores.first()
            return None

        except Exception as e:
            print(f"Error al obtener estado financiero anterior: {str(e)}")
            return None