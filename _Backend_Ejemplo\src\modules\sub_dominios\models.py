from django.db import models

from src.modules.dominios.models import Dominio


# Create your models here.
class SubDominio(models.Model):
    class Meta:
        db_table = "tr_sub_dominios"
        managed = True
        verbose_name = "sub_dominio"
        verbose_name_plural = "sub_dominios"

    int_idSubDominio = models.AutoField(primary_key=True)
    str_nombre = models.CharField(max_length=255)
    int_idDominio = models.ForeignKey(
        Dominio,
        related_name="sub_dominios",
        on_delete=models.CASCADE,
        db_column="int_idDominio",
    )
