import React from "react";
import { CircleCheckSolid } from "../../assets/SVG/CircleCheckSolid";

const ProgressLine = () => {
  const stages = [
    { id: "01", name: "Definici<PERSON>", completed: true, actual: false },
    { id: "02", name: "To be", completed: true, actual: false },
    { id: "03", name: "AS-IS", completed: false, actual: true },
    { id: "04", name: "Evaluación Final", completed: false, actual: false },
  ];

  return (
    <div className="flex items-center justify-between w-full  poppins-font-500 text-xs md:text-[0.75rem]">
      {stages.map((stage, index) => (
        <React.Fragment key={stage.id}>
          <div className="flex flex-col items-center ">
            <div
              className={`
              w-[1.8rem] h-[1.8rem] rounded-full flex items-center justify-center
              ${
                stage.completed
                  ? " text-white"
                  : "border-2 border-[#C1C0C0] text-[#909090]"
              }
                  ${stage.actual ? "text-[#156CFF] border-[#156CFF] drop-shadow-[0_0_0_rgba(255,206,128,0.4)] shadow-[0_0_0_3px_rgba(255,206,128,0.5)]  bg-white" : ""}
            `}
            >
              {stage.completed ? (
                <CircleCheckSolid color="#47D691" />
              ) : (
                stage.id
              )}
            </div>
            <span
              className={`
              text-[0.6rem] md:text-sm
              ${stage.completed ? "" : "text-[#909090]"}
            `}
            >
              {stage.name}
            </span>
          </div>
          {index < stages.length - 1 && (
            <div
              className={`
              flex-grow h-0.5 w-[10.0625rem]
              ${stage.completed ? "bg-[#47D691]" : "bg-gray-200"}
            `}
            ></div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default ProgressLine;
