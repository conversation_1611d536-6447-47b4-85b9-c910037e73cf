from openpyxl import Workbook
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.workbook.defined_name import DefinedName
from openpyxl.styles import PatternFill, Border, Side, Alignment, Font
from django.db.models import Q
from django.http import FileResponse
import os
from datetime import datetime

class PlantillaController:
    def __init__(self, title="Plantilla de EEFF"):
        self.title=title
        self.wb = Workbook()
        self.ws = self.wb.active
        self.ws.title = title
        self.archivo = None

    def __get_docs(self, path):
        if not os.path.exists(path):
            None

        nombre_archivo = os.path.basename(path)

        archivo_response = FileResponse(open(path, "rb"), as_attachment=True)
        archivo_response["Content-Disposition"] = (
            f'attachment; filename="{nombre_archivo}"'
        )
        archivo_response["Content-Type"] = "application/octet-stream"

        return archivo_response

    def __ajustar_ancho_columnas(self):
        """Ajusta el ancho de las columnas en la hoja activa."""
        for columna in self.ws.columns:
            max_length = 0
            columna_letra = columna[0].column_letter
            for celda in columna:
                try:
                    if len(str(celda.value)) > max_length:
                        max_length = len(str(celda.value))
                except:
                    pass
            adjusted_width = max_length + 2
            self.ws.column_dimensions[columna_letra].width = adjusted_width

    def __ajustar_ancho_columnas_hoja(self, hoja):
        """Ajusta el ancho de las columnas en una hoja específica."""
        for columna in hoja.columns:
            max_length = 0
            columna_letra = columna[0].column_letter
            for celda in columna:
                try:
                    if len(str(celda.value)) > max_length:
                        max_length = len(str(celda.value))
                except:
                    pass
            adjusted_width = max_length + 2
            hoja.column_dimensions[columna_letra].width = adjusted_width

    def __ajustar_altura_filas(self):

        for fila in self.ws.iter_rows():
            max_height = 0
            for celda in fila:
                try:
                    if len(str(celda.value)) > max_height:
                        max_height = len(str(celda.value))
                except:
                    pass
            self.ws.row_dimensions[celda.row].height = max_height * 1.2

    def __agregar_borde(self):
        for fila in self.ws.iter_rows():
            border = Border(
                top=Side(style="thin", color="000000"),
                bottom=Side(style="thin", color="000000"),
                left=Side(style="thin", color="000000"),
                right=Side(style="thin", color="000000"),
            )
            for celda in fila:
                celda.border = border

    def __estilo_cabecera(self, celdas, color):
        """Aplica estilos a las celdas de cabecera."""
        # Aseguramos que el color tenga el formato correcto (FF + 6 caracteres hex)
        color = color.replace('#', '').upper()
        if len(color) == 6:
            color = f"FF{color}"  # Añadimos FF al inicio para opacidad completa

        cabecera_fill = PatternFill(
            start_color=color,
            end_color=color,
            fill_type="solid"
        )
        cabecera_font = Font(
            bold=True,
            color="FFFFFF",
            size=11
        )
        cabecera_align = Alignment(
            horizontal="center",
            vertical="center",
            wrap_text=True
        )

        for celda in celdas:
            celda.fill = cabecera_fill
            celda.font = cabecera_font
            celda.alignment = cabecera_align

    def __background_color(self, celdas, color):
        cabecera_fill = PatternFill(
            start_color=color, fill_type="solid"
        )  # Cambiado a start_color
        cabecera_align = Alignment(horizontal="center", vertical="center")
        for celda in celdas:
            celda.fill = cabecera_fill
            celda.alignment = cabecera_align

    def __centrar_celdas(self):
        for fila in self.ws.iter_rows():
            for celda in fila:
                celda.alignment = Alignment(horizontal="center", vertical="center")

    def __font_color(self, celdas, color):
        cabecera_font = Font(color=color)
        for celda in celdas:
            celda.font = cabecera_font

    def descargar_plantilla_ejemplo(self):
        try:
            # Cabeceras de la plantilla
            self.ws["A1"] = "[Tipo Estado Financiero]"
            self.ws["B1"] = "[Tipo Periodo]"
            self.ws["C1"] = "[Fecha Inicio Periodo]"
            self.ws["D1"] = "[Fecha Fin Periodo]"
            self.ws["E1"] = "[# ID Agrupador]"
            self.ws["F1"] = "[Nombre Agrupador]"
            self.ws["G1"] = "[# ID Subagrupador]"
            self.ws["H1"] = "[Nombre Subagrupador]"
            self.ws["I1"] = "[# ID Agrupador]"
            self.ws["J1"] = "[# ID Subagrupador]"
            self.ws["K1"] = "[Nombre Cuenta]"
            self.ws["L1"] = "[Código Cuenta]"
            self.ws["M1"] = "[Valor Monetario]"

            # Datos de ejemplo para la primera columna - Tipo Estado Financiero
            self.ws["A2"] = "1: Estado de Situación Financiera, 2: Estado de Resultados"

            # Datos de ejemplo para la segunda columna - Tipo Periodo
            self.ws["B2"] = "1: Anual, 2: Trimestral, 3: Mensual"
            self.ws["B3"] = ""
            self.ws["B4"] = ""

            # Datos de ejemplo para fechas
            self.ws["C2"] = "DD/MM/YYYY"
            self.ws["D2"] = "DD/MM/YYYY"

            # Datos de ejemplo para agrupadores
            self.ws["E2"] = "1"
            self.ws["F2"] = "Activos"
            self.ws["E3"] = "2"
            self.ws["F3"] = "Pasivos"
            self.ws["E4"] = "3"
            self.ws["F4"] = "Patrimonio"

            # Datos de ejemplo para subagrupadores
            self.ws["G2"] = "1"
            self.ws["H2"] = "Activo Corriente"
            self.ws["I2"] = "1"

            self.ws["G3"] = "2"
            self.ws["H3"] = "Activo No Corriente"
            self.ws["I3"] = "1"

            self.ws["G4"] = "3"
            self.ws["H4"] = "Pasivo Corriente"
            self.ws["I4"] = "2"

            self.ws["G5"] = "4"
            self.ws["H5"] = "Pasivo No Corriente"
            self.ws["I5"] = "2"

            # Datos de ejemplo para cuentas
            self.ws["J2"] = "1"
            self.ws["K2"] = "Efectivo y equivalente de efectivo"
            self.ws["L2"] = "10000"
            self.ws["M2"] = "26,478,194.00"

            self.ws["J3"] = "1"
            self.ws["K3"] = "Cuentas por cobrar comerciales"
            self.ws["L3"] = "12000"
            self.ws["M3"] = "5,836,886.00"

            self.ws["J4"] = "1"
            self.ws["K4"] = "Cuentas por cobrar a relacionadas"
            self.ws["L4"] = "13000"
            self.ws["M4"] = "0.00"

            self.ws["J5"] = "2"
            self.ws["K5"] = "Otras cuentas por cobrar"
            self.ws["L5"] = "16000"
            self.ws["M5"] = "18,355.00"

            self.ws["J6"] = "2"
            self.ws["K6"] = "Inversiones a largo plazo"
            self.ws["L6"] = "30000"
            self.ws["M6"] = "2,241,525.00"

            self.ws["J7"] = "2"
            self.ws["K7"] = "Inversiones inmobiliarias"
            self.ws["L7"] = "31000"
            self.ws["M7"] = "12,237,781.00"

            self.ws["J8"] = "2"
            self.ws["K8"] = "Propiedad, planta y equipo, neto"
            self.ws["L8"] = "33000"
            self.ws["M8"] = "42,035,142.00"

            self.ws["J9"] = "3"
            self.ws["K9"] = "Capital Social"
            self.ws["L9"] = "50000"
            self.ws["M9"] = "35,070,000.00"

            self.ws["J10"] = "3"
            self.ws["K10"] = "Reserva Legal"
            self.ws["L10"] = "58000"
            self.ws["M10"] = "3,375,077.00"

            self.ws["J11"] = "3"
            self.ws["K11"] = "Resultado del Ejercicio"
            self.ws["L11"] = "57000"
            self.ws["M11"] = "1,213,163.00"

            self.ws["J12"] = "3"
            self.ws["K12"] = "Resultados acumulados"
            self.ws["L12"] = "59000"
            self.ws["M12"] = "24,244,119.00"

            # Aplicar estilos a las cabeceras
            self.__estilo_cabecera(
                [self.ws["A1"], self.ws["B1"], self.ws["C1"], self.ws["D1"]],
                "4472C4"  # Azul
            )

            self.__estilo_cabecera(
                [self.ws["E1"], self.ws["F1"]],
                "ED7D31"  # Naranja
            )

            self.__estilo_cabecera(
                [self.ws["G1"], self.ws["H1"], self.ws["I1"]],
                "A5A5A5"  # Gris
            )

            self.__estilo_cabecera(
                [self.ws["J1"], self.ws["K1"], self.ws["L1"]],
                "70AD47"  # Verde
            )

            self.__estilo_cabecera(
                [self.ws["M1"]],
                "4472C4"  # Azul
            )

            # Agregar bordes a todas las celdas utilizadas
            for fila in self.ws.iter_rows(min_row=1, max_row=12, min_col=1, max_col=13):
                border = Border(
                    top=Side(style="thin", color="000000"),
                    bottom=Side(style="thin", color="000000"),
                    left=Side(style="thin", color="000000"),
                    right=Side(style="thin", color="000000"),
                )
                for celda in fila:
                    celda.border = border

            # Centrar todas las celdas
            self.__centrar_celdas()

            # Ajustar el ancho de las columnas
            self.__ajustar_ancho_columnas()

            # Guardar el archivo
            self.wb.save(f"{self.title}.xlsx")
            return self.__get_docs(f"{self.title}.xlsx")

        except Exception as e:
            print(f"Error al descargar la plantilla: {str(e)}")
            return e

    def descargar_plantilla(self):
        try:
            # Crear un nuevo libro de trabajo
            self.wb = Workbook()

            # Configurar la primera hoja: "Plantilla EEFF"
            self.ws = self.wb.active
            self.ws.title = "Plantilla EEFF"

            # Crear la estructura de la primera hoja
            self.__crear_hoja_plantilla(self.ws)

            # Crear la segunda hoja: "Ejemplo de Estado de Situación Financiera"
            ejemplo_ESF_ws = self.wb.create_sheet(title="Ejemplo ESF")
            self.__crear_hoja_ejemplo_ESF(ejemplo_ESF_ws)

            # Crear la tercera hoja: "Ejemplo de Estado de Resultados"
            ejemplo_ER_ws = self.wb.create_sheet(title="Ejemplo ER")
            self.__crear_hoja_ejemplo_ER(ejemplo_ER_ws)

            # Crear la cuarta hoja: "Formato Codigo"
            formato_ws = self.wb.create_sheet(title="Formato Codigo")
            self.__crear_hoja_formato_codigo(formato_ws)

            # Guardar el archivo
            self.wb.save(f"{self.title}.xlsx")
            return self.__get_docs(f"{self.title}.xlsx")

        except Exception as e:
            print(f"Error al descargar la plantilla: {str(e)}")
            return e

    def __crear_hoja_plantilla(self, hoja):
        """Crea la estructura de la hoja de plantilla"""
        # Encabezados de la primera sección
        hoja["A1"] = "[Tipo Estado Financiero]"
        hoja["B1"] = "1: Estado de Situación Financiera, 2: Estado de Resultados"

        hoja["A2"] = "[Tipo Periodo]"
        hoja["B2"] = "1: Anual, 2: Trimestral, 3: Mensual"

        hoja["A3"] = "[Fecha Inicio Periodo]"
        hoja["B3"] = "DD/MM/YYYY"

        hoja["A4"] = "[Fecha Fin Periodo]"
        hoja["B4"] = "DD/MM/YYYY"

        # Sección de agrupador
        hoja["A6"] = "[# Agrupador]"

        hoja["A7"] = "Nombre Agrupador"

        # Sección de cuentas
        hoja["A8"] = "[Nombre Cuenta]"
        hoja["B8"] = "[Codigo Cuenta]"
        hoja["C8"] = "[Valor Monetario]"

        hoja["A9"] = "Nombre Cuenta"
        hoja["B9"] = "Codigo de 5 digitos (ABCDE)"
        hoja["C9"] = "(+/-)Valor Monetario"

        hoja["A10"] = "Nombre Cuenta"
        hoja["B10"] = "Codigo de 5 digitos (ABCDE)"
        hoja["C10"] = "(+/-)Valor Monetario"

        hoja["A11"] = "Nombre Cuenta"
        hoja["B11"] = "Codigo de 5 digitos (ABCDE)"
        hoja["C11"] = "(+/-)Valor Monetario"

        hoja["A12"] = "[# Total - Agrupador]"
        hoja["C12"] = "(+/-)Valor Monetario"

        # Segunda sección de agrupador
        hoja["A15"] = "[# Agrupador]"

        hoja["A16"] = "Nombre Agrupador"

        # Sección de subagrupador
        hoja["A18"] = "[## Subagrupador]"

        hoja["A19"] = "Nombre Subagrupador"

        # Sección de cuentas del subagrupador
        hoja["A20"] = "[Nombre Cuenta]"
        hoja["B20"] = "[Codigo Cuenta]"
        hoja["C20"] = "[Valor Monetario]"

        hoja["A21"] = "Nombre Cuenta"
        hoja["B21"] = "Codigo de 5 digitos (ABCDE)"
        hoja["C21"] = "(+/-)Valor Monetario"

        hoja["A22"] = "Nombre Cuenta"
        hoja["B22"] = "Codigo de 5 digitos (ABCDE)"
        hoja["C22"] = "(+/-)Valor Monetario"

        hoja["A23"] = "Nombre Cuenta"
        hoja["B23"] = "Codigo de 5 digitos (ABCDE)"
        hoja["C23"] = "(+/-)Valor Monetario"

        hoja["A24"] = "[## Total]"
        hoja["C24"] = "(+/-)Valor Monetario"

        # Segundo subagrupador
        hoja["A26"] = "[## Subagrupador]"

        hoja["A27"] = "Nombre Subagrupador"

        # Sección de cuentas del segundo subagrupador
        hoja["A28"] = "[Nombre Cuenta]"
        hoja["B28"] = "[Codigo Cuenta]"
        hoja["C28"] = "[Valor Monetario]"

        hoja["A29"] = "Nombre Cuenta"
        hoja["B29"] = "Codigo de 5 digitos (ABCDE)"
        hoja["C29"] = "(+/-)Valor Monetario"

        hoja["A30"] = "Nombre Cuenta"
        hoja["B30"] = "Codigo de 5 digitos (ABCDE)"
        hoja["C30"] = "(+/-)Valor Monetario"

        hoja["A31"] = "Nombre Cuenta"
        hoja["B31"] = "Codigo de 5 digitos (ABCDE)"
        hoja["C31"] = "(+/-)Valor Monetario"

        hoja["A32"] = "[## Total]"
        hoja["C32"] = "(+/-)Valor Monetario"

        hoja["A34"] = "[# Total - Agrupador]"
        hoja["C34"] = "(+/-)Valor Monetario"

        # Aplicar estilos
        # Estilo para encabezados principales (azul)
        self.__estilo_cabecera(
            [hoja["A1"], hoja["A2"], hoja["A3"], hoja["A4"]],
            "4472C4"  # Azul
        )

        # Estilo para agrupadores (negro)
        self.__estilo_cabecera(
            [hoja["A6"], hoja["A15"], hoja["A34"], hoja["A12"]],
            "000000"  # Negro
        )

        # Estilo para subagrupadores (negro)
        self.__estilo_cabecera(
            [hoja["A18"], hoja["A26"], hoja["A24"], hoja["A32"]],
            "000000"  # Negro
        )

        # Estilo para encabezados de cuentas (verde)
        self.__estilo_cabecera(
            [hoja["A8"], hoja["B8"], hoja["C8"], hoja["A20"], hoja["B20"], hoja["C20"], hoja["A28"], hoja["B28"], hoja["C28"]],
            "70AD47"  # Verde
        )

        # Estilo para nombres de agrupadores y subagrupadores (naranja claro)
        # self.__background_color(
        #     [hoja["A7"], hoja["A16"], hoja["A19"], hoja["A27"]],
        #     "FFCC99"  # Naranja claro
        # )

        # Estilo para filas de cuentas (naranja claro)
        # self.__background_color(
        #     [
        #         hoja["A9"], hoja["B9"], hoja["C9"],
        #         hoja["A10"], hoja["B10"], hoja["C10"],
        #         hoja["A11"], hoja["B11"], hoja["C11"],
        #         hoja["A21"], hoja["B21"], hoja["C21"],
        #         hoja["A22"], hoja["B22"], hoja["C22"],
        #         hoja["A23"], hoja["B23"], hoja["C23"],
        #         hoja["A29"], hoja["B29"], hoja["C29"],
        #         hoja["A30"], hoja["B30"], hoja["C30"],
        #         hoja["A31"], hoja["B31"], hoja["C31"]
        #     ],
        #     "FFCC99"  # Naranja claro
        # )

        # Agregar bordes a todas las celdas utilizadas
        for fila in range(1, 35):
            for col in range(1, 4):  # Columnas A, B, C
                celda = hoja.cell(row=fila, column=col)
                if celda.value is not None:
                    celda.border = Border(
                        top=Side(style="thin", color="000000"),
                        bottom=Side(style="thin", color="000000"),
                        left=Side(style="thin", color="000000"),
                        right=Side(style="thin", color="000000"),
                    )

        # Ajustar el ancho de las columnas
        self.__ajustar_ancho_columnas_hoja(hoja)

    def __crear_hoja_ejemplo_ESF(self, hoja):
        """Crea la estructura de la hoja de ejemplo de Estado de Situación Financiera"""
        # Encabezados de la primera sección
        hoja["A1"] = "[Tipo Estado Financiero]"
        hoja["B1"] = "1"

        hoja["A2"] = "[Tipo Periodo]"
        hoja["B2"] = "3"

        hoja["A3"] = "[Fecha Inicio Periodo]"
        hoja["B3"] = "1/2/2025"

        hoja["A4"] = "[Fecha Fin Periodo]"
        hoja["B4"] = "28/2/2025"

        # Sección de agrupador - Activos
        hoja["A6"] = "[# Agrupador]"

        hoja["A7"] = "Activos"

        # Sección de subagrupador - Activo Corriente
        hoja["A9"] = "[## Subagrupador]"

        hoja["A10"] = "Activo Corriente"

        # Sección de cuentas de Activo Corriente
        hoja["A11"] = "[Nombre Cuenta]"
        hoja["B11"] = "[Codigo Cuenta]"
        hoja["C11"] = "[Valor Monetario]"

        hoja["A12"] = "Efectivo y equivalente de efectivo"
        hoja["B12"] = "40000"
        hoja["C12"] = "26,478,194.00"

        hoja["A13"] = "Cuentas por cobrar comerciales"
        hoja["B13"] = "12000"
        hoja["C13"] = "5,836,886.00"

        hoja["A14"] = "Cuentas por cobrar a relacionadas"
        hoja["B14"] = "13000"
        hoja["C14"] = "-"

        hoja["A15"] = "Otras cuentas por cobrar"
        hoja["B15"] = "14001"
        hoja["C15"] = "5,833,347.00"

        hoja["A16"] = "Inventarios"
        hoja["B16"] = "14002"
        hoja["C16"] = "2,487,221.00"

        hoja["A17"] = "Activos no corrientes mantenidos para la venta"
        hoja["B17"] = "14003"
        hoja["C17"] = "-"

        hoja["A18"] = "Gastos contratados por anticipado"
        hoja["B18"] = "14004"
        hoja["C18"] = "420,975.00"

        hoja["A19"] = "[## Total]"
        hoja["C19"] = "41,056,623.00"

        # Sección de subagrupador - Activo No Corriente
        hoja["A21"] = "[## Subagrupador]"

        hoja["A22"] = "Activo No Corriente"

        # Sección de cuentas de Activo No Corriente
        hoja["A23"] = "[Nombre Cuenta]"
        hoja["B23"] = "[Codigo Cuenta]"
        hoja["C23"] = "[Valor Monetario]"

        hoja["A24"] = "Otras cuentas por cobrar"
        hoja["B24"] = "16000"
        hoja["C24"] = "18,355.00"

        hoja["A25"] = "Inversiones a largo plazo"
        hoja["B25"] = "30000"
        hoja["C25"] = "2,241,525.00"

        hoja["A26"] = "Inversiones inmobiliarias"
        hoja["B26"] = "31000"
        hoja["C26"] = "12,237,781.00"

        hoja["A27"] = "Propiedad, planta y equipo, neto"
        hoja["B27"] = "33000"
        hoja["C27"] = "42,035,142.00"

        hoja["A28"] = "Activos por derecho de uso, neto"
        hoja["B28"] = "34001"
        hoja["C28"] = "829,697.00"

        hoja["A29"] = "Activos intangibles, neto"
        hoja["B29"] = "34002"
        hoja["C29"] = "354,345.00"

        hoja["A30"] = "Otros activos"
        hoja["B30"] = "34003"
        hoja["C30"] = "-"

        hoja["A31"] = "[## Total]"
        hoja["C31"] = "57,716,845.00"

        hoja["A33"] = "[# Total - Agrupador]"
        hoja["C33"] = "98,773,468.00"

        # Sección de agrupador - Pasivos
        hoja["A36"] = "[# Agrupador]"

        hoja["A37"] = "Pasivos"

        # Sección de subagrupador - Pasivo Corriente
        hoja["A39"] = "[## Subagrupador]"

        hoja["A40"] = "Pasivo Corriente"

        # Sección de cuentas de Pasivo Corriente
        hoja["A41"] = "[Nombre Cuenta]"
        hoja["B41"] = "[Codigo Cuenta]"
        hoja["C41"] = "[Valor Monetario]"

        hoja["A42"] = "Obligaciones financieras"
        hoja["B42"] = "40000"
        hoja["C42"] = "3,694,109.00"

        hoja["A43"] = "Cuentas por pagar comerciales"
        hoja["B43"] = "42000"
        hoja["C43"] = "19,882,061.00"

        hoja["A44"] = "Pasivos por arrendamiento"
        hoja["B44"] = "43000"
        hoja["C44"] = "136,834.00"

        hoja["A45"] = "Otras cuentas por pagar"
        hoja["B45"] = "44001"
        hoja["C45"] = "2,239,479.00"

        hoja["A46"] = "[## Total]"
        hoja["C46"] = "25,952,483.00"

        # Sección de subagrupador - Pasivo No Corriente
        hoja["A48"] = "[## Subagrupador]"

        hoja["A49"] = "Pasivo No Corriente"

        # Sección de cuentas de Pasivo No Corriente
        hoja["A50"] = "[Nombre Cuenta]"
        hoja["B50"] = "[Codigo Cuenta]"
        hoja["C50"] = "[Valor Monetario]"

        hoja["A51"] = "Obligaciones financieras"
        hoja["B51"] = "46000"
        hoja["C51"] = "6,674,782.00"

        hoja["A52"] = "Pasivo por arrendamiento"
        hoja["B52"] = "46001"
        hoja["C52"] = "914,749.00"

        hoja["A53"] = "Otras cuentas por pagar"
        hoja["B53"] = "46002"
        hoja["C53"] = "-"

        hoja["A54"] = "Pasivo por impuestos diferidos"
        hoja["B54"] = "46003"
        hoja["C54"] = "1,329,095.00"

        hoja["A55"] = "[## Total]"
        hoja["C55"] = "8,918,626.00"

        hoja["A57"] = "[# Total - Agrupador]"
        hoja["C57"] = "34,871,109.00"

        # Sección de agrupador - Patrimonio
        hoja["A60"] = "[# Agrupador]"

        hoja["A61"] = "Patrimonio"

        # Sección de cuentas de Patrimonio
        hoja["A62"] = "[Nombre Cuenta]"
        hoja["B62"] = "[Codigo Cuenta]"
        hoja["C62"] = "[Valor Monetario]"

        hoja["A63"] = "Capital social"
        hoja["B63"] = "50000"
        hoja["C63"] = "35,070,000.00"

        hoja["A64"] = "Reserva legal"
        hoja["B64"] = "58000"
        hoja["C64"] = "3,375,077.00"

        hoja["A65"] = "Resultado del ejercicio"
        hoja["B65"] = "57000"
        hoja["C65"] = "1,213,163.00"

        hoja["A66"] = "Resultados acumulados"
        hoja["B66"] = "57001"
        hoja["C66"] = "24,244,119.00"

        hoja["A67"] = "Participación no controladora"
        hoja["B67"] = "59000"
        hoja["C67"] = "-"

        hoja["A68"] = "[# Total - Agrupador]"
        hoja["C68"] = "63,902,359.00"

        # Aplicar estilos
        # Estilo para encabezados principales (azul)
        self.__estilo_cabecera(
            [hoja["A1"], hoja["A2"], hoja["A3"], hoja["A4"]],
            "4472C4"  # Azul
        )

        # Estilo para agrupadores (negro)
        self.__estilo_cabecera(
            [hoja["A6"], hoja["A36"], hoja["A60"],
             hoja["A33"], hoja["A57"], hoja["A68"]],
            "000000"  # Negro
        )

        # Estilo para subagrupadores (negro)
        self.__estilo_cabecera(
            [hoja["A9"], hoja["A21"], hoja["A39"], hoja["A48"],
             hoja["A19"], hoja["A31"], hoja["A46"], hoja["A55"]],
            "000000"  # Negro
        )

        # Estilo para encabezados de cuentas (verde)
        self.__estilo_cabecera(
            [hoja["A11"], hoja["B11"], hoja["C11"],
             hoja["A23"], hoja["B23"], hoja["C23"],
             hoja["A41"], hoja["B41"], hoja["C41"],
             hoja["A50"], hoja["B50"], hoja["C50"],
             hoja["A62"], hoja["B62"], hoja["C62"]],
            "70AD47"  # Verde
        )

        # Agregar bordes a todas las celdas utilizadas
        for fila in range(1, 69):
            for col in range(1, 4):  # Columnas A, B, C
                celda = hoja.cell(row=fila, column=col)
                if celda.value is not None:
                    celda.border = Border(
                        top=Side(style="thin", color="000000"),
                        bottom=Side(style="thin", color="000000"),
                        left=Side(style="thin", color="000000"),
                        right=Side(style="thin", color="000000"),
                    )

        # Ajustar el ancho de las columnas
        self.__ajustar_ancho_columnas_hoja(hoja)

    def __crear_hoja_ejemplo_ER(self, hoja):
        """Crea la estructura de la hoja de ejemplo de Estado de Resultados"""
        # Encabezados de la primera sección
        hoja["A1"] = "[Tipo Estado Financiero]"
        hoja["B1"] = "2"

        hoja["A2"] = "[Tipo Periodo]"
        hoja["B2"] = "3"

        hoja["A3"] = "[Fecha Inicio Periodo]"
        hoja["B3"] = "1/2/2025"

        hoja["A4"] = "[Fecha Fin Periodo]"
        hoja["B4"] = "28/2/2025"

        # Sección de agrupador - Ventas Netas
        hoja["A6"] = "[# Agrupador]"
        hoja["A7"] = "Ventas Netas"

        # Sección de cuentas de Ventas Netas
        hoja["A8"] = "[Nombre Cuenta]"
        hoja["B8"] = "[Codigo Cuenta]"
        hoja["C8"] = "[Valor Monetario]"

        hoja["A9"] = "Ingreso de actividades ordinarias"
        hoja["B9"] = "70000"
        hoja["C9"] = "26,822,881.00"

        hoja["A10"] = "[# Total - Agrupador]"
        hoja["C10"] = "26,822,881.00"

        # Sección de agrupador - Costo de Ventas
        hoja["A12"] = "[# Agrupador]"
        hoja["A13"] = "Costo de Ventas"

        # Sección de cuentas de Costo de Ventas
        hoja["A14"] = "[Nombre Cuenta]"
        hoja["B14"] = "[Codigo Cuenta]"
        hoja["C14"] = "[Valor Monetario]"

        hoja["A15"] = "Costos de ventas"
        hoja["B15"] = "60100"
        hoja["C15"] = "-22,910,671.00"

        hoja["A16"] = "[# Total - Agrupador]"
        hoja["C16"] = "-22,910,671.00"

        # Utilidad bruta
        hoja["A18"] = "Utilidad bruta"
        hoja["B18"] = "ER-UB"
        hoja["C18"] = "3,912,210.00"

        # Sección de agrupador - Gastos Operacionales
        hoja["A21"] = "[# Agrupador]"
        hoja["A22"] = "Gastos Operacionales"

        # Sección de cuentas de Gastos Operacionales
        hoja["A23"] = "[Nombre Cuenta]"
        hoja["B23"] = "[Codigo Cuenta]"
        hoja["C23"] = "[Valor Monetario]"

        hoja["A24"] = "Gastos operativos"
        hoja["B24"] = "60101"
        hoja["C24"] = "-1,657,514.00"

        hoja["A25"] = "Gastos de administración"
        hoja["B25"] = "60200"
        hoja["C25"] = "-713,311.00"

        hoja["A26"] = "Gastos de ventas"
        hoja["B26"] = "60300"
        hoja["C26"] = "-91,757.00"

        hoja["A27"] = "[# Total - Agrupador]"
        hoja["C27"] = "-2,462,582.00"

        # Utilidad Operativa
        hoja["A29"] = "Utilidad Operativa"
        hoja["B29"] = "ER-UO"
        hoja["C29"] = "1,449,628.00"

        # Sección de agrupador - Ingresos y Gastos Financieros
        hoja["A31"] = "[# Agrupador]"
        hoja["A32"] = "Ingresos y Gastos Financieros"

        # Sección de cuentas de Ingresos y Gastos Financieros
        hoja["A33"] = "[Nombre Cuenta]"
        hoja["B33"] = "[Codigo Cuenta]"
        hoja["C33"] = "[Valor Monetario]"

        hoja["A34"] = "(Pérdida)/Ganancia neto por deterioro de cuentas por cobrar"
        hoja["B34"] = "60400"
        hoja["C34"] = "36,541.00"

        hoja["A35"] = "Otros ingresos"
        hoja["B35"] = "60500"
        hoja["C35"] = "1,763.00"

        hoja["A36"] = "Otros gastos"
        hoja["B36"] = "60600"
        hoja["C36"] = "-1,173.00"

        hoja["A37"] = "Ganancia/(pérdida) Venta de Activo fijo"
        hoja["B37"] = "60700"
        hoja["C37"] = "-"

        hoja["A38"] = "Variación en el valor de participación patrimonial"
        hoja["B38"] = "60800"
        hoja["C38"] = "-"

        hoja["A39"] = "Ingresos financieros"
        hoja["B39"] = "70107"
        hoja["C39"] = "378,847.00"

        hoja["A40"] = "Gastos financieros"
        hoja["B40"] = "60201"
        hoja["C40"] = "-105,901.00"

        hoja["A41"] = "Diferencia en cambio, neta"
        hoja["B41"] = "60301"
        hoja["C41"] = "-19,051.00"

        hoja["A42"] = "[# Total - Agrupador]"
        hoja["C42"] = "291,026.00"

        # Utilidad antes de impuesto a la renta
        hoja["A44"] = "Utilidad antes de impuesto a la renta"
        hoja["B44"] = "ER-UAIR"
        hoja["C44"] = "1,740,654.00"

        # Sección de agrupador - Impuestos
        hoja["A46"] = "[# Agrupador]"
        hoja["A47"] = "Impuestos"

        # Sección de cuentas de Impuestos
        hoja["A48"] = "[Nombre Cuenta]"
        hoja["B48"] = "[Codigo Cuenta]"
        hoja["C48"] = "[Valor Monetario]"

        hoja["A49"] = "Participación de los trabajadores"
        hoja["B49"] = "60103"
        hoja["C49"] = "-"

        hoja["A50"] = "Impuesto a la renta"
        hoja["B50"] = "60203"
        hoja["C50"] = "-527,491.00"

        hoja["A51"] = "[# Total - Agrupador]"
        hoja["C51"] = "-527,491.00"

        # Utilidad neta
        hoja["A53"] = "Utilidad neta"
        hoja["B53"] = "ER-UN"
        hoja["C53"] = "1,213,163.00"

        # Aplicar estilos
        # Estilo para encabezados principales (azul)
        self.__estilo_cabecera(
            [hoja["A1"], hoja["A2"], hoja["A3"], hoja["A4"]],
            "4472C4"  # Azul
        )

        # Estilo para agrupadores (negro)
        self.__estilo_cabecera(
            [hoja["A6"], hoja["A10"], hoja["A12"], hoja["A16"], hoja["A21"], 
             hoja["A27"], hoja["A31"], hoja["A42"], hoja["A46"], hoja["A51"],
             hoja["B18"], hoja["B29"], hoja["B44"], hoja["B53"],],
            "000000"  # Negro
        )

        # Estilo para encabezados de cuentas (verde)
        self.__estilo_cabecera(
            [hoja["A8"], hoja["B8"], hoja["C8"], hoja["A14"], hoja["B14"], hoja["C14"],
             hoja["A23"], hoja["B23"], hoja["C23"], hoja["A33"], hoja["B33"], hoja["C33"],
             hoja["A48"], hoja["B48"], hoja["C48"]],
            "70AD47"  # Verde
        )

        # Estilo para nombres de agrupadores (naranja claro)
        # self.__background_color(
        #     [hoja["A7"], hoja["A13"], hoja["A22"], hoja["A35"], hoja["A45"]],
        #     "FFCC99"  # Naranja claro
        # )

        # Estilo para filas de cuentas (naranja claro)
        # self.__background_color(
        #     [
        #         hoja["A9"], hoja["B9"], hoja["C9"],
        #         hoja["A15"], hoja["B15"], hoja["C15"],
        #         hoja["A24"], hoja["B24"], hoja["C24"],
        #         hoja["A25"], hoja["B25"], hoja["C25"],
        #         hoja["A26"], hoja["B26"], hoja["C26"],
        #         hoja["A27"], hoja["B27"], hoja["C27"],
        #         hoja["A28"], hoja["B28"], hoja["C28"],
        #         hoja["A29"], hoja["B29"], hoja["C29"],
        #         hoja["A30"], hoja["B30"], hoja["C30"],
        #         hoja["A31"], hoja["B31"], hoja["C31"],
        #         hoja["A37"], hoja["B37"], hoja["C37"],
        #         hoja["A38"], hoja["B38"], hoja["C38"],
        #         hoja["A39"], hoja["B39"], hoja["C39"],
        #         hoja["A47"], hoja["B47"], hoja["C47"],
        #         hoja["A48"], hoja["B48"], hoja["C48"]
        #     ],
        #     "FFCC99"  # Naranja claro
        # )

        # Estilo para totales y utilidades (negrita)
        for celda in [hoja["C10"], hoja["C16"],
                     hoja["A18"], hoja["C18"],
                     hoja["C27"], hoja["A29"], hoja["C29"],
                     hoja["C42"], hoja["A44"], hoja["C44"],
                     hoja["C51"], hoja["A53"], hoja["C53"]]:
            celda.font = Font(bold=True)

        # Agregar bordes a todas las celdas utilizadas
        for fila in range(1, 54):
            for col in range(1, 4):  # Columnas A, B, C
                celda = hoja.cell(row=fila, column=col)
                if celda.value is not None:
                    celda.border = Border(
                        top=Side(style="thin", color="000000"),
                        bottom=Side(style="thin", color="000000"),
                        left=Side(style="thin", color="000000"),
                        right=Side(style="thin", color="000000"),
                    )

        # Ajustar el ancho de las columnas
        self.__ajustar_ancho_columnas_hoja(hoja)


    def __crear_hoja_formato_codigo(self, hoja):
        """Crea la estructura de la hoja de formato de código"""
        # Título
        hoja["A1"] = "CODIGO 5 DIGITOS"

        # Formato
        hoja["A3"] = "Formato:"
        hoja["C3"] = "ABCDE"

        # Desglose
        hoja["A5"] = "Desglose:"

        hoja["A6"] = "Cuentas"
        hoja["C6"] = "AB"

        hoja["A7"] = "Subcuentas"
        hoja["C7"] = "ABC"

        hoja["A8"] = "Divisoria"
        hoja["C8"] = "ABCD"

        hoja["A9"] = "Subdivisoria"
        hoja["C9"] = "ABCDE"

        # Aplicar estilos
        # Estilo para el título
        self.__estilo_cabecera(
            [hoja["A1"]],
            "4472C4"  # Azul
        )

        # Agregar bordes a todas las celdas utilizadas
        for fila in range(1, 10):
            for col in range(1, 4):  # Columnas A, B, C
                celda = hoja.cell(row=fila, column=col)
                if celda.value is not None:
                    celda.border = Border(
                        top=Side(style="thin", color="000000"),
                        bottom=Side(style="thin", color="000000"),
                        left=Side(style="thin", color="000000"),
                        right=Side(style="thin", color="000000"),
                    )

        # Ajustar el ancho de las columnas
        self.__ajustar_ancho_columnas_hoja(hoja)