# Generated by Django 5.2.1 on 2025-05-15 11:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('estado_financiero', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='estadofinanciero',
            name='dt_fechaFinPeriodo',
            field=models.DateField(null=True),
        ),
        migrations.AddField(
            model_name='estadofinanciero',
            name='dt_fechaInicioPeriodo',
            field=models.DateField(null=True),
        ),
        migrations.AddField(
            model_name='estadofinanciero',
            name='str_tipoPeriodo',
            field=models.CharField(choices=[('1', 'Anual'), ('2', 'Trimestral'), ('3', 'Mensual')], default='3', max_length=20),
        ),
    ]
