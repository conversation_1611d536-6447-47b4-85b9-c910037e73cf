from django.db import models

from src.modules.sub_dominios.models import SubDominio


# Create your models here.
class Control(models.Model):
    class Meta:
        db_table = "tr_controles"
        managed = True
        verbose_name = "control"
        verbose_name_plural = "controles"

    int_idControl = models.AutoField(primary_key=True)
    str_descripcion = models.CharField(max_length=255, null=False, blank=False)
    str_valorIndustria = models.CharField(max_length=3)
    int_idSubDominio = models.ForeignKey(
        SubDominio,
        on_delete=models.CASCADE,
        db_column="int_idSubDominio",
        related_name="controles",
    )
