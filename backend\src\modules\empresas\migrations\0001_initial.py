# Generated by Django 5.2.1 on 2025-05-14 12:34

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Empresa',
            fields=[
                ('int_idEmpresa', models.AutoField(primary_key=True, serialize=False)),
                ('str_NombreEmpresa', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('str_RazonSocial', models.<PERSON><PERSON><PERSON><PERSON>(max_length=255)),
                ('str_Ruc', models.Char<PERSON>ield(max_length=25)),
                ('dt_FechaCreacion', models.DateField()),
                ('dt_FechaModificacion', models.DateTimeField(null=True)),
                ('int_idUsuarioCreacion', models.IntegerField()),
                ('int_idUsuarioModificacion', models.IntegerField(null=True)),
                ('str_idSuscripcion', models.Char<PERSON><PERSON>(max_length=10, null=True)),
                ('str_Pais', models.Char<PERSON><PERSON>(max_length=100)),
                ('str_Moneda', models.Char<PERSON><PERSON>(max_length=50)),
                ('str_SimboloMoneda', models.CharField(max_length=10)),
            ],
            options={
                'verbose_name': 'empresa',
                'verbose_name_plural': 'empresas',
                'db_table': 'tc_empresas',
                'managed': True,
            },
        ),
    ]
