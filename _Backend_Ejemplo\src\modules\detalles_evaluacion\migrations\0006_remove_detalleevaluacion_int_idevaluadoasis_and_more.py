# Generated by Django 5.1 on 2025-04-18 20:32

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('detalles_evaluacion', '0005_remove_detalleevaluacion_int_idevaluado_and_more'),
        ('evaluados', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='detalleevaluacion',
            name='int_idEvaluadoAsis',
        ),
        migrations.RemoveField(
            model_name='detalleevaluacion',
            name='int_idEvaluadoTobe',
        ),
        migrations.AddField(
            model_name='detalleevaluacion',
            name='int_idEvaluado',
            field=models.ForeignKey(db_column='int_idEvaluado', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='evaluado', to='evaluados.evaluado'),
        ),
    ]
