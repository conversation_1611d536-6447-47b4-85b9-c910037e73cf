import React, { useState, useEffect } from "react";
import { Doughn<PERSON> } from "react-chartjs-2";
import Card from "../../Components/Card/Card";
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from "chart.js";
import Select from "react-select";
import Button from "../../Components/Button/Button";
import IconoPlus from "../../assets/SVG/IconoPlus";
import IconoAjustes from "../../assets/SVG/IconoAjustes";
import FlagIcon from "../../assets/SVG/FlagIcon";
import ModalDetalleTarea from "./Modals/ModalDetalleTarea";
import FiltroModal from "./Modals/FiltroModal";
import ProyectosService from "../../Services/ProyectosService";
import CustomPaginator from "../../Components/Paginator/Paginator.jsx";

// Register ChartJS elements
ChartJS.register(ArcElement, Too<PERSON><PERSON>, Legend);

const InicioUsuario = () => {
  const [estiloDatos, setEstiloDatos] = useState("Tablero");
  const [openModal, setOpenModal] = useState(false);
  const [openFiltroModal, setOpenFiltroModal] = useState(false);
  const [tareaSeleccionada, setTareaSeleccionada] = useState(null);
  const [tareas, setTareas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filtrosAplicados, setFiltrosAplicados] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filtrosActuales, setFiltrosActuales] = useState({
    proyecto: "", // Cambiado de 'nombre' a 'proyecto' para coincidir con el backend
    fecha_inicio: "",
    fecha_fin: "",
    prioridad: null,
    estado: null
  });

  // Estadísticas de tareas
  const [estadisticas, setEstadisticas] = useState({
    nuevas: 0,
    enProceso: 0,
    terminadas: 0,
    total: 0
  });

  const handleOpenModal = (tarea) => {
    setOpenModal(true);
    setTareaSeleccionada(tarea);
  };

  const handleOpenFiltroModal = () => {
    setOpenFiltroModal(true);
  };

  const handleCloseFiltroModal = () => {
    setOpenFiltroModal(false);
    // No limpiamos los filtros al cerrar el modal para mantener los valores
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleSubmitFiltros = (tareasFiltradas, errorMessage = null, filtrosAplicadosData = {}) => {
    // Actualizar el estado de tareas con las tareas filtradas
    console.log("Index Usuario");
    console.log("Tareas filtradas:", tareasFiltradas);
    console.log("Filtros aplicados:", filtrosAplicadosData);
    setTareas(tareasFiltradas);

    // Guardar los filtros aplicados
    setFiltrosActuales(filtrosAplicadosData);

    // Calcular estadísticas con las tareas filtradas
    const nuevas = tareasFiltradas.filter(tarea => tarea.int_idEstado === 3).length;
    const enProceso = tareasFiltradas.filter(tarea => tarea.int_idEstado === 2).length;
    const terminadas = tareasFiltradas.filter(tarea => tarea.int_idEstado === 1).length;

    setEstadisticas({
      nuevas,
      enProceso,
      terminadas,
      total: tareasFiltradas.length
    });

    // Calcular el número total de páginas según el estilo de visualización
    const itemsPerPage = estiloDatos === "Tablero" ? 4 : 5;
    setTotalPages(Math.ceil(tareasFiltradas.length / itemsPerPage));

    // Resetear a la primera página cuando se aplican filtros
    setCurrentPage(1);

    // Si hay un mensaje de error, mostrarlo
    if (errorMessage) {
      setError(errorMessage);
    } else {
      setError(null);
    }

    // Indicar que se han aplicado filtros (incluso si no hay resultados)
    setFiltrosAplicados(true);

    handleCloseFiltroModal();
  };

  // Función para limpiar filtros y volver a cargar todas las tareas
  const limpiarFiltros = () => {
    setFiltrosAplicados(false);
    setFiltrosActuales({
      proyecto: "", // Cambiado de 'nombre' a 'proyecto' para coincidir con el backend
      fecha_inicio: "",
      fecha_fin: "",
      prioridad: null,
      estado: null
    });
    // Resetear a la primera página
    setCurrentPage(1);
    fetchTareas();
  };

  // Función para cargar tareas del usuario
  const fetchTareas = async () => {
    setLoading(true);
    setError(null);
    // Resetear el estado de filtros
    setFiltrosAplicados(false);
    // Resetear a la primera página
    setCurrentPage(1);

    try {
      const data = await ProyectosService.getTareasByUsuario();
      setTareas(data);

      // Calcular estadísticas
      const nuevas = data.filter(tarea => tarea.int_idEstado === 3).length;
      const enProceso = data.filter(tarea => tarea.int_idEstado === 2).length;
      const terminadas = data.filter(tarea => tarea.int_idEstado === 1).length;

      setEstadisticas({
        nuevas,
        enProceso,
        terminadas,
        total: data.length
      });

      // Calcular el número total de páginas según el estilo de visualización
      const itemsPerPage = estiloDatos === "Tablero" ? 4 : 5;
      setTotalPages(Math.ceil(data.length / itemsPerPage));
    } catch (error) {
      console.error("Error al cargar tareas:", error);
      if (error.isNotFound) {
        setTareas([]);
        setError("No se encontraron tareas para el usuario especificado");
        setTotalPages(0);
      } else {
        setError("Error al cargar las tareas. Por favor, inténtalo de nuevo.");
      }
    } finally {
      setLoading(false);
    }
  };

  // Cargar tareas al montar el componente
  useEffect(() => {
    fetchTareas();
  }, []);

  // Mapear tareas del API al formato que espera la UI
  const tareasData = tareas.map((tarea, index) => {
    // Determinar el estado basado en int_idEstado
    let estado;
    switch (tarea.int_idEstado) {
      case 1:
        estado = "terminado";
        break;
      case 2:
        estado = "proceso";
        break;
      case 3:
      default:
        estado = "nuevo";
    }

    // Determinar la dependencia
    const dependencia = tarea.int_idTareaPadre
      ? tarea.int_idTareaPadre.nombre
      : null;

    // Determinar el nombre de la prioridad
    let prioridadNombre;
    let prioridadColor;
    switch (tarea.int_idPrioridad) {
      case 1:
        prioridadNombre = "Alta";
        prioridadColor = "#FF4D4F"; // Rojo para alta prioridad
        break;
      case 2:
        prioridadNombre = "Media";
        prioridadColor = "#FAAD14"; // Amarillo para media prioridad
        break;
      case 3:
        prioridadNombre = "Baja";
        prioridadColor = "#52C41A"; // Verde para baja prioridad
        break;
      default:
        prioridadNombre = "Media";
        prioridadColor = "#FAAD14";
    }

    return {
      id: tarea.int_idTarea,
      titulo: `Tarea ${index + 1}`,
      nombre: tarea.str_nombre,
      descripcion: tarea.str_descripcion,
      dependencia,
      horasTrabajadas: tarea.str_horasTrabajadas,
      fechaInicio: tarea.dt_fechaInicio,
      fechaFin: tarea.dt_fechaFin,
      fechaInicioReal: tarea.dt_fechaInicioReal,
      fechaFinReal: tarea.dt_fechaFinReal,
      estado,
      presupuesto: tarea.str_presupuesto,
      gasto: tarea.str_gasto,
      avance: tarea.db_avance,
      prioridad: tarea.int_idPrioridad,
      prioridadNombre,
      prioridadColor,
      proyecto: tarea.int_idProyecto,
      usuario: tarea.int_idUsuarios ? `${tarea.int_idUsuarios.nombres} ${tarea.int_idUsuarios.apellidos}` : ""
    };
  });

  // Determinar el número de elementos por página según el estilo de visualización
  const itemsPerPage = estiloDatos === "Tablero" ? 4 : 5;

  // Paginar tareas según el estilo de visualización
  const paginatedTasks = tareasData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const columns = [
    { key: "numero", header: "#", width: "w-[3%]" },
    { key: "nombre", header: "Nombre", width: "w-[15%]" },
    { key: "origen", header: "Origen", width: "w-[10%]" },
    { key: "descripcion", header: "Descripción", width: "w-[20%]" },
    { key: "fechaInicio", header: "Fecha de inicio", width: "w-[8%]" },
    { key: "fechaFin", header: "Fecha de fin", width: "w-[8%]" },
    { key: "presupuesto", header: "Presupuesto", width: "w-[8%]" },
    { key: "horasTrabajadas", header: "Horas", width: "w-[5%]" },
    { key: "prioridad", header: "Prioridad", width: "w-[7%]" },
    { key: "estado", header: "Estado", width: "w-[10%]" },
    { key: "dependencia", header: "Dependencia", width: "w-[10%]" },
    { key: "acciones", header: "Acciones", width: "w-[5%]" },
  ];

  // Función para formatear fechas en formato DD/MM/YYYY sin usar Date
  const formatearFecha = (fechaString) => {
    if (!fechaString) return "-";

    try {
      // Verificar si la fecha tiene el formato esperado (YYYY-MM-DD)
      if (!/^\d{4}-\d{2}-\d{2}$/.test(fechaString)) return "-";

      // Dividir la fecha en sus componentes
      const [anio, mes, dia] = fechaString.split('-');

      // Retornar en formato DD/MM/YYYY
      return `${dia}/${mes}/${anio}`;
    } catch (error) {
      console.error("Error al formatear fecha:", error);
      return "-";
    }
  };

  // Función para formatear valores como moneda en soles
  const formatearMoneda = (valor) => {
    if (!valor) return "S/ 0.00";

    try {
      // Convertir a número si es string
      const numero = typeof valor === 'string' ? parseFloat(valor) : valor;

      // Verificar si es un número válido
      if (isNaN(numero)) return "S/ 0.00";

      // Formatear con 2 decimales y separador de miles
      return `S/ ${numero.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
    } catch (error) {
      console.error("Error al formatear moneda:", error);
      return "S/ 0.00";
    }
  };

  // Función para verificar si una tarea está atrasada
  const isAtrasada = (fechaFin, estado) => {
    if (!fechaFin) return false;

    const hoy = new Date();
    const fechaFinDate = new Date(fechaFin);

    // Verificar si la fecha de fin es anterior a hoy y el estado no es "terminado" (1)
    return fechaFinDate < hoy && (estado !== "terminado" && estado !== 1);
  };

  const getEstadoConfig = (estado) => {
    switch (estado) {
      case "nuevo":
        return { texto: "Nuevo", color: "#1890FF" };
      case "proceso":
        return { texto: "En Proceso", color: "#FFA20E" };
      case "terminado":
        return { texto: "Terminado", color: "#47D691" };
      default:
        return { texto: "Nuevo", color: "#1890FF" };
    }
  };

  const renderTabla = () => {
    return (
      <div className="border-1 border-[#D0D5DD] rounded-[0.75rem] poppins-font overflow-x-auto w-full">
        <table className="w-full">
          <thead>
            <tr>
              {columns.map((col) => (
                <th
                  key={col.key}
                  className={`px-2 md:px-2 py-5 poppins-font-600 text-[#272727] cursor-pointer bg-[#FBFCFF] text-sm md:text-[0.875rem] border-r-1 border-[#f9f0f0] text-left ${col.width}`}
                >
                  {col.header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {paginatedTasks.map((tarea, index) => (
              <tr
                key={tarea.id}
                className={`border-y-1 border-[#D0D5DD] ${
                  tareaSeleccionada === tarea ? "bg-gray-200" : ""
                }`}
                style={{
                  position: 'relative',
                  borderLeft: isAtrasada(tarea.fechaFin, tarea.int_idEstado) ? '4px solid #FF4D4F' : 'none'
                }}
                accion={() => handleOpenModal(tarea)}
              >
                <td className="py-5 px-4 md:px-2 text-sm md:text-center border-r-1 border-[#f9f0f0] text-gray-600" style={{ paddingLeft: isAtrasada(tarea.fechaFin, tarea.int_idEstado) ? '8px' : '16px' }}>
                  {(currentPage - 1) * itemsPerPage + index + 1}
                </td>
                <td className="py-5 px-4 md:px-2 text-sm border-r-1 border-[#f9f0f0]">
                  {tarea.nombre}
                </td>
                <td className="py-5 px-4 md:px-2 text-sm border-r-1 border-[#f9f0f0]">
                  {tarea.proyecto?.nombre || "-"}
                </td>
                <td className="py-5 px-4 md:px-2 text-sm border-r-1 border-[#f9f0f0] ">
                  {tarea.descripcion}
                </td>
                <td className="py-5 px-4 md:px-2 text-sm border-r-1 border-[#f9f0f0]">
                  {formatearFecha(tarea.fechaInicio)}
                </td>
                <td className="py-5 px-4 md:px-2 text-sm border-r-1 border-[#f9f0f0]">
                  <span className={isAtrasada(tarea.fechaFin, tarea.int_idEstado) ? "text-[#FF4D4F] font-medium" : ""}>
                    {formatearFecha(tarea.fechaFin)}
                  </span>
                </td>
                <td className="py-5 px-4 md:px-2 text-sm border-r-1 border-[#f9f0f0]">
                  {formatearMoneda(tarea.presupuesto)}
                </td>
                <td className="py-5 px-4 md:px-2 text-sm border-r-1 border-[#f9f0f0]">
                  {tarea.horasTrabajadas}
                </td>
                <td className="py-5 px-4 md:px-2 text-sm border-r-1 border-[#f9f0f0]">
                  <div className="flex items-center gap-1">
                    <FlagIcon color={tarea.prioridadColor} size="16" />
                    <span style={{ color: tarea.prioridadColor }}>
                      {tarea.prioridadNombre}
                    </span>
                  </div>
                </td>
                <td className="py-5 px-4 md:px-2 text-sm border-r-1 border-[#f9f0f0]">
                  <div className="flex gap-2">
                    <span
                      className="py-2 px-4 rounded-md text-white text-sm"
                      style={{
                        backgroundColor:
                          tarea.estado === "nuevo"
                            ? "#1890FF"
                            : tarea.estado === "proceso"
                            ? "#FFA20E"
                            : "#47D691",
                      }}
                    >
                      {tarea.estado === "nuevo"
                        ? "Nuevo"
                        : tarea.estado === "proceso"
                        ? "En Proceso"
                        : "Terminado"}
                    </span>
                    {/* {isAtrasada(tarea.fechaFin, tarea.estado) && (
                      <span className="py-2 px-4 rounded-md text-white text-sm bg-[#FF4D4F]">
                        Atrasada
                      </span>
                    )} */}
                  </div>
                </td>
                <td className="py-5 px-4 md:px-2 text-sm border-r-1 border-[#f9f0f0]">
                  {tarea.dependencia || ""}
                </td>
                <td className="py-5 px-4 md:px-2 text-sm border-r-1 border-[#f9f0f0]">
                  <Button
                    text_button=""
                    icon={<IconoPlus size="1.2rem" color="white" />}
                    accion={() => handleOpenModal(tarea)}
                    className="bg-[#272727] text-white p-2 rounded-md h-full cursor-pointer"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const renderTareasCard = () => {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 w-full">
        {paginatedTasks.map((tarea) => (
          <div
            key={tarea.id}
            className={`flex flex-col items-start rounded-md justify-center border-1 border-[#D0D5DD] relative overflow-hidden h-full ${
              isAtrasada(tarea.fechaFin, tarea.int_idEstado) ? "p-0" : "p-[0.0625rem]"
            }`}
          >
            {isAtrasada(tarea.fechaFin, tarea.int_idEstado) && (
              <div className="w-full h-1 bg-[#FF4D4F] absolute top-0 left-0"></div>
            )}
            <div className="flex justify-between w-full items-center py-3 px-4 sm:py-4 sm:px-6 lg:px-8">
              <div className="flex flex-wrap gap-2">
                <span
                  className="py-1 px-3 sm:py-2 sm:px-4 lg:px-6 text-xs sm:text-sm min-w-[4rem] sm:min-w-[5rem] rounded-md text-white"
                  style={{ backgroundColor: getEstadoConfig(tarea.estado).color }}
                >
                  {getEstadoConfig(tarea.estado).texto}
                </span>
                <div className="flex items-center gap-1">
                  <FlagIcon color={tarea.prioridadColor} size="16" />
                  <span className="text-xs sm:text-sm" style={{ color: tarea.prioridadColor }}>
                    {tarea.prioridadNombre}
                  </span>
                </div>
              </div>
              <div className="flex items-center">
                <Button
                  icon={<IconoPlus size="1.2rem" color="white" />}
                  text_button=" "
                  className="bg-[#272727] text-white p-1 sm:p-2 rounded-md h-full cursor-pointer"
                  accion={() => handleOpenModal(tarea)}
                />
              </div>
            </div>
            <div className="flex flex-col w-full items-start justify-start gap-1 py-3 px-4 sm:py-4 sm:px-6 lg:px-8 border-b-1 border-[#D0D5DD]">
              <span className="text-xs sm:text-sm text-[#272727] truncate w-full">{tarea.proyecto.nombre}</span>
              <span className="text-base sm:text-lg text-[#272727] font-semibold truncate w-full">
                {tarea.nombre}
              </span>
              <span className="text-xs sm:text-sm text-[#272727] font-bold mt-2">
                Descripción
              </span>
              <span className="text-sm sm:text-md text-[#272727] font-medium w-full line-clamp-3">
                {tarea.descripcion}
              </span>
              <span className="text-xs sm:text-sm text-[#909090] font-medium mt-4 sm:mt-8 lg:mt-14">
                Dependencia
              </span>
              <span className="text-sm sm:text-md text-[#272727] font-medium truncate w-full">
                {tarea.dependencia || "Sin dependencia"}
              </span>
              <span className="text-xs sm:text-sm text-[#909090] font-medium mt-2">
                Presupuesto
              </span>
              <span className="text-sm sm:text-md text-[#272727] font-medium">
                {formatearMoneda(tarea.presupuesto)}
              </span>
              <span className="text-xs sm:text-sm text-[#909090] font-medium mt-2">
                Horas Trabajadas
              </span>
              <span className="text-sm sm:text-md text-[#272727] font-medium">
                {tarea.horasTrabajadas}
              </span>
            </div>
            <div className="flex flex-row items-start justify-between gap-1 py-3 px-4 sm:py-4 sm:px-6 lg:px-8 w-full">
              <div className="flex flex-col gap-1 justify-start items-start">
                <label className="text-xs text-[#909090]">Fecha de Inicio</label>
                <span className="text-xs sm:text-sm text-[#272727] font-medium">
                  {formatearFecha(tarea.fechaInicio)}
                </span>
              </div>
              <div className="flex flex-col gap-1 justify-start items-start">
                <label className="text-xs text-[#909090]">Fecha de Fin</label>
                <span
                  className={`text-xs sm:text-sm font-medium ${
                    isAtrasada(tarea.fechaFin, tarea.int_idEstado)
                      ? "text-[#FF4D4F]"
                      : "text-[#272727]"
                  }`}
                >
                  {formatearFecha(tarea.fechaFin)}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Esta función se usa directamente en el select
  // const onChange = (e) => {
  //   setEstiloDatos(e.target.value);
  // };
  const data = {
    labels: ["Terminado", "En Proceso", "Nuevas"],
    datasets: [
      {
        data: [estadisticas.terminadas, estadisticas.enProceso, estadisticas.nuevas],
        backgroundColor: ["#A7FF90", "#FFCE80", "#1890FF"],
        hoverBackgroundColor: ["#A7FF90", "#FFCE80", "#1890FF"],
      },
    ],
  };

  const options = {
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: "#FFFFFF",
        borderColor: "#D0D5DD",
        borderWidth: 1,
        cornerRadius: 8,
        titleColor: "#101828",
        bodyColor: "#475467",
        padding: 12,
        boxWidth: 0,
        boxHeight: 0,
        titleFont: {
          weight: "bold",
        },
        callbacks: {
          label: function (context) {
            return ` ${context.label}: ${context.formattedValue}`;
          },
        },
      },
    },
  };

  return (
    <div className="p-3 sm:p-4 md:p-5 w-full max-w-full poppins-font">
      <div className="flex flex-col md:flex-row gap-3 sm:gap-4 md:gap-7">
        <div className="w-full md:w-[36.9375rem] md:h-full">
          <Card bg_color={"#F8FAFB"} className="h-full">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="w-full md:w-auto">
                <div className="text-black font-bold poppins-font-600 pb-3 md:pb-5 text-base sm:text-lg md:text-[1.25rem]">
                  Avance de Proyectos
                </div>
                {/* Lista de estados */}
                <div className="flex flex-col gap-2 w-max">
                  <div className="flex items-center gap-2">
                    <div className="bg-[#A7FF90] w-[0.6875rem] h-[0.6875rem]" />
                    <span className="text-gray-900 text-xs sm:text-sm">Terminado</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="bg-[#FFCE80] w-[0.6875rem] h-[0.6875rem]" />
                    <span className="text-gray-900 text-xs sm:text-sm">En Proceso</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="bg-[#1890FF] w-[0.6875rem] h-[0.6875rem]" />
                    <span className="text-gray-900 text-xs sm:text-sm">Nuevos</span>
                  </div>
                </div>
              </div>
              <div className="w-[6rem] sm:w-[7rem] md:w-[8rem] h-[6rem] sm:h-[7rem] md:h-[8rem] mt-4 md:mt-0 flex justify-center items-center">
                <Doughnut data={data} options={options} />
              </div>
            </div>
          </Card>
        </div>

        <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
          <Card
            shadow={""}
            bg_color={"#FFFFFF"}
            className="shadow-md w-full h-full"
            padding="py-3 px-4 sm:px-5"
          >
            <div className="h-full flex flex-col justify-center items-start gap-2 sm:gap-3">
              <span className="text-gray-900 text-sm sm:text-md mt-4 sm:mt-6 md:mt-8 w-full">
                Total Tareas en Proceso
              </span>
              <span className="text-gray-900 text-xl sm:text-2xl font-bold">{estadisticas.enProceso}</span>
            </div>
          </Card>
          <Card
            shadow={""}
            bg_color={"#FFFFFF"}
            className="shadow-md w-full h-full"
            padding="py-3 px-4 sm:px-5"
          >
            <div className="h-full flex flex-col justify-center items-start gap-2 sm:gap-3">
              <span className="text-gray-900 text-sm sm:text-md mt-4 sm:mt-6 md:mt-8 w-full">
                Total Tareas Nuevas
              </span>
              <span className="text-gray-900 text-xl sm:text-2xl font-bold">{estadisticas.nuevas}</span>
            </div>
          </Card>
          <Card
            shadow={""}
            bg_color={"#FFFFFF"}
            className="shadow-md w-full h-full"
            padding="py-3 px-4 sm:px-5"
          >
            <div className="h-full flex flex-col justify-center items-start gap-2 sm:gap-3">
              <span className="text-gray-900 text-sm sm:text-md mt-4 sm:mt-6 md:mt-8 w-full">
                Total Tareas Terminadas
              </span>
              <span className="text-gray-900 text-xl sm:text-2xl font-bold">{estadisticas.terminadas}</span>
            </div>
          </Card>
          <Card
            shadow={""}
            bg_color={"#FFFFFF"}
            className="shadow-md w-full h-full"
            padding="py-3 px-4 sm:px-5"
          >
            <div className="h-full flex flex-col justify-center items-start gap-2 sm:gap-3">
              <span className="text-gray-900 text-sm sm:text-md mt-4 sm:mt-6 md:mt-8 w-full">
                Total Tareas Trabajadas
              </span>
              <span className="text-gray-900 text-xl sm:text-2xl font-bold">{estadisticas.total}</span>
            </div>
          </Card>
        </div>
      </div>
      <div className="flex flex-wrap justify-between items-center gap-2 w-full mt-5">
        {filtrosAplicados && (
          <div className="flex items-center">
            <span className="text-xs sm:text-sm text-gray-600 mr-2">Filtros aplicados</span>
            <Button
              text_button="Limpiar"
              className="text-blue-600 hover:text-blue-800 underline cursor-pointer text-xs sm:text-sm"
              accion={limpiarFiltros}
            />
          </div>
        )}
        <div className="flex-grow"></div>
        <div className="flex items-center gap-2 sm:gap-3">
          <div className="w-[5rem] sm:w-[5.6875rem] cursor-pointer">
            <Button
              text_button="Filtrar"
              className="w-full h-[2rem] sm:h-[2.3rem] px-2 border-[#D0D5DD] poppins-font cursor-pointer border-1 rounded-md text-xs sm:text-sm"
              icon={<IconoAjustes size="1rem" color="#000000" />}
              accion={handleOpenFiltroModal}
            />
          </div>
          <div className="relative w-[9rem]  md:w-[9.75rem]">
            <div className="flex items-center">
              <div className="absolute left-2 z-10 pointer-events-none">
                {estiloDatos === "Tablero" ? (
                  <svg
                    width="1rem"
                    height="1rem"
                    viewBox="0 0 25 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.3333 3H3V10.3333H10.3333V3Z"
                      stroke="#7BACFF"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M21.9997 3H14.6663V10.3333H21.9997V3Z"
                      stroke="#7BACFF"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M21.9997 14.6667H14.6663V22H21.9997V14.6667Z"
                      stroke="#7BACFF"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M10.3333 14.6667H3V22H10.3333V14.6667Z"
                      stroke="#7BACFF"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                ) : (
                  <svg
                    width="1rem"
                    height="1rem"
                    viewBox="0 0 25 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M8 6H21"
                      stroke="#7BACFF"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M8 12H21"
                      stroke="#7BACFF"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M8 18H21"
                      stroke="#7BACFF"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M3 6H3.01"
                      stroke="#7BACFF"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M3 12H3.01"
                      stroke="#7BACFF"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M3 18H3.01"
                      stroke="#7BACFF"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                )}
              </div>
              <Select
                value={{ value: estiloDatos, label: estiloDatos }}
                options={[
                  { value: 'Tablero', label: 'Tablero' },
                  { value: 'Tabla', label: 'Tabla' }
                ]}
                onChange={(option) => {
                  const newEstilo = option.value;
                  setEstiloDatos(newEstilo);
                  // Recalcular el número total de páginas según el nuevo estilo
                  const newItemsPerPage = newEstilo === "Tablero" ? 4 : 5;
                  setTotalPages(Math.ceil(tareas.length / newItemsPerPage));
                  // Resetear a la primera página
                  setCurrentPage(1);
                }}
                className="w-full"
                classNamePrefix="react-select"
                styles={{
                  control: (baseStyles) => ({
                    ...baseStyles,
                    borderColor: '#7BACFF',
                    borderRadius: '0.5rem',
                    minHeight: '2rem',
                    height: '2rem',
                    boxShadow: 'none',
                    paddingLeft: '1.5rem',
                    width: '100%',
                  }),
                  valueContainer: (baseStyles) => ({
                    ...baseStyles,
                    padding: '0 8px 0 1px',
                    height: '2rem',
                  }),
                  input: (baseStyles) => ({
                    ...baseStyles,
                    margin: '0',
                    padding: '0',
                  }),
                  indicatorsContainer: (baseStyles) => ({
                    ...baseStyles,
                    height: '2.2rem',
                    padding: '0 1px 0 1px',
                  }),
                  dropdownIndicator: (baseStyles) => ({
                    ...baseStyles,
                    color: '#7BACFF',
                    padding: '0 4px 0 2px',
                    width: '2rem',
                  }),
                  placeholder: (baseStyles) => ({
                    ...baseStyles,
                    color: '#91A8E2',
                  }),
                  option: (baseStyles, { isFocused }) => ({
                    ...baseStyles,
                    backgroundColor: isFocused ? '#F8FAFB' : 'transparent',
                    color: '#91A8E2',
                  }),
                  menu: (baseStyles) => ({
                    ...baseStyles,
                    zIndex: 9999,
                  }),
                }}
              />
            </div>
          </div>
        </div>
      </div>
      <div className="w-full mt-5">
        {loading ? (
          <div className="w-full text-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#1890FF] mx-auto"></div>
            <p className="mt-4 text-gray-600">Cargando tareas...</p>
          </div>
        ) : error ? (
          <div className="w-full text-center py-10">
            <div className={`${filtrosAplicados ? "bg-yellow-100 border-yellow-400 text-yellow-700" : "bg-red-100 border-red-400 text-red-700"} border px-4 py-3 rounded relative`} role="alert">
              {filtrosAplicados ? (
                <>
                  <strong className="font-bold">Filtros aplicados: </strong>
                  <span className="block sm:inline">{error}</span>
                </>
              ) : (
                <>
                  <strong className="font-bold">Error: </strong>
                  <span className="block sm:inline">{error}</span>
                </>
              )}
            </div>
          </div>
        ) : tareasData.length === 0 ? (
          <div className="w-full text-center py-10">
            <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded relative" role="alert">
              <span className="block sm:inline">No se encontraron tareas asignadas para este usuario.</span>
            </div>
          </div>
        ) : (
          <div className="flex flex-col">
            {estiloDatos === "Tablero" ? renderTareasCard() : renderTabla()}
            {/* Paginador */}
            {tareasData.length > 0 && (
              <div className="flex justify-end mt-4">
                <CustomPaginator
                  totalPages={totalPages}
                  currentPage={currentPage}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </div>
        )}
      </div>
      <ModalDetalleTarea
        setOpenModal={setOpenModal}
        openModal={openModal}
        getEstadoConfig={getEstadoConfig}
        tareaSeleccionada={tareaSeleccionada}
        onTaskUpdated={fetchTareas}
      />
      <FiltroModal
        open={openFiltroModal}
        handleClose={handleCloseFiltroModal}
        handleSubmit={handleSubmitFiltros}
        clearFiltersExternal={filtrosAplicados}
        initialFilters={filtrosActuales}
      />
    </div>
  );
};

export default InicioUsuario;
2;
