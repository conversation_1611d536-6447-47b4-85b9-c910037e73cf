# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('suscripciones', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Framework',
            fields=[
                ('int_idFramework', models.AutoField(primary_key=True, serialize=False)),
                ('str_nombre', models.CharField(max_length=255)),
                ('str_descripcion', models.CharField(max_length=255)),
                ('str_idSuscripcion', models.ForeignKey(db_column='str_idSuscripcion', on_delete=django.db.models.deletion.CASCADE, related_name='frameworks', to='suscripciones.suscripcion')),
            ],
            options={
                'verbose_name': 'framework',
                'verbose_name_plural': 'frameworks',
                'db_table': 'tm_frameworks',
                'managed': True,
            },
        ),
    ]
