import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import IconoFlechaSimular from "../../../assets/SVG/IconoFlechaSimular";
import IconoSimulaciones from "../../../assets/SVG/IconoSimulaciones";
import IconoRegresar from "../../../assets/SVG/IconoRegresar";
import IconoInformacion from "../../../assets/SVG/IconoInformacion";
import IconoReestablecer from "../../../assets/SVG/IconoReestablecer";
import { RoutesPrivate } from "../../../Routes/ProtectedRoute";
import { formatearFechaDDMMYYYY } from "../../../utils/dateUtils";
import FinancieraService from "../../../Services/FinancieraService";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "../../../Services/TokenService";

const Simulaciones = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Recibir parámetros desde Inicio.jsx
  const parametrosInicio = location.state || {};
  const {
    tipoPeriodo: tipoPeriodoInicio,
    fechaFinPeriodo: fechaFinPeriodoInicio,
    idEstadoFinanciero: idEstadoFinancieroInicio
  } = parametrosInicio;

  // Estados para almacenar los parámetros recibidos
  const [tipoPeriodoActual, setTipoPeriodoActual] = useState(tipoPeriodoInicio || null);
  const [fechaFinActual, setFechaFinActual] = useState(fechaFinPeriodoInicio || null);
  const [idEstadoFinancieroActual, setIdEstadoFinancieroActual] = useState(idEstadoFinancieroInicio || null);

  // Estados para el selector de empresas
  const [empresas, setEmpresas] = useState([]);
  const [empresaSeleccionada, setEmpresaSeleccionada] = useState(null);
  const [cargandoEmpresas, setCargandoEmpresas] = useState(false);
  const [errorEmpresas, setErrorEmpresas] = useState(null);

  // Estados para el selector de periodos
  const [periodos, setPeriodos] = useState([]);
  const [periodoSeleccionado, setPeriodoSeleccionado] = useState(null);
  const [cargandoPeriodos, setCargandoPeriodos] = useState(false);
  const [errorPeriodos, setErrorPeriodos] = useState(null);

  // Estados para la moneda
  const [moneda, setMoneda] = useState("USD");
  const [simboloMoneda, setSimboloMoneda] = useState("$");

  // Estados para los ratios financieros
  const [ratiosFinancieros, setRatiosFinancieros] = useState([]);
  const [cargandoRatios, setCargandoRatios] = useState(false);
  const [errorRatios, setErrorRatios] = useState(null);

  // Estados para los datos iniciales del simulador
  const [datosIniciales, setDatosIniciales] = useState(null);
  const [cargandoDatosIniciales, setCargandoDatosIniciales] = useState(false);
  const [errorDatosIniciales, setErrorDatosIniciales] = useState(null);

  // Estados para guardar simulación
  const [guardandoSimulacion, setGuardandoSimulacion] = useState(false);
  const [errorGuardarSimulacion, setErrorGuardarSimulacion] = useState(null);
  const [simulacionGuardada, setSimulacionGuardada] = useState(false);

  const [datosMensuales, setDatosMensuales] = useState([
    // {
    //   nombre: "Abril",
    //   fecha: "31/04/2025",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     Patrimonio: {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Marzo",
    //   fecha: "31/03/2025",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     Patrimonio: {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Febrero",
    //   fecha: "31/02/2025",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     Patrimonio: {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,245,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$645,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$600,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Enero",
    //   fecha: "31/01/2025",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,545,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     Patrimonio: {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,985,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Diciembre",
    //   fecha: "31/12/2024",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,545,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     Patrimonio: {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,985,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Noviembre",
    //   fecha: "31/11/2024",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,545,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     Patrimonio: {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,985,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Octubre",
    //   fecha: "31/10/2024",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,545,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     Patrimonio: {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,985,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
    // {
    //   nombre: "Septiembre",
    //   fecha: "31/09/2024",
    //   indicadores: {
    //     "Activos totales": {
    //       valor: "$1,545,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Pasivos totales": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     Patrimonio: {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Liquidez": {
    //       valor: "$1,985,600",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //     "Ratio de Endeudamiento": {
    //       valor: "$685,300",
    //       porcentaje: "+ 2.1%",
    //       tendencia: "positiva",
    //       colorGrafico: "#FF6B8A",
    //     },
    //     "Ratio 3": {
    //       valor: "$680,30",
    //       porcentaje: "+ 12.8%",
    //       tendencia: "positiva",
    //       colorGrafico: "#21DDB8",
    //     },
    //   },
    // },
  ]);

  // Valores iniciales para la simulación DuPont
  const [utilidadNeta, setUtilidadNeta] = useState(32000000);
  const [utilidadNetaSlider, setUtilidadNetaSlider] = useState(50);
  const [ventas, setVentas] = useState(230342200);
  const [ventasSlider, setVentasSlider] = useState(50);
  const [activos, setActivos] = useState(50000000);
  const [activosSlider, setActivosSlider] = useState(50);
  const [patrimonio, setPatrimonio] = useState(23400000);
  const [patrimonioSlider, setPatrimonioSlider] = useState(50);

  // Valores originales de los datos del endpoint
  const [valoresOriginales, setValoresOriginales] = useState({
    // utilidadNeta: 32000000,
    // ventas: 230342200,
    // activos: 50000000,
    // patrimonio: 23400000,
    // roe: 31.8
  });

  // Calcular ROE
  const margenNeto = utilidadNeta / ventas;
  const rotacionActivos = ventas / activos;
  const multiplicadorCapital = activos / patrimonio;
  const roeSimulado = margenNeto * rotacionActivos * multiplicadorCapital * 100;

  // Obtener ROE real del endpoint de ratios financieros
  const roeReal = ratiosFinancieros.length > 0
    ? ratiosFinancieros.find(ratio => ratio.nombre_ratio.toLowerCase() === "roe")?.valor_ratio || valoresOriginales.roe
    : valoresOriginales.roe;

  // Añadir historial de cambios para el gráfico
  const [historialCambios, setHistorialCambios] = useState([
    {
      nombre: "Valores iniciales",
      utilidadNeta: 1,
      ventas: 1,
      activos: 1,
      patrimonio: 1,
      roe: 1
    }
  ]);

  // Función para extraer valores específicos de los datos iniciales
  const extraerValoresDuPont = (datosIniciales, ratiosFinancieros) => {
    if (!datosIniciales || !datosIniciales.agrupadores) {
      return null;
    }
    console.log("===============Extraer valores DuPont===================");
    console.log("datosIniciales", datosIniciales);
    console.log("ratiosFinancieros", ratiosFinancieros);
    const agrupadores = datosIniciales.agrupadores;

    // Buscar los agrupadores específicos
    const utilidadNetaData = agrupadores.find(ag => ag.nombre.toLowerCase() === "er-un");
    const ventasData = agrupadores.find(ag => ag.nombre.toLowerCase() === "ventas netas");
    const activosData = agrupadores.find(ag => ag.nombre.toLowerCase() === "activos totales");
    const patrimonioData = agrupadores.find(ag => ag.nombre.toLowerCase() === "patrimonio");
    console.log("patrimonioData", patrimonioData);
    console.log("activosData", activosData);
    console.log("ventasData", ventasData);
    console.log("utilidadNetaData", utilidadNetaData);

    // Buscar ROE en los ratios financieros
    const roeData = ratiosFinancieros.find(ratio => ratio.nombre_ratio.toLowerCase() === "roe");
    console.log("roeData", roeData);

    return {
      utilidadNeta: utilidadNetaData ? utilidadNetaData.valor : null,
      ventas: ventasData ? Math.abs(ventasData.valor) : null,
      activos: activosData ? Math.abs(activosData.valor) : null,
      patrimonio: patrimonioData ? Math.abs(patrimonioData.valor) : null,
      roe: roeData ? roeData.valor_ratio : null
    };
  };

  // Función para actualizar los valores del simulador con datos reales
  const actualizarValoresConDatosReales = () => {
    const valoresExtraidos = extraerValoresDuPont(datosIniciales, ratiosFinancieros);
    console.log("===============Actualizar valores con datos reales===================");
    // console.log("valoresExtraidos", valoresExtraidos);
    if (valoresExtraidos) {
      // Actualizar valores originales
      setValoresOriginales(valoresExtraidos);

      // Actualizar valores del simulador solo si los datos son válidos
      if (valoresExtraidos.utilidadNeta) {
        setUtilidadNeta(valoresExtraidos.utilidadNeta);
        setUtilidadNetaSlider(50); // Reset slider to middle
      }
      if (valoresExtraidos.ventas) {
        setVentas(valoresExtraidos.ventas);
        setVentasSlider(50);
      }
      if (valoresExtraidos.activos) {
        setActivos(valoresExtraidos.activos);
        setActivosSlider(50);
      }
      if (valoresExtraidos.patrimonio) {
        setPatrimonio(valoresExtraidos.patrimonio);
        setPatrimonioSlider(50);
      }

      console.log("Valores DuPont actualizados con datos reales:", valoresExtraidos);
    }
  };

  // Función para obtener la moneda desde las cookies
  const obtenerMonedaDesdeCookies = () => {
    try {
      const monedaCookie = Cookies.get("monedaFinanciera");
      const simboloCookie = Cookies.get("simbMonedaFinanciera");

      if (monedaCookie && simboloCookie) {
        const monedaDecrypted = decrypt(monedaCookie);
        let simboloDecrypted = decrypt(simboloCookie);

        if (monedaDecrypted && simboloDecrypted) {
          // Regla especial: si el símbolo es "S/." quitar el punto final
          if (simboloDecrypted === "S/.") {
            simboloDecrypted = "S/";
          }

          setMoneda(monedaDecrypted);
          setSimboloMoneda(simboloDecrypted);
          return { moneda: monedaDecrypted, simbolo: simboloDecrypted };
        }
      }
    } catch (error) {
      console.error("Error al obtener moneda desde cookies:", error);
    }

    // Valores por defecto si no se pueden obtener desde cookies
    return { moneda: "USD", simbolo: "$" };
  };

  // Función para formatear el nombre del periodo según el tipo
  const formatearNombrePeriodo = (periodo, tipoPeriodoParam) => {
    if (!periodo || !periodo.periodo_contable) return "Periodo desconocido";

    const periodoContable = periodo.periodo_contable;

    if (tipoPeriodoParam === 1) {
      // Para tipo anual, mostrar solo el año
      // Extraer el año del string "February 2025" -> "2025"
      const partes = periodoContable.split(' ');
      if (partes.length >= 2) {
        return partes[partes.length - 1]; // Último elemento es el año
      }
      return periodoContable;
    } else if (tipoPeriodoParam === 3) {
      // Para tipo mensual, mostrar mes y año: "Enero - 2025"
      const partes = periodoContable.split(' ');
      if (partes.length >= 2) {
        const mesIngles = partes[0];
        const año = partes[partes.length - 1];

        // Convertir mes de inglés a español
        const mesesTraduccion = {
          'January': 'Enero',
          'February': 'Febrero',
          'March': 'Marzo',
          'April': 'Abril',
          'May': 'Mayo',
          'June': 'Junio',
          'July': 'Julio',
          'August': 'Agosto',
          'September': 'Septiembre',
          'October': 'Octubre',
          'November': 'Noviembre',
          'December': 'Diciembre'
        };

        const mesEspañol = mesesTraduccion[mesIngles] || mesIngles;
        return `${mesEspañol} - ${año}`;
      }
      return periodoContable;
    }

    // Para otros tipos, devolver tal como viene
    return periodoContable;
  };

  // Función para cargar las empresas
  const cargarEmpresas = async () => {
    try {
      setCargandoEmpresas(true);
      setErrorEmpresas(null);
      const data = await FinancieraService.getEmpresas();
      setEmpresas(data);

      // Obtener el ID de empresa desde las cookies
      const empresaIdFromCookie = FinancieraService.getCompanyId();

      if (data && data.length > 0) {
        if (empresaIdFromCookie) {
          // Buscar la empresa que coincida con el ID de la cookie
          const empresaEncontrada = data.find(emp => emp.int_idEmpresa === empresaIdFromCookie);
          if (empresaEncontrada) {
            setEmpresaSeleccionada(empresaEncontrada);
          } else {
            // Si no se encuentra la empresa de la cookie, seleccionar la primera
            setEmpresaSeleccionada(data[0]);
          }
        } else {
          // Si no hay ID en cookie, seleccionar la primera empresa
          setEmpresaSeleccionada(data[0]);
        }
      }
    } catch (error) {
      console.error("Error al cargar empresas:", error);
      setErrorEmpresas(error.message || "Error al cargar las empresas");
    } finally {
      setCargandoEmpresas(false);
    }
  };

  // Función para cargar los periodos
  const cargarPeriodos = async (tipoPeriodoParam) => {
    try {
      setCargandoPeriodos(true);
      setErrorPeriodos(null);
      const data = await FinancieraService.getEstadosFinancierosPeriodos(tipoPeriodoParam);
      setPeriodos(data);

      // Buscar el periodo que coincida con fechaFinPeriodoInicio
      if (data && data.length > 0 && fechaFinPeriodoInicio) {
        const periodoEncontrado = buscarPeriodoPorFechaFin(data, fechaFinPeriodoInicio);
        if (periodoEncontrado) {
          setPeriodoSeleccionado(periodoEncontrado);
        } else {
          // Si no se encuentra, seleccionar el primer periodo
          setPeriodoSeleccionado(data[0]);
        }
      } else if (data && data.length > 0) {
        // Si no hay fechaFinPeriodoInicio, seleccionar el primer periodo
        setPeriodoSeleccionado(data[0]);
      }
    } catch (error) {
      console.error("Error al cargar periodos:", error);
      setErrorPeriodos(error.message || "Error al cargar los periodos");
    } finally {
      setCargandoPeriodos(false);
    }
  };

  // Función para cargar los ratios financieros
  const cargarRatiosFinancieros = async (tipoPeriodoParam, fechaFinParam) => {
    try {
      setCargandoRatios(true);
      setErrorRatios(null);
      // console.log("Cargando ratios financieros en Simulaciones... : tipoPeriodoParam: ", tipoPeriodoParam, "fechaFinParam:", fechaFinParam);
      // Convertir fecha de DD/MM/YYYY a DD-MM-YYYY para el endpoint
      let fechaFinFormateada = formatearFechaDDMMYYYY(fechaFinParam);
      // console.log("cargarRatiosFinancieros fechaFinFormateada: ", fechaFinFormateada);
      fechaFinFormateada = fechaFinFormateada.replace(/\//g, '-');
      // console.log("cargarRatiosFinancieros fechaFinFormateada 2: ", fechaFinFormateada);

      const data = await FinancieraService.getRatiosEFPeriodoEspecifico(tipoPeriodoParam, fechaFinFormateada);
      setRatiosFinancieros(data);
      // console.log("Ratios financieros cargados en Simulaciones:", data);
    } catch (error) {
      console.error("Error al cargar ratios financieros:", error);
      setErrorRatios(error.message || "Error al cargar los ratios financieros");
      setRatiosFinancieros([]); // Limpiar datos en caso de error
    } finally {
      setCargandoRatios(false);
    }
  };

  // Función para cargar los datos iniciales del simulador
  const cargarDatosIniciales = async (tipoPeriodoParam, fechaFinParam) => {
    try {
      setCargandoDatosIniciales(true);
      setErrorDatosIniciales(null);
      // console.log("cargarDatosIniciales asfasfasfa : tipoPeriodoParam: ", tipoPeriodoParam, "fechaFinParam:", fechaFinParam);
      // Convertir fecha de DD/MM/YYYY a DD-MM-YYYY para el endpoint
      let fechaFinFormateada = formatearFechaDDMMYYYY(fechaFinParam);
      // console.log("cargarDatosIniciales fechaFinFormateada: ", fechaFinFormateada);
      fechaFinFormateada = fechaFinFormateada.replace(/\//g, '-');
      // console.log("cargarDatosIniciales fechaFinFormateada 2: ", fechaFinFormateada);

      const data = await FinancieraService.getTotalAgrupadorPeriodoEspecifico(tipoPeriodoParam, fechaFinFormateada);

      if (data && data.data && data.data.estados_financieros) {
        // Procesar los datos para juntar todos los agrupadores en un solo campo
        const agrupadoresUnificados = [];

        data.data.estados_financieros.forEach(estadoFinanciero => {
          if (estadoFinanciero.agrupadores) {
            estadoFinanciero.agrupadores.forEach(agrupador => {
              agrupadoresUnificados.push({
                ...agrupador,
                tipo_estado_financiero: estadoFinanciero.tipo_estado_financiero,
                id_estado_financiero: estadoFinanciero.id_estado_financiero
              });
            });
          }
        });

        // Crear el objeto de datos procesados
        const datosProcessados = {
          ...data.data,
          agrupadores: agrupadoresUnificados
        };

        setDatosIniciales(datosProcessados);
        console.log("Datos iniciales cargados en Simulaciones:", datosProcessados);
      }
    } catch (error) {
      console.error("Error al cargar datos iniciales:", error);
      setErrorDatosIniciales(error.message || "Error al cargar los datos iniciales");
      setDatosIniciales(null); // Limpiar datos en caso de error
    } finally {
      setCargandoDatosIniciales(false);
    }
  };

  // Función para buscar un periodo por fecha fin
  const buscarPeriodoPorFechaFin = (periodosData, fechaFinBuscada) => {
    // console.log("Buscando periodo por fecha fin:", fechaFinBuscada);
    // console.log("Periodos disponibles:", periodosData);
    for (const periodo of periodosData) {
      for (const estadoFinanciero of periodo.estados_financieros) {
        const fechaFinFormateada = formatearFechaDDMMYYYY(estadoFinanciero.dt_fechaFinPeriodo);
        // console.log("Fecha fin formateada:", fechaFinFormateada);
        if (fechaFinFormateada === fechaFinBuscada) {
          // console.log("Periodo encontrado:", periodo);
          return periodo;
        }
      }
    }
    return null;
  };

  // Manejar cambio de empresa
  const handleEmpresaChange = (empresa) => {
    setEmpresaSeleccionada(empresa);

    // Guardar el ID de la empresa en las cookies usando encrypt
    if (empresa && empresa.int_idEmpresa) {
      Cookies.set("busFinanciera", encrypt(empresa.int_idEmpresa.toString()));
      // Guardar en cookies moneda y simbolo
      Cookies.set("monedaFinanciera", encrypt(empresa.str_Moneda));
      Cookies.set("simbMonedaFinanciera", encrypt(empresa.str_SimboloMoneda));
      // console.log("Empresa seleccionada guardado en cookies:", empresa);
    }

    // Cargar periodos para la nueva empresa
    if (tipoPeriodoActual) {
      cargarPeriodos(tipoPeriodoActual);
    }

    // Cargar ratios financieros para la nueva empresa si tenemos los parámetros necesarios
    if (tipoPeriodoActual && fechaFinActual) {
      // console.log("Cargando ratios financieros en Simulaciones... HANDLEEMPRESACHANGE : tipoPeriodoActual: ", tipoPeriodoActual, "fechaFinActual:", fechaFinActual);
      cargarRatiosFinancieros(tipoPeriodoActual, fechaFinActual);
      cargarDatosIniciales(tipoPeriodoActual, fechaFinActual);
    }
  };

  // Manejar cambio de periodo
  const handlePeriodoChange = (periodo) => {
    setPeriodoSeleccionado(periodo);
    // console.log("Periodo seleccionado:", periodo);

    // Actualizar los parámetros actuales si es necesario
    if (periodo && periodo.estados_financieros && periodo.estados_financieros.length > 0) {
      // Obtener la fecha fin del primer estado financiero del periodo
      const fechaFinPeriodo = formatearFechaDDMMYYYY(periodo.estados_financieros[0].dt_fechaFinPeriodo);
      // console.log("Fecha fin del periodo:", fechaFinPeriodo);

      // Actualizar el estado fechaFinActual
      setFechaFinActual(fechaFinPeriodo);

      // Cargar ratios financieros para el nuevo periodo
      if (tipoPeriodoActual) {
        cargarRatiosFinancieros(tipoPeriodoActual, fechaFinPeriodo);
        cargarDatosIniciales(tipoPeriodoActual, fechaFinPeriodo);
      }
    }
  };

  // useEffect para mostrar los parámetros recibidos desde otras páginas
  useEffect(() => {
    if (tipoPeriodoInicio || fechaFinPeriodoInicio || idEstadoFinancieroInicio) {
      console.log("=== PARÁMETROS RECIBIDOS EN SIMULACIONES ===");
      console.log("Tipo de Periodo:", tipoPeriodoInicio, tipoPeriodoInicio === 1 ? "(Anual)" : tipoPeriodoInicio === 3 ? "(Mensual)" : "(Otro)");
      console.log("Fecha Fin de Periodo:", fechaFinPeriodoInicio);
      console.log("ID Estado Financiero Tipo 1:", idEstadoFinancieroInicio);
      console.log("Origen: Puede ser desde Inicio.jsx o DetalleEF.jsx");
      console.log("===============================================");
    } else {
      console.log("No se recibieron parámetros desde otras páginas");
    }
  }, []);

  // Efecto para cargar las empresas al montar el componente
  useEffect(() => {
    cargarEmpresas();
  }, []);

  // Efecto para cargar periodos cuando tipoPeriodoActual esté disponible
  useEffect(() => {
    if (tipoPeriodoActual && empresaSeleccionada) {
      cargarPeriodos(tipoPeriodoActual);
    }
  }, [tipoPeriodoActual, empresaSeleccionada]);

  // Efecto para cargar la información de moneda desde cookies
  useEffect(() => {
    obtenerMonedaDesdeCookies();
  }, [empresaSeleccionada]); // Se ejecuta cuando cambia la empresa seleccionada

  // Efecto para cargar ratios financieros cuando los parámetros estén disponibles
  useEffect(() => {
    if (tipoPeriodoActual && fechaFinActual && empresaSeleccionada) {
      // console.log("Cargando ratios financieros en Simulaciones... USEFFECT : tipoPeriodoActual: ", tipoPeriodoActual, "fechaFinActual:", fechaFinActual);
      cargarRatiosFinancieros(tipoPeriodoActual, fechaFinActual);
      cargarDatosIniciales(tipoPeriodoActual, fechaFinActual);
    }
  }, [tipoPeriodoActual, fechaFinActual, empresaSeleccionada]);

  // Efecto para actualizar valores DuPont cuando se cargan los datos
  useEffect(() => {
    if (datosIniciales && ratiosFinancieros.length > 0) {
      actualizarValoresConDatosReales();
    }
  }, [datosIniciales, ratiosFinancieros]);

  // Actualizar historial cuando cambian los valores
  useEffect(() => {
    // Solo añadir al historial si los valores son diferentes al último registro
    const ultimoRegistro = historialCambios[historialCambios.length - 1];
    if (
      ultimoRegistro.utilidadNeta !== utilidadNeta ||
      ultimoRegistro.ventas !== ventas ||
      ultimoRegistro.activos !== activos ||
      ultimoRegistro.patrimonio !== patrimonio
    ) {
      setHistorialCambios([
        ...historialCambios,
        {
          nombre: `Simulación ${historialCambios.length}`,
          utilidadNeta,
          ventas,
          activos,
          patrimonio,
          roe: parseFloat(roeSimulado.toFixed(4))
        }
      ]);
    }
  }, [utilidadNeta, ventas, activos, patrimonio]);

  // Preparar datos para el gráfico
  const prepararDatosGrafico = () => {
    // Normalizar valores para que sean comparables en el gráfico
    return historialCambios.map(registro => ({
      nombre: registro.nombre,
      "Utilidad Neta": (registro.utilidadNeta / 1000000).toFixed(1),
      "Ventas": (registro.ventas / 10000000).toFixed(1),
      "Activos": (registro.activos / 1000000).toFixed(1),
      "Patrimonio": (registro.patrimonio / 1000000).toFixed(1),
      "ROE": registro.roe
    }));
  };

  // Funciones para manejar cambios en los sliders
  const handleUtilidadNetaChange = (e) => {
    const value = parseInt(e.target.value);
    setUtilidadNetaSlider(value);
    // Calcular rango dinámico: 50% menos a 100% más del valor original
    const valorOriginal = valoresOriginales.utilidadNeta;
    const minimo = valorOriginal * 0.5;
    const maximo = valorOriginal * 2;
    const nuevoValor = minimo + ((maximo - minimo) * value / 100);
    setUtilidadNeta(nuevoValor);
  };

  const handleVentasChange = (e) => {
    const value = parseInt(e.target.value);
    setVentasSlider(value);
    // Calcular rango dinámico: 50% menos a 100% más del valor original
    const valorOriginal = valoresOriginales.ventas;
    const minimo = valorOriginal * 0.5;
    const maximo = valorOriginal * 2;
    const nuevoValor = minimo + ((maximo - minimo) * value / 100);
    setVentas(nuevoValor);
  };

  const handleActivosChange = (e) => {
    const value = parseInt(e.target.value);
    setActivosSlider(value);
    // Calcular rango dinámico: 50% menos a 100% más del valor original
    const valorOriginal = valoresOriginales.activos;
    const minimo = valorOriginal * 0.5;
    const maximo = valorOriginal * 2;
    const nuevoValor = minimo + ((maximo - minimo) * value / 100);
    setActivos(nuevoValor);
  };

  const handlePatrimonioChange = (e) => {
    const value = parseInt(e.target.value);
    setPatrimonioSlider(value);
    // Calcular rango dinámico: 50% menos a 100% más del valor original
    const valorOriginal = valoresOriginales.patrimonio;
    const minimo = valorOriginal * 0.5;
    const maximo = valorOriginal * 2;
    const nuevoValor = minimo + ((maximo - minimo) * value / 100);
    setPatrimonio(nuevoValor);
  };

  // Función para formatear números
  const formatNumber = (num) => {
    if (num === null || num === undefined) return `${simboloMoneda}0`;

    // Redondear a 2 decimales
    const numeroRedondeado = Math.round(num * 100) / 100;

    // Separar parte entera y decimal
    const partes = numeroRedondeado.toString().split('.');
    const parteEntera = partes[0];
    const parteDecimal = partes[1];

    // Formatear la parte entera con separadores de miles (comas)
    const parteEnteraFormateada = parseInt(parteEntera).toLocaleString('es-ES').replace(/\./g, ',');

    // Construir el número final
    let numeroFormateado;
    if (parteDecimal) {
      // Si hay decimales, usar punto como separador decimal
      numeroFormateado = `${parteEnteraFormateada}.${parteDecimal}`;
    } else {
      // Si no hay decimales, solo la parte entera
      numeroFormateado = parteEnteraFormateada;
    }

    // Agregar símbolo de moneda
    return `${simboloMoneda}${numeroFormateado}`;
  };

  // Función para formatear números con separadores de miles y símbolo de moneda
  const formatearNumero = (numero, conSeparador = true, conMoneda = false) => {
    if (numero === null || numero === undefined) return conMoneda ? `${simboloMoneda}0` : "0";

    // Redondear a 2 decimales
    const numeroRedondeado = Math.round(numero * 100) / 100;

    let numeroFormateado;
    if (conSeparador) {
      // Formatear con separadores de miles usando coma como separador de miles
      numeroFormateado = numeroRedondeado.toLocaleString('es-ES').replace(/\./g, ',');
    } else {
      // Sin separadores para los datos de gráficos
      numeroFormateado = numeroRedondeado.toString();
    }

    // Agregar símbolo de moneda si se solicita
    return conMoneda ? `${simboloMoneda}${numeroFormateado}` : numeroFormateado;
  };

  // Función para calcular ratios dinámicos basados en los valores del simulador
  const calcularRatiosDinamicos = () => {
    // Verificar que los valores no sean cero para evitar división por cero
    if (ventas === 0 || activos === 0 || patrimonio === 0) {
      return [];
    }

    // Calcular los ratios según las fórmulas proporcionadas
    const margenNeto = (utilidadNeta / ventas) * 100;
    const rotacion = (ventas / activos) * 100;
    const apalancamiento = (activos / patrimonio) * 100;
    const roe = (utilidadNeta / patrimonio) * 100;
    const roa = (utilidadNeta / activos) * 100;

    // Aproximar a 2 decimales
    return [
      {
        nombre_ratio: "Margen Neto",
        valor_ratio: parseFloat(margenNeto.toFixed(2)),
        formula_ratio: "Utilidad Neta / Ventas"
      },
      {
        nombre_ratio: "Rotación",
        valor_ratio: parseFloat(rotacion.toFixed(2)),
        formula_ratio: "Ventas / Activo Total"
      },
      {
        nombre_ratio: "Apalancamiento",
        valor_ratio: parseFloat(apalancamiento.toFixed(2)),
        formula_ratio: "Activo Total / Patrimonio"
      },
      {
        nombre_ratio: "ROE",
        valor_ratio: parseFloat(roe.toFixed(2)),
        formula_ratio: "Utilidad Neta / Patrimonio"
      },
      {
        nombre_ratio: "ROA",
        valor_ratio: parseFloat(roa.toFixed(2)),
        formula_ratio: "Utilidad Neta / Activo Total"
      }
    ];
  };

  // Función para formatear el valor de un ratio
  const formatearValorRatio = (valor, nombreRatio) => {
    if (valor === null || valor === undefined) return "N/A";

    // Definir qué ratios son montos (el resto son porcentajes)
    const ratiosMontos = ["Capital de Trabajo", "EBITDA"];
    const esRatioMonto = ratiosMontos.some(ratio => nombreRatio.toLowerCase().includes(ratio.toLowerCase()));

    if (esRatioMonto) {
      // Para ratios de monto, formatear con separadores de miles y símbolo de moneda
      return formatearNumero(valor, true, true);
    } else {
      // Para ratios de porcentaje, ya vienen multiplicados por 100 desde el backend
      if (valor !== 0) {
        let valorAproximado = valor.toFixed(2);
        if (valorAproximado === "0.00" || valorAproximado === "-0.00") {
          valorAproximado = valor.toFixed(3);
          if (valorAproximado === "0.000" || valorAproximado === "-0.000") {
            valorAproximado = valor.toFixed(4);
            if (valorAproximado === "0.0000" || valorAproximado === "-0.0000") {
              valorAproximado = '0';
            }
          }
        }
        return `${valorAproximado}%`;
      } else {
        return `${valor}%`;
      }
    }
  };

  // Función para restablecer valores
  const restablecerValores = () => {
    // Usar valores originales del endpoint si están disponibles
    setUtilidadNeta(valoresOriginales.utilidadNeta);
    setUtilidadNetaSlider(50);
    setVentas(valoresOriginales.ventas);
    setVentasSlider(50);
    setActivos(valoresOriginales.activos);
    setActivosSlider(50);
    setPatrimonio(valoresOriginales.patrimonio);
    setPatrimonioSlider(50);
  };

  // Función para guardar la simulación
  const guardarSimulacion = async () => {
    try {
      setGuardandoSimulacion(true);
      setErrorGuardarSimulacion(null);
      setSimulacionGuardada(false);

      // Verificar que tenemos el ID del estado financiero de referencia
      if (!idEstadoFinancieroActual) {
        throw new Error("No se ha encontrado el ID del estado financiero de referencia");
      }

      // Preparar los datos de agrupadores con los valores actuales del simulador
      const agrupadoresSimulacion = {
        "ventas netas": ventas,
        "utilidad neta": utilidadNeta,
        "activos totales": activos,
        "patrimonio": patrimonio
      };

      console.log("Guardando simulación con ID estado financiero:", idEstadoFinancieroActual);
      console.log("Agrupadores de simulación:", agrupadoresSimulacion);

      const resultado = await FinancieraService.guardarSimulacion(
        idEstadoFinancieroActual,
        agrupadoresSimulacion
      );

      if (resultado.success) {
        setSimulacionGuardada(true);
        console.log("Simulación guardada exitosamente:", resultado.data);

        // Mostrar mensaje de éxito por unos segundos
        setTimeout(() => {
          setSimulacionGuardada(false);
        }, 3000);
      } else {
        throw new Error(resultado.message || "Error al guardar la simulación");
      }
    } catch (error) {
      console.error("Error al guardar simulación:", error);
      setErrorGuardarSimulacion(error.message || "Error al guardar la simulación");

      // Limpiar el error después de unos segundos
      setTimeout(() => {
        setErrorGuardarSimulacion(null);
      }, 5000);
    } finally {
      setGuardandoSimulacion(false);
    }
  };

  // Valores iniciales dinámicos obtenidos del backend
  const valoresIniciales = {
    utilidadNeta: valoresOriginales.utilidadNeta || 1000000,
    ventas: valoresOriginales.ventas || 1000000,
    activos: valoresOriginales.activos || 1000000,
    patrimonio: valoresOriginales.patrimonio || 1000000,
    roe: valoresOriginales.roe || 1.0
  };

  // Función para preparar datos de comparación para el gráfico
  const prepararDatosComparacion = () => {
    // Calcular ROE actual
    const margenNeto = utilidadNeta / ventas;
    const rotacionActivos = ventas / activos;
    const multiplicadorCapital = activos / patrimonio;
    const roeActual = margenNeto * rotacionActivos * multiplicadorCapital * 100;

    return [
      {
        nombre: "Utilidad Neta",
        "Valor Original": valoresIniciales.utilidadNeta,
        "Valor Simulado": utilidadNeta,
        "Diferencia": ((utilidadNeta - valoresIniciales.utilidadNeta) / valoresIniciales.utilidadNeta * 100).toFixed(1)
      },
      {
        nombre: "Ventas",
        "Valor Original": valoresIniciales.ventas,
        "Valor Simulado": ventas,
        "Diferencia": ((ventas - valoresIniciales.ventas) / valoresIniciales.ventas * 100).toFixed(1)
      },
      {
        nombre: "Activos",
        "Valor Original": valoresIniciales.activos,
        "Valor Simulado": activos,
        "Diferencia": ((activos - valoresIniciales.activos) / valoresIniciales.activos * 100).toFixed(1)
      },
      {
        nombre: "Patrimonio",
        "Valor Original": valoresIniciales.patrimonio,
        "Valor Simulado": patrimonio,
        "Diferencia": ((patrimonio - valoresIniciales.patrimonio) / valoresIniciales.patrimonio * 100).toFixed(1)
      },
      {
        // Aqui mostrar los datos de la data recibida del endpoint que trae los ratios
        nombre: "ROE",
        "Valor Original": ratiosFinancieros.length > 0 ? parseFloat(ratiosFinancieros.find(ratio => ratio.nombre_ratio === "ROE")?.valor_ratio.toFixed(1)) : valoresIniciales.roe,
        "Valor Simulado": parseFloat(roeActual.toFixed(1)),
        "Diferencia": ((roeActual - valoresIniciales.roe) / valoresIniciales.roe * 100).toFixed(1)
      }
    ];
  };

  // Función para preparar datos para el gráfico lineal
  const prepararDatosGraficoLineal = () => {
    // Calcular ROE actual
    const margenNeto = utilidadNeta / ventas;
    const rotacionActivos = ventas / activos;
    const multiplicadorCapital = activos / patrimonio;
    const roeActual = margenNeto * rotacionActivos * multiplicadorCapital * 100;

    // Crear array con solo dos puntos: valores originales y valores simulados
    return [
      {
        nombre: "Valores Originales",
        "Utilidad Neta": valoresIniciales.utilidadNeta / 1000000,
        "Ventas": valoresIniciales.ventas / 10000000,
        "Activos": valoresIniciales.activos / 1000000,
        "Patrimonio": valoresIniciales.patrimonio / 1000000,
        "ROE": valoresIniciales.roe
      },
      {
        nombre: "Valores Simulados",
        "Utilidad Neta": utilidadNeta / 1000000,
        "Ventas": ventas / 10000000,
        "Activos": activos / 1000000,
        "Patrimonio": patrimonio / 1000000,
        "ROE": parseFloat(roeActual.toFixed(1))
      }
    ];
  };

  // Componente para mostrar un ratio individual con tooltip
  const RatioItem = ({ nombre, valor, formula }) => {
    const [showTooltip, setShowTooltip] = useState(false);

    return (
      <div className="flex flex-col w-full gap-1">
        <div className="flex justify-between w-full">
          <div className="flex justify-start items-center gap-2">
            <span className="text-[#1F263E] text-md font-base">
              {nombre}
            </span>
            <div
              className="relative"
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
            >
              <IconoInformacion size={"1rem"} color={"#979797"} />
              {showTooltip && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg shadow-lg z-10 whitespace-nowrap">
                  {formula}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                </div>
              )}
            </div>
          </div>
          <span className="text-[#1F263E] text-md font-base">
            {formatearValorRatio(valor, nombre)}
          </span>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full h-full flex flex-col gap-4">
      <div className="flex w-full flex-col gap-4 justify-start items-start">
        <div
          className="flex gap-1 justify-start items-center cursor-pointer"
          onClick={() => navigate(RoutesPrivate.INICIO)}
        >
          <IconoRegresar
            size={"1.8rem"}
            color={"#909090"}
            salir={true}
            pagina={RoutesPrivate.INICIO}
          />
          <span className=" text-sm font-semibold text-[#909090]">
            Dashboard
          </span>
        </div>
        <div className="flex justify-between w-full">
          <div className="flex flex-col gap-1">
            <span className="text-[#1F263E] text-2xl font-semibold">
              Simulador de Análisis DuPont
            </span>
            <span className="text-[#C3C0C0] text-sm font-base">
              Ajuste los ratios financieros para simular el Retorno sobre el
              Patrimonio (ROE) y comparar con el rendimiento real.
            </span>
            {/* Mostrar información de los parámetros recibidos */}
            {/* {console.log("fechaFinPeriodoInicio", fechaFinPeriodoInicio)} */}
            {/* {(tipoPeriodoInicio || fechaFinPeriodoInicio || idEstadoFinancieroInicio) && (
              <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <span className="text-blue-800 text-xs font-medium">
                  Datos del periodo seleccionado:
                  {tipoPeriodoInicio && ` Tipo ${tipoPeriodoInicio === 1 ? 'Anual' : 'Mensual'}`}
                  {fechaFinPeriodoInicio && ` | Fecha: ${formatearFechaDDMMYYYY(fechaFinPeriodoInicio)}`}
                  {idEstadoFinancieroInicio && ` | EF ID: ${idEstadoFinancieroInicio}`}
                </span>
              </div>
            )} */}

            {/* Mostrar información de los datos iniciales cargados */}
            {/* {cargandoDatosIniciales && (
              <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <span className="text-yellow-800 text-xs font-medium">
                  Cargando datos iniciales del simulador...
                </span>
              </div>
            )}

            {errorDatosIniciales && (
              <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <span className="text-red-800 text-xs font-medium">
                  Error: {errorDatosIniciales}
                </span>
              </div>
            )}

            {datosIniciales && (
              <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                <span className="text-green-800 text-xs font-medium">
                  Datos iniciales cargados: {datosIniciales.agrupadores?.length || 0} agrupadores encontrados
                  {datosIniciales.fecha_fin && ` | Fecha: ${datosIniciales.fecha_fin}`}
                </span>
              </div>
            )} */}
          </div>
          <div className="flex gap-4 ">
            <select
              name="empresa"
              id="empresa"
              className="border border-gray-400 rounded-md p-1 min-w-[13rem] outline-none h-[2.5rem]"
              value={empresaSeleccionada?.int_idEmpresa || ""}
              onChange={(e) => {
                const empresaId = parseInt(e.target.value);
                const empresa = empresas.find(emp => emp.int_idEmpresa === empresaId);
                handleEmpresaChange(empresa);
              }}
            >
              {cargandoEmpresas ? (
                <option value="" disabled>Cargando empresas...</option>
              ) : errorEmpresas ? (
                <option value="" disabled>Error al cargar empresas</option>
              ) : empresas.length === 0 ? (
                <option value="" disabled>No hay empresas disponibles</option>
              ) : (
                empresas.map((empresa) => (
                  <option key={empresa.int_idEmpresa} value={empresa.int_idEmpresa}>
                    {empresa.str_NombreEmpresa}
                  </option>
                ))
              )}
            </select>
            <select
              name="periodo"
              id="periodo"
              className="border border-gray-400 rounded-md p-1 min-w-[13rem] outline-none h-[2.5rem]"
              value={periodoSeleccionado?.periodo_contable || ""}
              onChange={(e) => {
                const periodoContable = e.target.value;
                const periodo = periodos.find(p => p.periodo_contable === periodoContable);
                handlePeriodoChange(periodo);
              }}
            >
              {cargandoPeriodos ? (
                <option value="" disabled>Cargando periodos...</option>
              ) : errorPeriodos ? (
                <option value="" disabled>Error al cargar periodos</option>
              ) : periodos.length === 0 ? (
                <option value="" disabled>No hay periodos disponibles</option>
              ) : (
                periodos.map((periodo, index) => (
                  <option key={index} value={periodo.periodo_contable}>
                    {formatearNombrePeriodo(periodo, tipoPeriodoActual)}
                  </option>
                ))
              )}
            </select>
            <button
              className="group bg-[#FFFF] text-[#1F263E] p-2 rounded-lg border-[#E4E4E4] border-2 gap-2 flex items-center justify-center hover:bg-[#1F263E] hover:text-[#FFFF] cursor-pointer hover:border-[#1F263E] h-[2.5rem]"
              onClick={() => navigate(RoutesPrivate.COMPARATIVA)}
            >
              <IconoSimulaciones
                size={"1.2rem"}
                className="text-[#1F263E] group-hover:text-[#FFFF]"
              />{" "}
              Comparar Simulaciones
              <IconoFlechaSimular
                size={"1.2rem"}
                className="text-[#1F263E] group-hover:text-[#FFFF]"
              />
            </button>
          </div>
        </div>
      </div>

      <div className="flex w-full gap-4 justify-start items-start mt-3">
        <div className="flex flex-col gap-2 w-[50%] border-1 border-[#EFEFEF] rounded-lg">
          <div className="p-5 flex flex-col gap-2 bg-[#F8FAF8] justify-start items-start">
            <span className="text-[#1F263E] text-xl font-medium">
              Fórmula Dupont
            </span>
            <span className="text-[#C3C0C0] text-sm font-base">
              Ajuste los componentes para ver cómo afectan su Retorno sobre el
              Patrimonio (ROE){" "}
            </span>
          </div>
          <div className="p-5 flex flex-col gap-6 justify-start items-start">
            <div className="flex border-1 border-[#EFEFEF] rounded-lg p-5 w-full">
              {/* Fórmula DuPont visual */}
              <div className="w-full flex flex-col gap-6">
                <div className="flex items-center justify-between w-full">
                  <div className="flex flex-col items-center">
                    <div className="bg-[#F0F0FF] px-4 py-2 rounded-md text-[#5D5FEF] font-medium text-sm">
                      {formatNumber(utilidadNeta)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Utilidad Neta</span>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#FFF0F0] px-4 py-2 rounded-md text-[#FF6B8A] font-medium text-sm">
                      {formatNumber(ventas)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Ventas</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-400">×</div>
                  <div className="flex flex-col items-center">
                    <div className="bg-[#FFF0F0] px-4 py-2 rounded-md text-[#FF6B8A] font-medium text-sm">
                      {formatNumber(ventas)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Ventas</span>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#F0FFF0] px-4 py-2 rounded-md text-[#31C969] font-medium text-sm">
                      {formatNumber(activos)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Activos</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-400">×</div>
                  <div className="flex flex-col items-center">
                    <div className="bg-[#F0FFF0] px-4 py-2 rounded-md text-[#31C969] font-medium text-sm">
                      {formatNumber(activos)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Activos</span>
                    <div className="w-full border-t border-gray-300 my-2"></div>
                    <div className="bg-[#F0F0FF] px-4 py-2 rounded-md text-[#5D5FEF] font-medium text-sm">
                      {formatNumber(patrimonio)}
                    </div>
                    <span className="text-xs text-[#979797] mt-1">Patrimonio</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-400">=</div>
                  <div className="bg-[#FFF8F0] px-4 py-2 rounded-md text-[#FF9F43] font-medium text-sm">
                    {roeSimulado.toFixed(2)}%
                  </div>
                  <span className="text-xs text-[#979797] mt-1">ROE</span>
                </div>
              </div>
            </div>

            <div className="flex border-1 border-[#EFEFEF] rounded-lg p-5 w-full">
              {/* Componentes DuPont separados */}
              <div className="w-full flex flex-col gap-6 p-5 pt-0">
                <div className="flex justify-between mt-8">
                  {/* Margen Neto */}
                  <div className="flex flex-col items-center">
                    <span className="text-[#1F263E] text-md font-medium mb-2">Margen Neto</span>
                    <div className="flex items-center gap-4">
                      <div className="flex flex-col items-center">
                        <div className="bg-[#F0F0FF] px-3 py-1 rounded text-[#5D5FEF] font-medium text-md mb-1">
                          <span className="text-xs text-[#979797]">Utilidad neta</span>
                          <div className="font-semibold">{formatNumber(utilidadNeta)}</div>
                        </div>
                        <div className="w-full border-t border-gray-300 my-1"></div>
                        <div className="bg-[#E6F7EA] px-3 py-1 rounded text-[#34A853] font-medium text-md mt-1">
                          <span className="text-xs text-[#979797]">Ventas</span>
                          <div className="font-semibold">{formatNumber(ventas)}</div>
                        </div>
                      </div>
                      <div className="bg-[#FFF8E0] px-4 py-2 rounded-md text-[#FF9F43] font-medium text-md flex items-center justify-center min-w-[80px]">
                        {(margenNeto * 100).toFixed(2)}%
                      </div>
                    </div>
                  </div>

                  {/* Rotación de Activos */}
                  <div className="flex flex-col items-center">
                    <span className="text-[#1F263E] text-md font-medium mb-2">Rotación de Activos</span>
                    <div className="flex items-center gap-4">
                      <div className="flex flex-col items-center">
                        <div className="bg-[#E6F7EA] px-3 py-1 rounded text-[#34A853] font-medium text-md mb-1">
                          <span className="text-xs text-[#979797]">Ventas</span>
                          <div className="font-semibold">{formatNumber(ventas)}</div>
                        </div>
                        <div className="w-full border-t border-gray-300 my-1"></div>
                        <div className="bg-[#EEE6FF] px-3 py-1 rounded text-[#5D5FEF] font-medium text-md mt-1">
                          <span className="text-xs text-[#979797]">Activos</span>
                          <div className="font-semibold">{formatNumber(activos)}</div>
                        </div>
                      </div>
                      <div className="bg-[#FFF8E0] px-4 py-2 rounded-md text-[#FF9F43] font-medium text-md flex items-center justify-center min-w-[80px]">
                        {(rotacionActivos * 100).toFixed(2)}%
                      </div>
                    </div>
                  </div>

                  {/* Apalancamiento */}
                  <div className="flex flex-col items-center">
                    <span className="text-[#1F263E] text-md font-medium mb-2">Apalancamiento</span>
                    <div className="flex items-center gap-4">
                      <div className="flex flex-col items-center">
                        <div className="bg-[#EEE6FF] px-3 py-1 rounded text-[#5D5FEF] font-medium text-md mb-1">
                          <span className="text-xs text-[#979797]">Activos</span>
                          <div className="font-semibold">{formatNumber(activos)}</div>
                        </div>
                        <div className="w-full border-t border-gray-300 my-1"></div>
                        <div className="bg-[#FFF8E0] px-3 py-1 rounded text-[#FF9F43] font-medium text-md mt-1">
                          <span className="text-xs text-[#979797]">Patrimonio</span>
                          <div className="font-semibold">{formatNumber(patrimonio)}</div>
                        </div>
                      </div>
                      <div className="bg-[#FFF8E0] px-4 py-2 rounded-md text-[#FF9F43] font-medium text-md flex items-center justify-center min-w-[80px]">
                        {(multiplicadorCapital * 100).toFixed(2)}%
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Comparación de ROE */}
            <div className="flex border-1 border-[#EFEFEF] rounded-lg p-5 w-full">
              <div className="w-full flex">
                {/* ROE Real */}
                <div className="w-1/2 flex flex-col items-center justify-center border-r border-[#EFEFEF] p-3">
                  <span className="text-[#1F263E] text-sm mb-2">
                    Retorno sobre el Patrimonio (ROE) Real
                  </span>
                  <span className="text-[#FF4D8D] text-3xl font-medium">
                    {/* Aproximar a 2 decimales */}
                    {roeReal ? roeReal.toFixed(2) : "N/A"}%
                  </span>
                </div>

                {/* ROE Simulado */}
                <div className="w-1/2 flex flex-col items-center justify-center p-3">
                  <span className="text-[#1F263E] text-sm mb-2">
                    Retorno sobre el Patrimonio (ROE) Simulado
                  </span>
                  <span className="text-[#31C969] text-3xl font-medium">
                    {roeSimulado.toFixed(2)}%
                  </span>
                  <span className="text-[#31C969] text-sm mt-1">
                    Real: {roeReal ? roeReal.toFixed(2) : "N/A"}% | Diferencia:{" "}
                    {(roeSimulado - roeReal).toFixed(2)}%
                  </span>
                </div>
              </div>
            </div>

            {/* Botón para restablecer valores */}
            {/* <div className="w-full flex justify-end">
              <button
                onClick={restablecerValores}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg border border-gray-300 transition-colors"
              >
                <IconoReestablecer size={"1rem"} color={"#6B7280"} />
                <span className="text-sm">Restablecer valores originales</span>
              </button>
            </div> */}

            {/* Sliders para ajustar valores */}
            <div className="w-full flex flex-col gap-4 mt-2">
              <div className="flex gap-4">
                {/* Utilidad Neta */}
                <div className="flex flex-col gap-1 w-1/2">
                  <div className="flex justify-between">
                    <span className="text-[#1F263E] text-sm font-medium">
                      Utilidad neta
                    </span>
                    <div className="border border-[#EFEFEF] rounded px-2 py-1 text-sm">
                      {formatNumber(utilidadNeta)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={utilidadNetaSlider}
                      onChange={handleUtilidadNetaChange}
                      className="w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #5D5FEF ${utilidadNetaSlider}%, #E5E7EB ${utilidadNetaSlider}%)`,
                      }}
                    />
                  </div>
                </div>

                {/* Activos */}
                <div className="flex flex-col gap-1 w-1/2">
                  <div className="flex justify-between">
                    <span className="text-[#1F263E] text-sm font-medium">
                      Activos
                    </span>
                    <div className="border border-[#EFEFEF] rounded px-2 py-1 text-sm">
                      {formatNumber(activos)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={activosSlider}
                      onChange={handleActivosChange}
                      className="w-full h-2 bg-purple-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #737507 ${activosSlider}%, #E5E7EB ${activosSlider}%)`,
                      }}
                    />
                  </div>
                </div>
              </div>

              <div className="flex gap-4">
                {/* Ventas */}
                <div className="flex flex-col gap-1 w-1/2">
                  <div className="flex justify-between">
                    <span className="text-[#1F263E] text-sm font-medium">
                      Ventas
                    </span>
                    <div className="border border-[#EFEFEF] rounded px-2 py-1 text-sm">
                      {formatNumber(ventas)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={ventasSlider}
                      onChange={handleVentasChange}
                      className="w-full h-2 bg-green-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #34A853 ${ventasSlider}%, #E5E7EB ${ventasSlider}%)`,
                      }}
                    />
                  </div>
                </div>

                {/* Patrimonio */}
                <div className="flex flex-col gap-1 w-1/2">
                  <div className="flex justify-between">
                    <span className="text-[#1F263E] text-sm font-medium">
                      Patrimonio
                    </span>
                    <div className="border border-[#EFEFEF] rounded px-2 py-1 text-sm">
                      {formatNumber(patrimonio)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={patrimonioSlider}
                      onChange={handlePatrimonioChange}
                      className="w-full h-2 bg-orange-200 rounded-lg appearance-none cursor-pointer"
                      style={{
                        background: `linear-gradient(to right, #FF6B00 ${patrimonioSlider}%, #E5E7EB ${patrimonioSlider}%)`,
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-2 w-[50%] border-1 border-[#EFEFEF] rounded-lg">
          <div className="p-5 flex flex-col gap-2 bg-[#F8FAF8] justify-start items-start">
            <span className="text-[#1F263E] text-xl font-medium">Ratios</span>
            <span className="text-[#C3C0C0] text-sm font-base">
              Impacto en el rendimiento financiero
            </span>
          </div>
          <div className="p-5 flex gap-4 w-full">
            {/* Mostrar ratios dinámicos y estáticos */}
            {(() => {
              const ratiosDinamicos = calcularRatiosDinamicos();

              // Combinar ratios dinámicos con ratios del backend
              // Filtrar ratios del backend que no están en los dinámicos
              const nombresDinamicos = ratiosDinamicos.map(r => r.nombre_ratio.toLowerCase());
              const ratiosEstaticos = ratiosFinancieros.filter(ratio =>
                !nombresDinamicos.includes(ratio.nombre_ratio.toLowerCase())
              );

              // Combinar todos los ratios: primero los dinámicos, luego los estáticos
              const todosLosRatios = [...ratiosDinamicos, ...ratiosEstaticos];

              if (todosLosRatios.length === 0) {
                return (
                  <div className="flex justify-center items-center w-full py-4">
                    <span className="text-[#979797] text-sm">
                      {cargandoRatios ? "Cargando ratios financieros..." :
                       errorRatios ? errorRatios :
                       "No hay ratios financieros disponibles"}
                    </span>
                  </div>
                );
              }

              return (
                <>
                  <div className="flex flex-col w-[50%] gap-6">
                    {/* Ratios - columna izquierda */}
                    {todosLosRatios.slice(0, Math.ceil(todosLosRatios.length / 2)).map((ratio, index) => {
                      const esDinamico = nombresDinamicos.includes(ratio.nombre_ratio.toLowerCase());
                      return (
                        <div key={index} className="relative">
                          {/* {esDinamico && (
                            <div className="absolute -top-1 -right-1 bg-green-500 text-white text-xs px-1 py-0.5 rounded-full text-[10px]">
                              Dinámico
                            </div>
                          )} */}
                          <RatioItem
                            nombre={ratio.nombre_ratio}
                            valor={ratio.valor_ratio}
                            formula={ratio.formula_ratio}
                          />
                        </div>
                      );
                    })}
                  </div>
                  <div className="flex flex-col w-[50%] gap-6">
                    {/* Ratios - columna derecha */}
                    {todosLosRatios.slice(Math.ceil(todosLosRatios.length / 2)).map((ratio, index) => {
                      const esDinamico = nombresDinamicos.includes(ratio.nombre_ratio.toLowerCase());
                      return (
                        <div key={index + Math.ceil(todosLosRatios.length / 2)} className="relative">
                          {/* {esDinamico && (
                            <div className="absolute -top-1 -right-1 bg-green-500 text-white text-xs px-1 py-0.5 rounded-full text-[10px]">
                              Dinámico
                            </div>
                          )} */}
                          <RatioItem
                            nombre={ratio.nombre_ratio}
                            valor={ratio.valor_ratio}
                            formula={ratio.formula_ratio}
                          />
                        </div>
                      );
                    })}
                  </div>
                </>
              );
            })()}
          </div>



        </div>
      </div>

          <div className="mt-2 p-4 bg-[#F8FAF8] rounded-lg">
            <h3 className="text-[#1F263E] font-medium mb-2">Resumen de cambios</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {prepararDatosComparacion().map((item) => (
                <div key={item.nombre} className="flex flex-col p-3 bg-white rounded-lg shadow-sm">
                  <span className="text-sm text-[#6B7280]">{item.nombre}</span>
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-lg font-medium text-[#1F263E]">
                      {item.nombre === "ROE"
                        ? `${item["Valor Simulado"]}%`
                        : `${formatNumber(item["Valor Simulado"])}`}
                    </span>
                    <span className={`text-sm font-medium ${parseFloat(item["Diferencia"]) >= 0 ? 'text-[#34A853]' : 'text-[#FF4D4D]'}`}>
                      {parseFloat(item["Diferencia"]) >= 0 ? '+' : ''}{item["Diferencia"]}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

      {/* Mensaje de error al guardar simulación */}
      {errorGuardarSimulacion && (
        <div className="w-full p-3 bg-red-50 border border-red-200 rounded-lg">
          <span className="text-red-800 text-sm font-medium">
            Error al guardar simulación: {errorGuardarSimulacion}
          </span>
        </div>
      )}

      {/* Botones de acción */}
      <div className="flex w-full justify-end items-center gap-4 p-7.5">
        <button
          onClick={restablecerValores}
          className="group bg-[#FFFF] text-[#1F263E] p-2 rounded-lg border-[#E4E4E4] border-2 gap-2 flex items-center justify-center hover:bg-[#1F263E] hover:text-[#FFFF] cursor-pointer hover:border-[#1F263E] h-[2.5rem]">
          <IconoReestablecer size={"1.5rem"} className="text-[#1F263E] group-hover:text-[#FFFF]" /> Restablecer Valores
        </button>
        <button
          onClick={guardarSimulacion}
          disabled={guardandoSimulacion || !idEstadoFinancieroActual}
          className={`group p-2 rounded-lg border-2 gap-2 flex items-center justify-center h-[2.5rem] transition-colors ${
            guardandoSimulacion || !idEstadoFinancieroActual
              ? 'bg-gray-400 text-gray-600 border-gray-400 cursor-not-allowed'
              : simulacionGuardada
              ? 'bg-green-600 text-white border-green-600 hover:bg-green-700'
              : 'bg-[#1F263E] text-[#FFFF] border-[#1F263E] hover:bg-[#1F263E] hover:text-[#FFFF] cursor-pointer hover:border-[#1F263E]'
          }`}>
          {guardandoSimulacion ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Guardando...
            </>
          ) : simulacionGuardada ? (
            <>
              ✓ Simulación Guardada
            </>
          ) : (
            'Guardar Simulación'
          )}
        </button>
      </div>
     </div>
  );
};

export default Simulaciones;
