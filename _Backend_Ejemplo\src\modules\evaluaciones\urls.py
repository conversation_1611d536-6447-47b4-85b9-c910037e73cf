from django.urls import path, include
from rest_framework.routers import DefaultRouter

from src.modules.evaluaciones.views import EvaluacionView


router = DefaultRouter()
router.register("", EvaluacionView, basename="evaluaciones")

urlpatterns = [
    path("", include(router.urls)),
    path("<int:id_evaluacion>/estado/update/", EvaluacionView.as_view({"patch": "cambiar_estado_evaluacion"})),
    path("<int:id_evaluacion>/reporte/controles/", EvaluacionView.as_view({"get": "reporte_controles"})),
    path("<int:id_evaluacion>/resultados/", EvaluacionView.as_view({"get": "lista_resultados"})),
    path("<int:id_evaluacion>/resultados/subdominio/<int:id_subdominio>/", EvaluacionView.as_view({"get": "lista_resultados_by_subdominio"})),
    path("<int:id_evaluacion>/resultados/control/promedio/", EvaluacionView.as_view({"get": "lista_resultados_control_promedio"})),
    path("<int:id_evaluacion>/resultados/control/promedio/subdominio/<int:id_subdominio>/", EvaluacionView.as_view({"get": "lista_resultados_control_promedio_by_subdominio"})),
    path("<int:id_evaluacion>/avance/tobe/", EvaluacionView.as_view({"get": "avance_tobe_by_evaluacion"})),
    path("<int:id_evaluacion>/avance/tobe/evaluado/<int:id_evaluado>/", EvaluacionView.as_view({"get": "avance_tobe_by_evaluado"})),
    path("<int:id_evaluacion>/avance/asis/", EvaluacionView.as_view({"get": "avance_asis_by_evaluacion"})),
    path("<int:id_evaluacion>/avance/asis/evaluado/<int:id_evaluado>/", EvaluacionView.as_view({"get": "avance_asis_by_evaluado"})),
    path("<int:id_evaluacion>/resultado/promedio/", EvaluacionView.as_view({"get": "resultado_promedio_by_evaluacion"})),
    path("evaluado/<int:id_evaluado>/", EvaluacionView.as_view({"get": "evaluacion_by_evaluado"})),
    path("usuario/<int:id_usuario>/", EvaluacionView.as_view({"get": "evaluacion_by_usuario"})),
]
