from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from src.modules.plantillas.controller import PlantillaController
from rest_framework.decorators import action
from drf_yasg import openapi


class DescargarPlantillaView(viewsets.ViewSet):
    controller = None

    @swagger_auto_schema(
        operation_summary="Descarga la plantilla de EEFF",
        responses={
            200: "Plantilla descargada",
            400: "Error al descargar la plantilla",
        },
    )
    @action(detail=False, methods=["get"], url_path="descargar")
    def descargar_plantilla(self, request, *args, **kwargs):
        try:
            self.controller = PlantillaController()
            fileResponse = self.controller.descargar_plantilla()
            if not fileResponse:
                return Response(
                    "Error al descargar la plantilla",
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return fileResponse
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)

   