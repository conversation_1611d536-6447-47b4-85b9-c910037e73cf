from src.utils.classes import Response
from src.modules.ratios_ef.models import RatioEF
from src.modules.ratios_ef.serializers import RatioEFSerializer, RatioEFSimpleSerializer
from django.core.exceptions import ObjectDoesNotExist
from src.modules.estado_financiero.models import EstadoFinanciero
from src.modules.ratios.models import Ratio
from datetime import datetime, timedelta
from decimal import Decimal

class RatiosEFController:
    def __init__(self):
        pass

    def obtener_ratios_por_empresa_periodo(self, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene todos los ratios de una empresa para un periodo específico.

        Args:
            id_empresa: ID de la empresa
            tipo_periodo: Tipo de periodo (1: anual, 2: trimestral, 3: mensual, etc.)
            fecha_fin: Fecha de fin del periodo (formato DD-MM-YYYY)

        Returns:
            Response: Objeto de respuesta con los ratios encontrados
        """
        try:
            # Convertir la fecha del formato DD-MM-YYYY al formato YYYY-MM-DD
            try:
                # Parsear la fecha en formato DD-MM-YYYY
                fecha_obj = datetime.strptime(fecha_fin, '%d-%m-%Y')
                # Convertir al formato YYYY-MM-DD para la consulta en la base de datos
                fecha_fin_formateada = fecha_obj.strftime('%Y-%m-%d')
            except ValueError:
                return Response(
                    message="Formato de fecha inválido. Debe ser DD-MM-YYYY",
                    state=False
                )

            # Buscar estados financieros que coincidan con los criterios
            estados_financieros = EstadoFinanciero.objects.filter(
                int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                int_idEmpresa_id=id_empresa,
                int_tipoPeriodo=tipo_periodo,
                dt_fechaFinPeriodo=fecha_fin_formateada
            )

            if not estados_financieros.exists():
                return Response(
                    message="No se encontraron estados financieros para los criterios especificados",
                    state=False
                )

            # Lista para almacenar los ratios
            ratios_list = []

            # Para cada estado financiero, obtener sus ratios
            for ef in estados_financieros:
                ratios_ef = RatioEF.objects.filter(int_idEstadoFinanciero=ef)

                for ratio_ef in ratios_ef:
                    # Obtener el nombre y la fórmula del ratio
                    nombre_ratio = ratio_ef.int_idRatios.str_descripcion
                    formula_ratio = ratio_ef.int_idRatios.str_formula
                    valor_ratio = ratio_ef.db_valorRatio

                    # Verificar si el ratio ya está en la lista (para evitar duplicados)
                    ratio_existente = next(
                        (r for r in ratios_list if r['nombre_ratio'] == nombre_ratio),
                        None
                    )

                    if not ratio_existente:
                        # Calcular la variación respecto al periodo anterior
                        porcentaje_variacion = None
                        try:
                            # Buscar el ratio en el periodo anterior (primero tipo 1, luego tipo 2)
                            ratio_anterior = self._buscar_ratio_periodo_anterior(
                                ratio_ef.int_idRatios,
                                tipo_periodo,
                                id_empresa,
                                ef.dt_fechaFinPeriodo
                            )

                            if ratio_anterior:
                                # Calcular la variación porcentual
                                if ratio_anterior.db_valorRatio != 0:
                                    variacion_abs = (valor_ratio - ratio_anterior.db_valorRatio)
                                    variacion = variacion_abs / abs(ratio_anterior.db_valorRatio)
                                    variacion = variacion * 100

                                    # Formatear a 2 decimales y agregar el signo
                                    signo = "+" if variacion > 0 else ""
                                    porcentaje_variacion = f"{signo}{variacion:.2f}%"
                                else:
                                    porcentaje_variacion = None
                        except Exception:
                            porcentaje_variacion = None

                        ratios_list.append({
                            'nombre_ratio': nombre_ratio,
                            'valor_ratio': float(valor_ratio),
                            'formula_ratio': formula_ratio,
                            'variacion': porcentaje_variacion
                        })

            if not ratios_list:
                return Response(
                    message="No se encontraron ratios para los estados financieros",
                    state=False
                )

            # Serializar los datos
            serializer = RatioEFSimpleSerializer(ratios_list, many=True)

            return Response(
                message="Ratios obtenidos exitosamente",
                data=serializer.data,
                state=True
            )

        except Exception as e:
            return Response(
                message=f"Error al obtener ratios: {str(e)}",
                state=False
            )

    def obtener_ratios_simulacion(self, id_estado_financiero):
        """
        Obtiene los ratios financieros para un estado financiero de simulación específico.

        Args:
            id_estado_financiero (int): ID del estado financiero de simulación

        Returns:
            Response: Objeto de respuesta con los ratios encontrados para la simulación
        """
        try:
            # Verificar que el estado financiero existe
            try:
                estado_financiero = EstadoFinanciero.objects.get(int_idEstadoFinanciero=id_estado_financiero)
            except ObjectDoesNotExist:
                return Response(
                    message=f"No se encontró el estado financiero con ID {id_estado_financiero}",
                    state=False
                )

            # Verificar que sea una simulación
            if estado_financiero.int_tipoRegistro != 1:
                return Response(
                    message=f"El estado financiero con ID {id_estado_financiero} no es una simulación",
                    state=False
                )

            # Obtener los ratios para este estado financiero
            ratios_ef = RatioEF.objects.filter(int_idEstadoFinanciero=estado_financiero)

            if not ratios_ef.exists():
                return Response(
                    message=f"No se encontraron ratios para el estado financiero con ID {id_estado_financiero}",
                    state=False
                )

            # Lista para almacenar los ratios
            ratios_list = []

            # Procesar cada ratio
            for ratio_ef in ratios_ef:
                # Obtener el nombre y la fórmula del ratio
                nombre_ratio = ratio_ef.int_idRatios.str_descripcion
                formula_ratio = ratio_ef.int_idRatios.str_formula
                valor_ratio = ratio_ef.db_valorRatio

                # Agregar el ratio a la lista
                ratios_list.append({
                    'nombre_ratio': nombre_ratio,
                    'valor_ratio': float(valor_ratio),
                    'formula_ratio': formula_ratio
                })

            # Serializar los datos
            serializer = RatioEFSimpleSerializer(ratios_list, many=True)

            return Response(
                message="Ratios de la simulación obtenidos exitosamente",
                data=serializer.data,
                state=True
            )

        except Exception as e:
            return Response(
                message=f"Error al obtener ratios de la simulación: {str(e)}",
                state=False
            )

    def obtener_ratios_ultimos_periodos(self, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene los ratios financieros de los últimos 12 periodos para una empresa específica.

        Args:
            id_empresa (int): ID de la empresa
            tipo_periodo (int): ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
            fecha_fin (str): Fecha de fin del periodo en formato DD-MM-YYYY

        Returns:
            Response: Objeto de respuesta con los ratios encontrados para los últimos periodos
        """
        try:
            # Convertir la fecha del formato DD-MM-YYYY al formato YYYY-MM-DD
            try:
                # Parsear la fecha en formato DD-MM-YYYY
                fecha_obj = datetime.strptime(fecha_fin, '%d-%m-%Y')
                # Convertir al formato YYYY-MM-DD para la consulta en la base de datos
                fecha_fin_formateada = fecha_obj.strftime('%Y-%m-%d')
            except ValueError:
                return Response(
                    message="Formato de fecha inválido. Debe ser DD-MM-YYYY",
                    state=False
                )

            # Obtener las fechas únicas de los últimos 12 periodos (incluyendo el actual)
            fechas_periodos = EstadoFinanciero.objects.filter(
                int_idEmpresa_id=id_empresa,
                int_tipoPeriodo=tipo_periodo,
                int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                dt_fechaFinPeriodo__lte=fecha_fin_formateada  # Fecha igual o anterior a la actual
            ).values_list('dt_fechaFinPeriodo', flat=True).distinct().order_by('-dt_fechaFinPeriodo')[:12]  # Limitar a los últimos 12 periodos

            if not fechas_periodos:
                return Response(
                    message=f"No se encontraron estados financieros para la empresa con ID {id_empresa} y tipo de periodo {tipo_periodo}",
                    state=False
                )

            resultado = []

            # Procesar cada periodo (fecha única)
            for fecha_periodo in fechas_periodos:
                # Formatear la fecha de fin para mostrar en el resultado
                fecha_fin_formateada_str = fecha_periodo.strftime('%d/%m/%Y')

                # Buscar todos los estados financieros para este periodo
                estados_financieros_periodo = EstadoFinanciero.objects.filter(
                    int_idEmpresa_id=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                    dt_fechaFinPeriodo=fecha_periodo
                )

                # Lista para almacenar los ratios de este periodo
                ratios_periodo = []

                # Para cada estado financiero, obtener sus ratios
                for ef in estados_financieros_periodo:
                    ratios_ef = RatioEF.objects.filter(int_idEstadoFinanciero=ef)

                    for ratio_ef in ratios_ef:
                        if ratio_ef.int_idRatios.str_descripcion in ["ROE", "ROA", "Margen Neto", "Rotación", "Apalancamiento"]:
                            # Obtener el nombre y la fórmula del ratio
                            nombre_ratio = ratio_ef.int_idRatios.str_descripcion
                            formula_ratio = ratio_ef.int_idRatios.str_formula
                            valor_ratio = ratio_ef.db_valorRatio

                            # Verificar si el ratio ya está en la lista (para evitar duplicados)
                            ratio_existente = next(
                                (r for r in ratios_periodo if r['nombre_ratio'] == nombre_ratio),
                                None
                            )

                            if not ratio_existente:
                                # Agregar el ratio a la lista de este periodo
                                ratios_periodo.append({
                                    'nombre_ratio': nombre_ratio,
                                    'valor_ratio': float(valor_ratio),
                                    'formula_ratio': formula_ratio
                                })

                # Si se encontraron ratios para este periodo, agregarlo al resultado
                if ratios_periodo:
                    periodo_data = {
                        "fecha_fin": fecha_fin_formateada_str,
                        "ratios": ratios_periodo
                    }
                    resultado.append(periodo_data)

            if not resultado:
                return Response(
                    message="No se encontraron ratios para los periodos especificados",
                    state=False
                )

            return Response(
                message="Ratios de los últimos periodos obtenidos exitosamente",
                data=resultado,
                state=True
            )

        except Exception as e:
            return Response(
                message=f"Error al obtener ratios de los últimos periodos: {str(e)}",
                state=False
            )

    def _obtener_estado_financiero_anterior(self, estado_financiero: EstadoFinanciero, tipo_periodo, id_empresa):
        """
        Obtiene el estado financiero del periodo anterior según el tipo de periodo.
        Por ejemplo, si es mensual, obtiene el mes anterior; si es anual, obtiene el año anterior.

        Args:
            estado_financiero (EstadoFinanciero): Estado financiero actual
            tipo_periodo (int): ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
            id_empresa (int): ID de la empresa

        Returns:
            EstadoFinanciero: Estado financiero del periodo anterior o None si no existe
        """
        try:
            fecha_actual = estado_financiero.dt_fechaFinPeriodo

            # Determinar la fecha del periodo anterior según el tipo de periodo
            if tipo_periodo == 1:  # Anual
                # Para periodos anuales, buscar el año anterior
                año_anterior = fecha_actual.year - 1
                # Filtrar por el año anterior manteniendo el mismo mes y día
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                    dt_fechaFinPeriodo__year=año_anterior,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero  # Mismo tipo de estado financiero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 2:  # Trimestral
                # Para periodos trimestrales, buscar el trimestre anterior
                # Determinar el trimestre actual (1-4)
                trimestre_actual = (fecha_actual.month - 1) // 3 + 1
                año = fecha_actual.year

                if trimestre_actual == 1:
                    # Si es el primer trimestre, el anterior es el cuarto del año pasado
                    trimestre_anterior = 4
                    año = año - 1
                else:
                    # Si no, es el trimestre anterior del mismo año
                    trimestre_anterior = trimestre_actual - 1

                # Calcular el rango de meses del trimestre anterior
                mes_inicio = (trimestre_anterior - 1) * 3 + 1
                mes_fin = trimestre_anterior * 3

                # Filtrar por el trimestre anterior
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=año,
                    dt_fechaFinPeriodo__month__gte=mes_inicio,
                    dt_fechaFinPeriodo__month__lte=mes_fin,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 3:  # Mensual
                # Para periodos mensuales, buscar el mes anterior
                if fecha_actual.month == 1:
                    # Si es enero, el mes anterior es diciembre del año pasado
                    mes_anterior = 12
                    año_anterior = fecha_actual.year - 1
                else:
                    # Si no, es el mes anterior del mismo año
                    mes_anterior = fecha_actual.month - 1
                    año_anterior = fecha_actual.year

                # Filtrar por el mes anterior
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=año_anterior,
                    dt_fechaFinPeriodo__month=mes_anterior,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 4:  # Semanal
                # Para periodos semanales, buscar la semana anterior
                # Restar 7 días a la fecha actual
                fecha_anterior = fecha_actual - timedelta(days=7)

                # Filtrar por la semana anterior (aproximadamente)
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=fecha_anterior.year,
                    dt_fechaFinPeriodo__month=fecha_anterior.month,
                    dt_fechaFinPeriodo__day__gte=max(1, fecha_anterior.day - 3),  # Aproximación
                    dt_fechaFinPeriodo__day__lte=min(31, fecha_anterior.day + 3),  # Aproximación
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 5:  # Diario
                # Para periodos diarios, buscar el día anterior
                fecha_anterior = fecha_actual - timedelta(days=1)

                # Filtrar por el día anterior
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo=fecha_anterior,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            else:
                # Para cualquier otro tipo de periodo, usar el método original
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__lt=fecha_actual,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            # Retornar el primer resultado si existe
            if efs_anteriores.exists():
                return efs_anteriores.first()
            return None

        except Exception as e:
            print(f"Error al obtener estado financiero anterior: {str(e)}")
            return None

    def _buscar_ratio_periodo_anterior(self, ratio_id, tipo_periodo, id_empresa, fecha_actual):
        """
        Busca un ratio específico en el periodo anterior.
        Primero busca en estados financieros tipo 1, si no encuentra busca en tipo 2.

        Args:
            ratio_id: ID del ratio a buscar
            tipo_periodo (int): ID del Tipo de periodo
            id_empresa (int): ID de la empresa
            fecha_actual: Fecha del periodo actual

        Returns:
            RatioEF: Ratio encontrado en el periodo anterior o None si no existe
        """
        try:
            # Primero buscar en estados financieros tipo 1 (Balance General)
            ef_anterior_tipo1 = self._obtener_estado_financiero_anterior_por_tipo(
                fecha_actual, tipo_periodo, id_empresa, tipo_ef=1
            )

            if ef_anterior_tipo1:
                try:
                    ratio_anterior = RatioEF.objects.get(
                        int_idEstadoFinanciero=ef_anterior_tipo1,
                        int_idRatios=ratio_id
                    )
                    return ratio_anterior
                except RatioEF.DoesNotExist:
                    pass

            # Si no se encontró en tipo 1, buscar en tipo 2 (Estado de Resultados)
            ef_anterior_tipo2 = self._obtener_estado_financiero_anterior_por_tipo(
                fecha_actual, tipo_periodo, id_empresa, tipo_ef=2
            )

            if ef_anterior_tipo2:
                try:
                    ratio_anterior = RatioEF.objects.get(
                        int_idEstadoFinanciero=ef_anterior_tipo2,
                        int_idRatios=ratio_id
                    )
                    return ratio_anterior
                except RatioEF.DoesNotExist:
                    pass

            return None

        except Exception as e:
            print(f"Error al buscar ratio en periodo anterior: {str(e)}")
            return None

    def _obtener_estado_financiero_anterior_por_tipo(self, fecha_actual, tipo_periodo, id_empresa, tipo_ef):
        """
        Obtiene el estado financiero del periodo anterior para un tipo específico de estado financiero.

        Args:
            fecha_actual: Fecha del periodo actual
            tipo_periodo (int): ID del Tipo de periodo
            id_empresa (int): ID de la empresa
            tipo_ef (int): Tipo de estado financiero (1: Balance, 2: Estado de Resultados)

        Returns:
            EstadoFinanciero: Estado financiero del periodo anterior o None si no existe
        """
        try:
            # Determinar la fecha del periodo anterior según el tipo de periodo
            if tipo_periodo == 1:  # Anual
                año_anterior = fecha_actual.year - 1
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=año_anterior,
                    int_tipoEstadoFinanciero=tipo_ef
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 2:  # Trimestral
                trimestre_actual = (fecha_actual.month - 1) // 3 + 1
                año = fecha_actual.year

                if trimestre_actual == 1:
                    trimestre_anterior = 4
                    año = año - 1
                else:
                    trimestre_anterior = trimestre_actual - 1

                mes_inicio = (trimestre_anterior - 1) * 3 + 1
                mes_fin = trimestre_anterior * 3

                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=año,
                    dt_fechaFinPeriodo__month__gte=mes_inicio,
                    dt_fechaFinPeriodo__month__lte=mes_fin,
                    int_tipoEstadoFinanciero=tipo_ef
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 3:  # Mensual
                if fecha_actual.month == 1:
                    mes_anterior = 12
                    año_anterior = fecha_actual.year - 1
                else:
                    mes_anterior = fecha_actual.month - 1
                    año_anterior = fecha_actual.year

                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=año_anterior,
                    dt_fechaFinPeriodo__month=mes_anterior,
                    int_tipoEstadoFinanciero=tipo_ef
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 4:  # Semanal
                fecha_anterior = fecha_actual - timedelta(days=7)
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=fecha_anterior.year,
                    dt_fechaFinPeriodo__month=fecha_anterior.month,
                    dt_fechaFinPeriodo__day__gte=max(1, fecha_anterior.day - 3),
                    dt_fechaFinPeriodo__day__lte=min(31, fecha_anterior.day + 3),
                    int_tipoEstadoFinanciero=tipo_ef
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 5:  # Diario
                fecha_anterior = fecha_actual - timedelta(days=1)
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo=fecha_anterior,
                    int_tipoEstadoFinanciero=tipo_ef
                ).order_by('-dt_fechaFinPeriodo')

            else:
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__lt=fecha_actual,
                    int_tipoEstadoFinanciero=tipo_ef
                ).order_by('-dt_fechaFinPeriodo')

            if efs_anteriores.exists():
                return efs_anteriores.first()
            return None

        except Exception as e:
            print(f"Error al obtener estado financiero anterior por tipo: {str(e)}")
            return None