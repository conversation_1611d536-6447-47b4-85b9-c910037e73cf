from django.http import FileResponse
from rest_framework import status
from rest_framework.response import Response
from src.utils.classes import Response as ResponseAPI
from src.modules.gestor_archivos.controller import GestorArchivosController
from src.modules.pasos.models import Paso
from .models import Adjunto
import os

class AdjuntoController:
    def __init__(self, archivo=None, paso_id=None, *args, **kwargs):
        self.ruta_archivos = r'src\assets\framework'
        
        self.archivo = archivo
        self.nombre_archivo_original = self.archivo.name
        self.nombre_archivo_base = os.path.splitext(self.nombre_archivo_original)[0]
        self.extension_archivo = os.path.splitext(self.nombre_archivo_original)[1]

        self.paso_id = paso_id
        self.framework_id = Paso.objects.filter(int_idPaso=paso_id).first().int_idNivel.int_idControl.int_idSubDominio.int_idDominio.int_idFramework.int_idFramework
        self.control_id = Paso.objects.filter(int_idPaso=paso_id).first().int_idNivel.int_idControl.int_idControl
        self.adjuntos_folder = os.path.join(
            self.ruta_archivos, f'{self.framework_id}','Controles', f'{self.control_id}'
        )

    def registrar_adjunto_paso(self):
        """
        Guarda el archivo en la carpeta correspondiente y registra el archivo en la base de datos.
        """
        try:
            # 1. Guarda el archivo en la carpeta correspondiente
            gestorArchivo = GestorArchivosController() 
            response = gestorArchivo.save_docs(
                self.adjuntos_folder,    
                self.archivo,
                self.nombre_archivo_original, 
                self.nombre_archivo_base, 
                self.extension_archivo
                )

            # 2. Registra el archivo en la base de datos
            Adjunto(
                str_nombre=self.nombre_archivo_base,
                str_peso=self.archivo.size,
                str_ruta=response.data,
                str_extension=self.extension_archivo,
                int_idPaso=Paso.objects.filter(
                    int_idPaso=self.paso_id
                    ).first(),
            ).save()

            # 3. Retorna la respuesta
            return ResponseAPI("Guardado correctamente", None, state=True)
        except Exception as e:
            return ResponseAPI(str(e), e, state=False)
    

class AdjuntoDownloadController:
    def __init__(self, *args, **kwargs):
        ...

    def get_docs(self, path):
        if not os.path.exists(path):
            return ResponseAPI("No se encontró el archivo", None, state=False)

        nombre_archivo = os.path.basename(path)

        archivo_response = FileResponse(open(path, "rb"), as_attachment=True)
        archivo_response["Content-Disposition"] = f'attachment; filename="{nombre_archivo}"'
        archivo_response["Content-Type"] = "application/octet-stream"

        return archivo_response
    
class AdjuntoDeleteController:
    def __init__(self, *args, **kwargs):
        ...

    def delete_adjunto(self, adjunto_id):
        adjunto = Adjunto.objects.filter(
            int_idAdjunto=adjunto_id
        ).first()  # Assume `id` is the primary key field name

        if not adjunto:
            return ResponseAPI("Adjunto no encontrado.", None, state=False)

        ruta_adjunto = adjunto.str_ruta
        if ruta_adjunto:
            try:
                print(os.path.join(ruta_adjunto))
                os.remove(os.path.join(ruta_adjunto))
                print(f"Archivo eliminado: {ruta_adjunto}")
            except FileNotFoundError:
                print(f"Archivo no encontrado en el sistema de archivos: {ruta_adjunto}")
                adjunto.delete()
                return ResponseAPI(f"Archivo no encontrado en el sistema de archivos: {ruta_adjunto}", None, state=True)
            except Exception as e:
                print(f"Error al eliminar el archivo: {e}")
                return ResponseAPI("Error al eliminar el archivo.", None, state=False)

        adjunto.delete()
        return ResponseAPI("Archivo eliminado con éxito.", None, state=True)
    
