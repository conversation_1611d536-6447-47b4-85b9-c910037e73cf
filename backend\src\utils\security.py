import jwt
from datetime import datetime, timedelta
from configparser import ConfigParser

# TODO: Buscar una nueva implementación de UTCNOW para datetime


class JWT:
    def __init__(self) -> None:
        self.secret_key = None
        self.token = None
        self.alghoritm = "HS256"
        self.default_expire_time_hours = 8
        self.default_refresh_expire_time_days = 7
        self.default_transfer_expire_time_secs = 10
        self.default_expire_time_minutes = 1

    def __get_config(self) -> ConfigParser:
        __config_path = "config.ini"
        config = ConfigParser()
        config.read(__config_path)
        return config

    def get_secret_key(self) -> str:
        config = self.__get_config()
        return config["JWT"]["SECRET_KEY"]

    def get_token(self, payload: dict = {}) -> str:

        self.secret_key = self.get_secret_key()
        expire_at = datetime.utcnow() + timedelta(hours=self.default_expire_time_hours)
        payload["exp"] = expire_at
        token = jwt.encode(
            payload=payload,
            key=self.secret_key,
            algorithm=self.alghoritm,
        )
        print(token)
        return token

    def get_transfer_token(self, payload: dict = {}) -> str:

        self.secret_key = self.get_secret_key()
        expire_at = datetime.utcnow() + timedelta(
            seconds=self.default_transfer_expire_time_secs
        )
        payload["exp"] = expire_at

        token = jwt.encode(
            payload=payload,
            key=self.secret_key,
            algorithm=self.alghoritm,
        )
        
        return token

    def get_refresh_token(self, payload: dict = {}) -> str:

        self.secret_key = self.get_secret_key()
        expire_at = datetime.utcnow() + timedelta(
            days=self.default_refresh_expire_time_days
        )
        payload["exp"] = expire_at

        token = jwt.encode(
            payload=payload,
            key=self.secret_key,
            algorithm=self.alghoritm,
        )
        print(token)
        return token

    def decode_token(self, token: str):
        try:
            self.secret_key = self.get_secret_key()
            payload = jwt.decode(token, self.secret_key, algorithms=[self.alghoritm])
            return payload
        except jwt.ExpiredSignatureError:
            return {"error": "Token has expired"}
        except jwt.InvalidTokenError:
            return {"error": "Invalid token"}

    def refresh_token(self, token: str):
        try:
            payload = self.decode_token(token)
            if "error" not in payload:
                expire_at = datetime.utcnow() + timedelta(
                    days=self.default_refresh_expire_time_days
                )
                del payload["exp"]
                payload["exp"] = expire_at
                return self.get_token(payload)
            else:
                return None
        except Exception as e:
            return str(e)
