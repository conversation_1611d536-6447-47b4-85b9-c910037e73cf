import React from "react";

const IconoAdenda = ({ color, size }) => {
  return (
    <svg
      height={size}
      version="1.1"
      viewBox="0 0 846.66 846.66"
      width={size}
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <style type="text/css"></style>
      </defs>
      <g id="Layer_x0020_1">
        <path
          d="M123.75 122.22l57.84 0 0 -32.26c0,-44.59 36.4,-80.99 80.99,-80.99l460.33 0c44.59,0 80.99,36.4 80.99,80.99l0 553.49c0,44.59 -36.41,80.98 -80.99,80.98l-57.84 0 0 32.27c0,44.59 -36.4,80.99 -80.99,80.99l-460.33 0c-44.59,0 -80.99,-36.4 -80.99,-80.99l0 -553.49c0,-44.59 36.4,-80.99 80.99,-80.99zm99.26 0l361.07 0c44.59,0 80.99,36.4 80.99,80.99l0 479.8 57.84 0c21.71,0 39.56,-17.85 39.56,-39.56l0 -553.49c0,-21.72 -17.84,-39.57 -39.56,-39.57l-460.33 0c-21.72,0 -39.57,17.85 -39.57,39.57l0 32.26zm140.49 193.5c-27.24,0 -27.24,-41.42 0,-41.42l165.32 0c27.24,0 27.24,41.42 0,41.42l-165.32 0zm-174.9 383.59c-27.24,0 -27.24,-41.42 0,-41.42l330.63 0c27.24,0 27.24,41.42 0,41.42l-330.63 0zm0 -228.1c-27.24,0 -27.24,-41.42 0,-41.42l330.63 0c27.24,0 27.24,41.42 0,41.42l-330.63 0zm0 76.03c-27.24,0 -27.24,-41.42 0,-41.42l330.63 0c27.24,0 27.24,41.42 0,41.42l-330.63 0zm0 76.04c-27.24,0 -27.24,-41.42 0,-41.42l330.63 0c27.24,0 27.24,41.42 0,41.42l-330.63 0zm174.9 -241.8c-27.24,0 -27.24,-41.43 0,-41.43l165.32 0c27.24,0 27.24,41.43 0,41.43l-165.32 0zm-184.49 -120.88l85.86 0c11.43,0 20.71,9.27 20.71,20.71l0 93.16c0,11.43 -9.28,20.71 -20.71,20.71l-85.86 0c-11.44,0 -20.71,-9.28 -20.71,-20.71l0 -93.16c0,-11.44 9.27,-20.71 20.71,-20.71zm65.15 41.42l-44.44 0 0 51.73 44.44 0 0 -51.73zm339.92 -138.38l-460.33 0c-21.72,0 -39.56,17.85 -39.56,39.57l0 553.49c0,21.72 17.84,39.57 39.56,39.57l460.33 0c21.72,0 39.57,-17.85 39.57,-39.57l0 -553.49c0,-21.72 -17.85,-39.57 -39.57,-39.57z" fill={color}
        />
      </g>
    </svg>
  );
};

export default IconoAdenda;
