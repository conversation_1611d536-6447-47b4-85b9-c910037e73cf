import React from "react";

const IconoValorMinimo = ({size,active}) => {
  return (
    <svg viewBox="0 0 64 64" width={size} height={size} xmlns="http://www.w3.org/2000/svg">
      <g data-name="27 receipt" id="_27_receipt">
        <path d="M57.46,4.05H6.54A3.037,3.037,0,0,0,3.5,7.08V17.27A3.037,3.037,0,0,0,6.54,20.3h5.11V58.01a1,1,0,0,0,.68.95,1.023,1.023,0,0,0,1.12-.35,14.657,14.657,0,0,1,6.35-4.85,14.186,14.186,0,0,1,7.78-.74,14.873,14.873,0,0,1,3.99,1.33,17.809,17.809,0,0,1,2.39,1.53c.***********,1.14.8a17.055,17.055,0,0,0,8.58,3.23c.53.03,1.06.05,1.61.05,1.08,0,2.22-.06,3.45-.17a12.37,12.37,0,0,0,2.12-.32c.99-.26,1.49-.75,1.49-1.46V20.3h5.11a3.037,3.037,0,0,0,3.04-3.03V7.08A3.037,3.037,0,0,0,57.46,4.05ZM11.65,14.23H9.57V10.12h2.08Zm38.7,43.3c-.02.01-.03.01-.05.02a10.143,10.143,0,0,1-1.73.25,34.3,34.3,0,0,1-4.78.12,15.237,15.237,0,0,1-7.58-2.9c-.37-.25-.73-.5-1.08-.76a19.293,19.293,0,0,0-2.67-1.7,16.483,16.483,0,0,0-4.52-1.51,16.258,16.258,0,0,0-8.88.85,16.52,16.52,0,0,0-5.41,3.48V10.12h36.7ZM58.5,17.27a1.037,1.037,0,0,1-1.04,1.03H52.35V16.23h3.08a1,1,0,0,0,1-1V9.12a1,1,0,0,0-1-1H8.57a1,1,0,0,0-1,1v6.11a1,1,0,0,0,1,1h3.08V18.3H6.54A1.037,1.037,0,0,1,5.5,17.27V7.08A1.037,1.037,0,0,1,6.54,6.05H57.46A1.037,1.037,0,0,1,58.5,7.08Zm-6.15-3.04V10.12h2.08v4.11Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"} />
        <path d="M24.85,14.21a1,1,0,0,1-1,1H17.74a1,1,0,0,1,0-2h6.11A1,1,0,0,1,24.85,14.21Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
        <path d="M36.06,14.21a1,1,0,0,1-1,1H28.94a1,1,0,0,1,0-2h6.12A1,1,0,0,1,36.06,14.21Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
        <path d="M47.26,14.21a1,1,0,0,1-1,1H40.15a1,1,0,0,1,0-2h6.11A1,1,0,0,1,47.26,14.21Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
        <path d="M35.06,25.98A2.785,2.785,0,0,1,33,27.91v.54a1,1,0,1,1-2,0v-.7a7.81,7.81,0,0,1-1.29-.68c-.16-.1-.32-.2-.47-.28a1,1,0,0,1,.97-1.75c.18.1.37.21.56.33a3,3,0,0,0,1.53.64.89.89,0,0,0,.83-.55c.03-.12.03-.27-.21-.43l-2.45-1.64a2.494,2.494,0,0,1-1.1-1.67,2.3,2.3,0,0,1,.5-1.81A2.731,2.731,0,0,1,31,19.08v-.77a1,1,0,0,1,2,0v.67a2.81,2.81,0,0,1,.85.38l.98.65a1,1,0,0,1,.27,1.39.987.987,0,0,1-1.38.27l-.98-.65a1.06,1.06,0,0,0-1.31.13.321.321,0,0,0-.08.27.51.51,0,0,0,.23.31l2.46,1.64A2.345,2.345,0,0,1,35.06,25.98Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
        <path d="M36.06,44.77a1,1,0,0,1-1,1H17.74a1,1,0,0,1,0-2H35.06A1,1,0,0,1,36.06,44.77Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
        <path d="M47.26,32.55a1,1,0,0,1-1,1H40.15a1,1,0,1,1,0-2h6.11A1,1,0,0,1,47.26,32.55Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
        <path d="M47.26,36.62a1,1,0,0,1-1,1H40.15a1,1,0,0,1,0-2h6.11A1,1,0,0,1,47.26,36.62Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
        <path d="M47.26,40.69a1,1,0,0,1-1,1H40.15a1,1,0,0,1,0-2h6.11A1,1,0,0,1,47.26,40.69Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
        <path d="M47.26,44.77a1,1,0,0,1-1,1H40.15a1,1,0,0,1,0-2h6.11A1,1,0,0,1,47.26,44.77Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
        <path d="M36.06,40.69a1,1,0,0,1-1,1H17.74a1,1,0,1,1,0-2H35.06A1,1,0,0,1,36.06,40.69Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
        <path d="M36.06,36.62a1,1,0,0,1-1,1H17.74a1,1,0,0,1,0-2H35.06A1,1,0,0,1,36.06,36.62Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
        <path d="M36.06,32.55a1,1,0,0,1-1,1H17.74a1,1,0,1,1,0-2H35.06A1,1,0,0,1,36.06,32.55Z" fill={active ? "#FFF" : "rgba(23, 39, 71, 0.95)"}/>
      </g>
    </svg>
  );
};

export default IconoValorMinimo;
