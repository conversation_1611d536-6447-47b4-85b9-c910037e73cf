# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('frameworks', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Dominio',
            fields=[
                ('int_idDominio', models.AutoField(primary_key=True, serialize=False)),
                ('str_nombre', models.CharField(max_length=255)),
                ('int_idFramework', models.ForeignKey(db_column='int_idFramework', on_delete=django.db.models.deletion.CASCADE, related_name='dominios', to='frameworks.framework')),
            ],
            options={
                'verbose_name': 'dominio',
                'verbose_name_plural': 'dominios',
                'db_table': 'tr_dominio',
                'managed': True,
            },
        ),
    ]
