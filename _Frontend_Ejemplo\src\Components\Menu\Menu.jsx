import React from "react";
import FotoPerfil from "../../assets/avatars/300-13.jpg";
import IconoLista from "../../assets/SVG/IconoLista.tsx";
import IconoConfiguracion from "../../assets/SVG/IconoConfiguracion.tsx";
import IconoLogout from "../../assets/SVG/IconoLogout.tsx";

import { useLocation, useNavigate } from "react-router-dom";
import { RoutesPrivate } from "../../Routes/ProtectedRoute.tsx";
import IconoAdministracion from "../../assets/SVG/IconoAdministracion.tsx";
import IconoProyectos from "../../assets/SVG/IconoProyectos.jsx";
const Menu = ({ perfil }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { item } = location.state || {};
  const ruta = location.pathname;

  const menuColor = perfil === "Usuario" ? "#47D691" : "#156CFF";

  return (
    <div className=" md:w-1/13 w-2/13  shadow-md    py-20  flex  flex-col  gap-8  items-center  justify-between ">
      <div className=" flex  flex-col  gap-10  items-center  justify-start  w-full">
        {" "}
        <img
          src={FotoPerfil}
          alt=""
          className=" md:w-15 w-12  md:h-15  h-12  rounded-full"
        />
        <nav className="  flex  flex-col  gap-4  items-center  justify-start">
          <a
            href=""
            className={` text-center  text-xl  font-semibold   md:p-5 p-3  rounded-2xl  hover:text-white   hover:bg-[${menuColor}] ${
              (ruta === RoutesPrivate.INICIO ||
                ruta === RoutesPrivate.SEGUIMIENTO ||
                ruta === RoutesPrivate.TAREAS) &&
              ` bg-[${menuColor}]`
            }`}
            onClick={() =>
              navigate(RoutesPrivate.INICIO, {
                replace: true,
              })
            }
          >
            <IconoProyectos
              size="1.4rem"
              color={
                ruta === RoutesPrivate.INICIO ||
                ruta === RoutesPrivate.SEGUIMIENTO ||
                ruta === RoutesPrivate.TAREAS
                  ? "#fff"
                  : "#D9D9D9"
              }
            />
          </a>
        </nav>
      </div>
      <div className=" w-full  flex  items-center  justify-center">
        <button className="  p-3    text-white  font-semibold  rounded-lg    transition-all  cursor-pointer  text-sm  flex  items-center  justify-center  active:bg-orange-100">
          <IconoLogout size="2rem" color={menuColor} />
        </button>
      </div>
    </div>
  );
};

export default Menu;
