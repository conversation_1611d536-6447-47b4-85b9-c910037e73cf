from src.modules.evaluados.models import Evaluado
from src.modules.niveles.models import Nivel
from src.modules.evaluaciones.models import Evaluacion
from src.modules.detalles_evaluacion.models import DetalleEvaluacion
from src.modules.evidencias.models import Evidencia
from src.modules.evidencias.serializers import EvidenciaNombreSerializer
from src.utils.classes import Response
from django.db.models import Q, Avg, Count, F, Sum
from datetime import datetime

from django.http import FileResponse
import os
from src.modules.plantillas.controller import PlantillaController
from src.modules.niveles.controller import NivelController

class EvaluacionController:
    def __init__(self):
        self.nivel_controller = NivelController()



    def buscar_por_nombre(self, nombre: str):
        """
        Busca evaluaciones que contengan el texto especificado en su nombre

        Args:
            nombre (str): Texto a buscar en el nombre de las evaluaciones

        Returns:
            Response: Objeto con las evaluaciones encontradas
        """
        try:
            evaluaciones = Evaluacion.objects.filter(
                str_nombre__icontains=nombre
            ).all()

            return Response(
                message="Evaluaciones encontradas exitosamente",
                data=evaluaciones,
                state=True
            )

        except Exception as e:
            return Response(
                message=f"Error al buscar evaluaciones: {str(e)}",
                state=False
            )

    def buscar_avanzado(self, filtros: dict):
        """
        Busca evaluaciones aplicando múltiples filtros opcionales

        Args:
            filtros (dict): Diccionario con los filtros a aplicar
                - nombre (str, opcional): Nombre parcial de la evaluación
                - framework_id (int, opcional): ID del framework
                - fecha_inicio (str, opcional): Fecha de inicio en formato YYYY-MM-DD
                - estado (bool, opcional): Estado de la evaluación

        Returns:
            Response: Objeto con las evaluaciones encontradas
        """
        try:
            query = Evaluacion.objects.all()

            # Filtro por nombre
            if nombre := filtros.get('nombre'):
                query = query.filter(str_nombre__icontains=nombre)

            # Filtro por framework
            if framework_id := filtros.get('framework_id'):
                query = query.filter(int_idFramework=framework_id)

            # Filtro por fecha de inicio
            if fecha_inicio := filtros.get('fecha_inicio'):
                try:
                    fecha = datetime.strptime(fecha_inicio, '%Y-%m-%d').date()
                    query = query.filter(dt_fechaInicio__date=fecha)
                except ValueError:
                    return Response(
                        message="Formato de fecha inválido. Use YYYY-MM-DD",
                        state=False
                    )

            # Filtro por estado
            if 'estado' in filtros:
                estado = filtros['estado']
                if isinstance(estado, str):
                    estado = estado.lower() == 'true'
                query = query.filter(bool_estado=estado)

            evaluaciones = query.all()

            if not evaluaciones:
                return Response(
                    message="No se encontraron evaluaciones con los criterios especificados",
                    state=False
                )

            return Response(
                message="Evaluaciones encontradas exitosamente",
                data=evaluaciones,
                state=True
            )

        except Exception as e:
            return Response(
                message=f"Error al buscar evaluaciones: {str(e)}",
                state=False)

    def reportar_controles(self, evaluacion_id: int):
        """
        Genera un reporte Excel específico para una evaluación, agrupando por control.
        """
        try:
            # Obtener la evaluación
            evaluacion = Evaluacion.objects.get(int_idEvaluacion=evaluacion_id)

            # Crear instancia del nuevo PlantillaController
            report_title = f"Reporte Evaluacion ID {evaluacion.int_idEvaluacion}"
            print(report_title)
            reporte_controller = PlantillaController(title=report_title)
            print(reporte_controller)

            # Generar el reporte y obtener la ruta del archivo
            archivo_response = reporte_controller.descargar_reporte(evaluacion_id)
            print(archivo_response)

            return archivo_response

        except Evaluacion.DoesNotExist:
            return Response(message="Evaluación no encontrada", state=False)
        except Exception as e:
            # Considerar loggear el error 'e' para depuración
            return Response(message=f"Error inesperado al generar el reporte: {str(e)}", state=False)

    def listar_resultados(self, evaluacion_id: int):
        """
        Genera una lista de resultados para una evaluación.
        """
        try:
            detalles = DetalleEvaluacion.objects.filter(int_idEvaluacion_id = evaluacion_id).all()
            resultados = []

            for detalle in detalles:
                name_dominio = detalle.int_idControl.int_idSubDominio.int_idDominio.str_nombre
                name_subdominio = detalle.int_idControl.int_idSubDominio.str_nombre
                descripcion_control = detalle.int_idControl.str_descripcion
                valor_tobe = detalle.str_valor_tobe
                valor_asis = detalle.str_valor_asis
                valor_industria = detalle.int_idControl.str_valorIndustria

                id_detalle = detalle.int_idDetalleEvaluacion
                evidencias = Evidencia.objects.filter(int_idDetalleEvaluacion_id=id_detalle).all()

                # Obtener los niveles para este control
                niveles_response = self.nivel_controller.get_by_control_id(detalle.int_idControl.int_idControl)
                niveles_formateados = []

                if niveles_response.state:
                    for nivel in niveles_response.data:
                        niveles_formateados.append({
                            "id_nivel": nivel.int_idNivel,
                            "descripcion": nivel.str_descripcion,
                            "valor": nivel.int_valor
                        })

                # Obtener el ID del evaluado
                id_evaluado = detalle.int_idEvaluado.int_idEvaluado if detalle.int_idEvaluado else None

                if not evidencias:
                    resultados.append({
                        "dominio": name_dominio,
                        "subdominio": name_subdominio,
                        "control": descripcion_control,
                        "tobe": valor_tobe,
                        "asis": valor_asis,
                        "valor_industria": valor_industria,
                        "evidencias": [],
                        "niveles": niveles_formateados,
                        "id_evaluado": id_evaluado,
                        "id_detalleEvaluacion": id_detalle
                    })
                else:
                    resultados.append({
                        "dominio": name_dominio,
                        "subdominio": name_subdominio,
                        "control": descripcion_control,
                        "tobe": valor_tobe,
                        "asis": valor_asis,
                        "valor_industria": valor_industria,
                        "evidencias": EvidenciaNombreSerializer(evidencias, many=True).data,
                        "niveles": niveles_formateados,
                        "id_evaluado": id_evaluado,
                        "id_detalleEvaluacion": id_detalle
                    })


            return Response(message="Resultados encontrados exitosamente", data=resultados, state=True)


        except Evaluacion.DoesNotExist:
            return Response(message="Evaluación no encontrada", state=False)
        except Exception as e:
            # Considerar loggear el error 'e' para depuración
            return Response(message=f"Error inesperado al generar la lista de resultados: {str(e)}", state=False)

    def get_by_evaluado(self, id_evaluado: int):
        """
        Genera una lista de evaluaciones por el id usuario que tiene el objeto evaluado.
        """
        try:
            # Primero obtenemos el evaluado para conseguir el ID del usuario
            try:
                evaluado = Evaluado.objects.get(int_idEvaluado=id_evaluado)
            except Evaluado.DoesNotExist:
                return Response(message=f"No se encontró el evaluado con ID {id_evaluado}", state=False)

            # Obtenemos el ID del usuario asociado al evaluado
            id_usuario = evaluado.int_idUsuarios.int_idUsuarios

            # Obtenemos todos los evaluados asociados a este usuario
            evaluados_usuario = Evaluado.objects.filter(int_idUsuarios_id=id_usuario).values_list('int_idEvaluado', flat=True)

            # Obtenemos todos los detalles de evaluación asociados a estos evaluados
            evaluaciones_id = DetalleEvaluacion.objects.filter(int_idEvaluado_id__in=evaluados_usuario).values_list('int_idEvaluacion', flat=True).distinct()

            # Obtenemos todas las evaluaciones
            evaluaciones = Evaluacion.objects.filter(int_idEvaluacion__in=evaluaciones_id).all()

            return Response(message="Evaluaciones encontradas exitosamente", data=evaluaciones, state=True)
        except Exception as e:
            return Response(message=f"Error inesperado al generar la lista de evaluaciones: {str(e)}", state=False)

    def calcular_avance_evaluado(self, evaluado_id: int):
        """
        Calcula el avance de un evaluado específico.

        Args:
            evaluado_id (int): ID del evaluado

        Returns:
            int: Porcentaje de avance (0-100)
        """
        try:
            # Obtener el evaluado
            evaluado = Evaluado.objects.get(int_idEvaluado=evaluado_id)

            # Obtener los detalles de evaluación para este evaluado
            detalles = DetalleEvaluacion.objects.filter(int_idEvaluado_id=evaluado_id).all()

            if not detalles:
                return 0

            total_detalles = len(detalles)
            detalles_completados = 0

            # Verificar qué campo debemos revisar según el tipo de evaluado
            if evaluado.int_idTipoEvaluado == 1:  # ToBe
                for detalle in detalles:
                    if detalle.str_valor_tobe:
                        detalles_completados += 1
            elif evaluado.int_idTipoEvaluado == 2:  # AsIs
                for detalle in detalles:
                    if detalle.str_valor_asis:
                        detalles_completados += 1

            # Calcular el porcentaje de avance
            if total_detalles > 0:
                avance = (detalles_completados / total_detalles) * 100
                return int(avance)
            else:
                return 0

        except Exception as e:
            print(f"Error al calcular avance del evaluado {evaluado_id}: {str(e)}")
            return 0

    def get_by_usuario(self, id_usuario: int):
        """
        Genera una lista de evaluaciones directamente por el id usuario.
        Incluye los evaluados asociados a cada evaluación que pertenecen al usuario.
        """
        try:
            # Obtenemos todos los evaluados asociados a este usuario
            evaluados_usuario = Evaluado.objects.filter(int_idUsuarios_id=id_usuario).values_list('int_idEvaluado', flat=True)

            if not evaluados_usuario.exists():
                return Response(message=f"No se encontraron evaluados para el usuario con ID {id_usuario}", state=False)

            # Obtenemos todos los detalles de evaluación asociados a estos evaluados
            evaluaciones_id = DetalleEvaluacion.objects.filter(int_idEvaluado_id__in=evaluados_usuario).values_list('int_idEvaluacion', flat=True).distinct()

            # Obtenemos todas las evaluaciones
            evaluaciones = Evaluacion.objects.filter(int_idEvaluacion__in=evaluaciones_id).all()

            if not evaluaciones:
                return Response(message=f"No se encontraron evaluaciones para el usuario con ID {id_usuario}", state=False)

            # Preparamos un diccionario para almacenar los evaluados por evaluación
            evaluaciones_con_evaluados = []

            # Para cada evaluación, obtenemos sus evaluados asociados al usuario
            for evaluacion in evaluaciones:
                # Obtenemos los evaluados que pertenecen a esta evaluación y al usuario especificado
                evaluados = Evaluado.objects.filter(
                    int_idEvaluacion_id=evaluacion.int_idEvaluacion,
                    int_idUsuarios_id=id_usuario
                ).all()

                # Para cada evaluado, calculamos su avance
                evaluados_con_avance = []
                for evaluado in evaluados:
                    avance = self.calcular_avance_evaluado(evaluado.int_idEvaluado)
                    evaluados_con_avance.append({
                        'evaluado': evaluado,
                        'avance': avance
                    })

                # Creamos un diccionario con la evaluación y sus evaluados
                evaluacion_dict = {
                    'evaluacion': evaluacion,
                    'evaluados': evaluados_con_avance
                }

                evaluaciones_con_evaluados.append(evaluacion_dict)

            return Response(message="Evaluaciones encontradas exitosamente", data=evaluaciones_con_evaluados, state=True)
        except Exception as e:
            return Response(message=f"Error inesperado al generar la lista de evaluaciones: {str(e)}", state=False)

    def listar_resultados_by_subdominio(self, evaluacion_id: int, subdominio_id: int):
        """
        Genera una lista de resultados para una evaluación por subdominio.
        """
        try:
            detalles = DetalleEvaluacion.objects.filter(
                int_idEvaluacion_id = evaluacion_id,
                int_idControl__int_idSubDominio_id = subdominio_id
                ).all()
            resultados = []

            for detalle in detalles:
                name_dominio = detalle.int_idControl.int_idSubDominio.int_idDominio.str_nombre
                name_subdominio = detalle.int_idControl.int_idSubDominio.str_nombre
                descripcion_control = detalle.int_idControl.str_descripcion
                valor_tobe = detalle.str_valor_tobe
                valor_asis = detalle.str_valor_asis
                valor_industria = detalle.int_idControl.str_valorIndustria

                id_detalle = detalle.int_idDetalleEvaluacion
                evidencias = Evidencia.objects.filter(int_idDetalleEvaluacion_id=id_detalle).all()

                # Obtener los niveles para este control
                niveles_response = self.nivel_controller.get_by_control_id(detalle.int_idControl.int_idControl)
                niveles_formateados = []

                if niveles_response.state:
                    for nivel in niveles_response.data:
                        niveles_formateados.append({
                            "id_nivel": nivel.int_idNivel,
                            "descripcion": nivel.str_descripcion,
                            "valor": nivel.int_valor
                        })

                # Obtener el ID del evaluado
                id_evaluado = detalle.int_idEvaluado.int_idEvaluado if detalle.int_idEvaluado else None

                if not evidencias:
                    resultados.append({
                        "dominio": name_dominio,
                        "subdominio": name_subdominio,
                        "control": descripcion_control,
                        "tobe": valor_tobe,
                        "asis": valor_asis,
                        "valor_industria": valor_industria,
                        "evidencias": [],
                        "niveles": niveles_formateados,
                        "id_evaluado": id_evaluado,
                        "id_detalleEvaluacion": id_detalle
                    })
                else:
                    resultados.append({
                        "dominio": name_dominio,
                        "subdominio": name_subdominio,
                        "control": descripcion_control,
                        "tobe": valor_tobe,
                        "asis": valor_asis,
                        "valor_industria": valor_industria,
                        "evidencias": EvidenciaNombreSerializer(evidencias, many=True).data,
                        "niveles": niveles_formateados,
                        "id_evaluado": id_evaluado,
                        "id_detalleEvaluacion": id_detalle
                    })


            return Response(message="Resultados encontrados exitosamente", data=resultados, state=True)


        except Evaluacion.DoesNotExist:
            return Response(message="Evaluación no encontrada", state=False)
        except Exception as e:
            # Considerar loggear el error 'e' para depuración
            return Response(message=f"Error inesperado al generar la lista de resultados: {str(e)}", state=False)


    def calcular_avance_tobe(self, evaluacion_id: int):
        """
        Calcula el avance ToBe de una evaluación.
        """
        try:
            # Obtener los IDs de los evaluados ToBe para esta evaluación
            evaluados_tobe_id = Evaluado.objects.filter(
                int_idTipoEvaluado = 1,
                int_idEvaluacion_id = evaluacion_id
                ).values_list('int_idEvaluado', flat=True)

            # Obtener los detalles de evaluación para estos evaluados ToBe
            detalles = DetalleEvaluacion.objects.filter(
                int_idEvaluacion_id = evaluacion_id,
                int_idEvaluado_id__in = evaluados_tobe_id
                ).all()
            nro_detalles_avance_tobe = 0
            total_detalles = len(detalles)

            if total_detalles == 0:
                return Response(message="No se encontraron detalles de evaluación", state=False)

            for detalle in detalles:
                if detalle.str_valor_tobe:
                    nro_detalles_avance_tobe += 1

            avance_tobe = (nro_detalles_avance_tobe / total_detalles) * 100
            return Response(message="Avance ToBe calculado exitosamente", data=int(avance_tobe), state=True)

        except Evaluacion.DoesNotExist:
            return Response(message="Evaluación no encontrada", state=False)
        except Exception as e:
            # Considerar loggear el error 'e' para depuración
            return Response(message=f"Error inesperado al calcular el avance ToBe: {str(e)}", state=False)

    def calcular_avance_tobe_evaluado(self, evaluacion_id: int, evaluado_id: int):
        """
        Calcula el avance ToBe de un evaluado específico dentro de una evaluación.

        Args:
            evaluacion_id (int): ID de la evaluación
            evaluado_id (int): ID del evaluado

        Returns:
            Response: Objeto con el resultado de la operación
                - state (bool): True si la operación fue exitosa, False en caso contrario
                - data (int): Porcentaje de avance (0-100)
                - message (str): Mensaje descriptivo del resultado
        """
        try:
            # Verificar que la evaluación existe
            if not Evaluacion.objects.filter(int_idEvaluacion=evaluacion_id).exists():
                return Response(message="Evaluación no encontrada", state=False)

            # Verificar que el evaluado existe y pertenece a la evaluación
            try:
                # Solo verificamos que exista, no necesitamos usar la variable
                Evaluado.objects.get(
                    int_idEvaluado=evaluado_id,
                    int_idEvaluacion_id=evaluacion_id,
                    int_idTipoEvaluado=1  # ToBe
                )
            except Evaluado.DoesNotExist:
                return Response(message="Evaluado ToBe no encontrado para esta evaluación", state=False)

            # Obtener los detalles de evaluación para este evaluado
            detalles = DetalleEvaluacion.objects.filter(
                int_idEvaluacion_id=evaluacion_id,
                int_idEvaluado_id=evaluado_id
            ).all()

            if not detalles:
                return Response(message="No se encontraron detalles de evaluación para este evaluado", state=False)

            total_detalles = len(detalles)
            detalles_completados = 0

            # Contar detalles completados
            for detalle in detalles:
                if detalle.str_valor_tobe:
                    detalles_completados += 1

            # Calcular el porcentaje de avance
            avance = (detalles_completados / total_detalles) * 100

            return Response(
                message="Avance ToBe del evaluado calculado exitosamente",
                data=int(avance),
                state=True
            )

        except Exception as e:
            return Response(message=f"Error inesperado al calcular el avance ToBe del evaluado: {str(e)}", state=False)


    def calcular_avance_asis(self, evaluacion_id: int):
        """
        Calcula el avance AsIs de una evaluación.
        """
        try:
            # Obtener los IDs de los evaluados AsIs para esta evaluación
            evaluados_asis_id = Evaluado.objects.filter(
                int_idTipoEvaluado = 2,
                int_idEvaluacion_id = evaluacion_id
                ).values_list('int_idEvaluado', flat=True)

            # Obtener los detalles de evaluación para estos evaluados AsIs
            detalles = DetalleEvaluacion.objects.filter(
                int_idEvaluacion_id = evaluacion_id,
                int_idEvaluado_id__in = evaluados_asis_id
                ).all()

            nro_detalles_avance_asis = 0
            total_detalles = len(detalles)

            if total_detalles == 0:
                return Response(message="No se encontraron detalles de evaluación", state=False)

            for detalle in detalles:
                if detalle.str_valor_asis:
                    nro_detalles_avance_asis += 1

            avance_asis = (nro_detalles_avance_asis / total_detalles) * 100
            return Response(message="Avance AsIs calculado exitosamente", data=int(avance_asis), state=True)

        except Evaluacion.DoesNotExist:
            return Response(message="Evaluación no encontrada", state=False)
        except Exception as e:
            # Considerar loggear el error 'e' para depuración
            return Response(message=f"Error inesperado al calcular el avance AsIs: {str(e)}", state=False)

    def calcular_avance_asis_evaluado(self, evaluacion_id: int, evaluado_id: int):
        """
        Calcula el avance AsIs de un evaluado específico dentro de una evaluación.

        Args:
            evaluacion_id (int): ID de la evaluación
            evaluado_id (int): ID del evaluado

        Returns:
            Response: Objeto con el resultado de la operación
                - state (bool): True si la operación fue exitosa, False en caso contrario
                - data (int): Porcentaje de avance (0-100)
                - message (str): Mensaje descriptivo del resultado
        """
        try:
            # Verificar que la evaluación existe
            if not Evaluacion.objects.filter(int_idEvaluacion=evaluacion_id).exists():
                return Response(message="Evaluación no encontrada", state=False)

            # Verificar que el evaluado existe y pertenece a la evaluación
            try:
                # Solo verificamos que exista, no necesitamos usar la variable
                Evaluado.objects.get(
                    int_idEvaluado=evaluado_id,
                    int_idEvaluacion_id=evaluacion_id,
                    int_idTipoEvaluado=2  # AsIs
                )
            except Evaluado.DoesNotExist:
                return Response(message="Evaluado AsIs no encontrado para esta evaluación", state=False)

            # Obtener los detalles de evaluación para este evaluado
            detalles = DetalleEvaluacion.objects.filter(
                int_idEvaluacion_id=evaluacion_id,
                int_idEvaluado_id=evaluado_id
            ).all()

            if not detalles:
                return Response(message="No se encontraron detalles de evaluación para este evaluado", state=False)

            total_detalles = len(detalles)
            detalles_completados = 0

            # Contar detalles completados
            for detalle in detalles:
                if detalle.str_valor_asis:
                    detalles_completados += 1

            # Calcular el porcentaje de avance
            avance = (detalles_completados / total_detalles) * 100

            return Response(
                message="Avance AsIs del evaluado calculado exitosamente",
                data=int(avance),
                state=True
            )

        except Exception as e:
            return Response(message=f"Error inesperado al calcular el avance AsIs del evaluado: {str(e)}", state=False)


    def cambiar_estado(self, evaluacion_id: int):
        """Cambia el estado (activo/inactivo) de una evaluacion.

        Si bool_estado es True (en proceso) y se quiere cambiar a False (terminado),
        se verifica que bool_asis y bool_tobe sean True antes de permitir el cambio.
        Si ambos son True, se calcula el resultado promedio y se actualiza str_nota y str_resultado.

        Args:
            evaluacion_id (int): ID de la evaluacion a modificar.

        Returns:
            Response: Respuesta indicando el resultado de la operación.
        """
        try:
            evaluacion = Evaluacion.objects.get(int_idEvaluacion=evaluacion_id)

            # Si la evaluación está en proceso (bool_estado=True) y se quiere terminar (cambiar a False)
            if evaluacion.bool_estado:
                # Verificar que bool_asis y bool_tobe sean True
                if not (evaluacion.bool_asis and evaluacion.bool_tobe):
                    return Response(
                        "No se puede finalizar la evaluación porque no se han completado todas las evaluaciones AsIs y ToBe",
                        state=False
                    )

                # Calcular resultado promedio
                resultado = self.calcular_resultado_promedio(evaluacion_id)
                if not resultado.state:
                    return Response(
                        f"No se pudo calcular el resultado promedio: {resultado.message}",
                        state=False
                    )

                # Obtener valores asis y tobe
                asis_value = resultado.data.get("asis")
                tobe_value = resultado.data.get("tobe")

                if asis_value is None or tobe_value is None:
                    return Response(
                        "No se pudo obtener los valores AsIs o ToBe",
                        state=False
                    )

                # Convertir a float si son strings
                if isinstance(asis_value, str):
                    asis_value = float(asis_value)
                if isinstance(tobe_value, str):
                    tobe_value = float(tobe_value)

                # # Truncar asis al entero anterior
                # asis_truncated = int(asis_value)

                # Aproximar tobe según el primer decimal
                tobe_decimal = tobe_value - int(tobe_value)
                if tobe_decimal >= 0.5:
                    tobe_rounded = int(tobe_value) + 1
                else:
                    tobe_rounded = int(tobe_value)

                # # Calcular la brecha
                # brecha = tobe_rounded - asis_truncated

                # # Establecer mensaje según la brecha
                # if brecha <= 0:
                #     mensaje = "¡Excelente! El estado actual cumple o supera el nivel deseado."
                # else:
                #     mensaje = "Hay oportunidades de mejora para alcanzar el nivel deseado."

                # # Actualizar campos de la evaluación
                # evaluacion.str_nota = str(asis_truncated)
                # evaluacion.str_resultado = mensaje
                evaluacion.bool_estado = False  # Cambiar a terminado
                evaluacion.save()

                return Response(
                    # f"Evaluación finalizada. AsIs: {asis_truncated}, ToBe: {tobe_rounded}. {mensaje}",
                    f"Evaluación finalizada. AsIs: {evaluacion.str_nota}, ToBe: {tobe_rounded}. {evaluacion.str_resultado}",
                    state=True
                )
            # else:
            #     # Si está inactiva, simplemente cambiar a activa
            #     evaluacion.bool_estado = True
            #     evaluacion.save()
            #     return Response(
            #         "Estado de la evaluación cambiado a activo",
            #         state=True
            #     )
        except Evaluacion.DoesNotExist:
            return Response(
                f"Evaluación con ID {evaluacion_id} no encontrada",
                state=False
            )
        except Exception as e:
            # Considerar registrar el error aquí para depuración
            return Response(f"Error al cambiar estado: {str(e)}", state=False)

    def calcular_resultado_promedio(self, evaluacion_id: int):
        """
        Calcula el Resultado promedio ToBe y AsIs de una evaluación.
        """
        try:
            # Obtener los detalles de evaluación ToBe para esta evaluación
            evaluados_tobe_id = Evaluado.objects.filter(
                int_idTipoEvaluado = 1,
                int_idEvaluacion_id = evaluacion_id
                ).values_list('int_idEvaluado', flat=True)
            detalles_tobe = DetalleEvaluacion.objects.filter(
                int_idEvaluacion_id = evaluacion_id,
                int_idEvaluado_id__in = evaluados_tobe_id
                ).all()

            # Obtener los detalles de evaluación AsIs para esta evaluación
            evaluados_asis_id = Evaluado.objects.filter(
                int_idTipoEvaluado = 2,
                int_idEvaluacion_id = evaluacion_id
                ).values_list('int_idEvaluado', flat=True)
            detalles_asis = DetalleEvaluacion.objects.filter(
                int_idEvaluacion_id = evaluacion_id,
                int_idEvaluado_id__in = evaluados_asis_id
                ).all()

            # Validar si existen detalles de evaluación ToBe -> Promedio ToBe
            nro_detalles_avance_tobe = 0
            valor_tobe = 0
            controles_tobe = []
            detalles_tobe_state = True

            if len(detalles_tobe) == 0:
                detalles_tobe_state = False
                # return Response(message="No se encontraron detalles de evaluación ToBe", state=False)
            else:
                for detalle in detalles_tobe:
                    if detalle.str_valor_tobe:
                        if not detalle.int_idControl.int_idControl in controles_tobe:
                            controles_tobe.append(detalle.int_idControl.int_idControl)
                        nro_detalles_avance_tobe += 1
                        valor_tobe += int(detalle.str_valor_tobe)

                if not nro_detalles_avance_tobe == 0:
                    print(f'Valor Tobe: {valor_tobe}, Nro Detalles: {nro_detalles_avance_tobe}')
                    promedio_tobe = valor_tobe / nro_detalles_avance_tobe
                    print(f'Promedio ToBe: {promedio_tobe}')

            # Validar si existen detalles de evaluación AsIs -> Promedio AsIs
            nro_detalles_avance_asis = 0
            valor_asis = 0
            controles_asis = []
            detalle_asis_state = True

            if len(detalles_asis) == 0:
                detalle_asis_state = False
                # return Response(message="No se encontraron detalles de evaluación AsIs", state=False)
            else:
                for detalle in detalles_asis:
                    print(detalle.int_idDetalleEvaluacion, detalle.int_idControl.int_idControl, detalle.str_valor_asis, detalle.str_valor_tobe)
                    if detalle.str_valor_asis:
                        if not detalle.int_idControl.int_idControl in controles_asis:
                            controles_asis.append(detalle.int_idControl.int_idControl)
                        nro_detalles_avance_asis += 1
                        valor_asis += int(detalle.str_valor_asis)

                if not nro_detalles_avance_asis == 0:
                    print(f'Valor Asis: {valor_asis}, Nro Detalles: {nro_detalles_avance_asis}')
                    promedio_asis = valor_asis / nro_detalles_avance_asis
                    print(f'Promedio Asis: {promedio_asis}')


            if not detalles_tobe_state and not detalle_asis_state:
                return Response(message="No se encontraron detalles de evaluación", state=False)
            elif not detalles_tobe_state:
                return Response(message="No se encontraron detalles de evaluación ToBe, pero si AsIs",
                                data={
                                    "asis": round(promedio_asis,1) if not nro_detalles_avance_asis == 0 else None,
                                    "tobe": None
                                },
                                state=False)
            elif not detalle_asis_state:
                return Response(message="No se encontraron detalles de evaluación AsIs, pero si ToBe",
                                data={
                                    "asis": None,
                                    "tobe": round(promedio_tobe,1) if not nro_detalles_avance_tobe == 0 else None
                                },
                                state=False)
            else:
                return Response(message="Resultado promedio calculado exitosamente",
                            data={
                                "asis": round(promedio_asis,1) if not nro_detalles_avance_asis == 0 else None,
                                "tobe": round(promedio_tobe,1) if not nro_detalles_avance_tobe == 0 else None
                            },
                            state=True)

        except Evaluacion.DoesNotExist:
            return Response(message="Evaluación no encontrada", state=False)
        except Exception as e:
            # Considerar loggear el error 'e' para depuración
            return Response(message=f"Error inesperado al calcular el avance AsIs: {str(e)}", state=False)

    def listar_resultados_control_promedio(self, evaluacion_id: int):
        """
        Genera una lista de resultados promedio por control para una evaluación.
        Agrupa los detalles de evaluación por control y calcula el promedio de los valores tobe y asis.
        """
        try:
            # Verificar que la evaluación existe
            if not Evaluacion.objects.filter(int_idEvaluacion=evaluacion_id).exists():
                return Response(message="Evaluación no encontrada", state=False)

            # Obtener todos los detalles de evaluación para esta evaluación
            detalles = DetalleEvaluacion.objects.filter(int_idEvaluacion_id=evaluacion_id).all()

            if not detalles:
                return Response(message="No se encontraron detalles de evaluación", state=False)

            # Diccionario para agrupar por control
            controles_dict = {}

            # Recorrer todos los detalles y agruparlos por control
            for detalle in detalles:
                control_id = detalle.int_idControl.int_idControl

                # Si el control no está en el diccionario, inicializarlo
                if control_id not in controles_dict:
                    controles_dict[control_id] = {
                        "id_control": control_id,
                        "dominio": detalle.int_idControl.int_idSubDominio.int_idDominio.str_nombre,
                        "subdominio": detalle.int_idControl.int_idSubDominio.str_nombre,
                        "control": detalle.int_idControl.str_descripcion,
                        "valor_industria": detalle.int_idControl.str_valorIndustria,
                        "tobe_values": [],
                        "asis_values": [],
                        "evidencias": [],
                    }

                # Obtener el tipo de evaluado
                tipo_evaluado = detalle.int_idEvaluado.int_idTipoEvaluado if detalle.int_idEvaluado else None

                # Agregar valores según el tipo de evaluado
                if tipo_evaluado == 1 and detalle.str_valor_tobe:  # ToBe
                    controles_dict[control_id]["tobe_values"].append(int(detalle.str_valor_tobe))
                elif tipo_evaluado == 2 and detalle.str_valor_asis:  # AsIs
                    controles_dict[control_id]["asis_values"].append(int(detalle.str_valor_asis))

                # Agregar evidencias si existen
                evidencias = Evidencia.objects.filter(int_idDetalleEvaluacion_id=detalle.int_idDetalleEvaluacion).all()
                if evidencias:
                    for evidencia in evidencias:
                        evidencia_data = {
                            "int_idEvidencia": evidencia.int_idEvidencia,
                            "nombre_archivo": os.path.basename(evidencia.str_ruta) if evidencia.str_ruta else None
                        }
                        if evidencia_data not in controles_dict[control_id]["evidencias"]:
                            controles_dict[control_id]["evidencias"].append(evidencia_data)

            # Calcular promedios y formatear resultados
            resultados = []
            for control_id, data in controles_dict.items():
                # Calcular promedio de tobe
                tobe_avg = None
                if data["tobe_values"]:
                    tobe_avg = sum(data["tobe_values"]) / len(data["tobe_values"])
                    tobe_avg = str(round(tobe_avg, 1))

                # Calcular promedio de asis
                asis_avg = None
                if data["asis_values"]:
                    asis_avg = sum(data["asis_values"]) / len(data["asis_values"])
                    asis_avg = str(round(asis_avg, 1))

                # Crear objeto de resultado
                resultado = {
                    "id_control": data["id_control"],
                    "dominio": data["dominio"],
                    "subdominio": data["subdominio"],
                    "control": data["control"],
                    "tobe": tobe_avg,
                    "asis": asis_avg,
                    "valor_industria": data["valor_industria"],
                    "evidencias": data["evidencias"]
                }

                resultados.append(resultado)

            return Response(message="Resultados promedio por control encontrados exitosamente", data=resultados, state=True)

        except Exception as e:
            # Considerar loggear el error 'e' para depuración
            return Response(message=f"Error inesperado al generar la lista de resultados promedio por control: {str(e)}", state=False)

    def listar_resultados_control_promedio_by_subdominio(self, evaluacion_id: int, subdominio_id: int):
        """
        Genera una lista de resultados promedio por control para una evaluación, filtrados por subdominio.
        Agrupa los detalles de evaluación por control y calcula el promedio de los valores tobe y asis.
        """
        try:
            # Verificar que la evaluación existe
            if not Evaluacion.objects.filter(int_idEvaluacion=evaluacion_id).exists():
                return Response(message="Evaluación no encontrada", state=False)

            # Obtener todos los detalles de evaluación para esta evaluación y subdominio
            detalles = DetalleEvaluacion.objects.filter(
                int_idEvaluacion_id=evaluacion_id,
                int_idControl__int_idSubDominio_id=subdominio_id
            ).all()

            if not detalles:
                return Response(message="No se encontraron detalles de evaluación para el subdominio especificado", state=False)

            # Diccionario para agrupar por control
            controles_dict = {}

            # Recorrer todos los detalles y agruparlos por control
            for detalle in detalles:
                control_id = detalle.int_idControl.int_idControl

                # Si el control no está en el diccionario, inicializarlo
                if control_id not in controles_dict:
                    controles_dict[control_id] = {
                        "id_control": control_id,
                        "dominio": detalle.int_idControl.int_idSubDominio.int_idDominio.str_nombre,
                        "subdominio": detalle.int_idControl.int_idSubDominio.str_nombre,
                        "control": detalle.int_idControl.str_descripcion,
                        "valor_industria": detalle.int_idControl.str_valorIndustria,
                        "tobe_values": [],
                        "asis_values": [],
                        "evidencias": [],
                    }

                # Obtener el tipo de evaluado
                tipo_evaluado = detalle.int_idEvaluado.int_idTipoEvaluado if detalle.int_idEvaluado else None

                # Agregar valores según el tipo de evaluado
                if tipo_evaluado == 1 and detalle.str_valor_tobe:  # ToBe
                    controles_dict[control_id]["tobe_values"].append(int(detalle.str_valor_tobe))
                elif tipo_evaluado == 2 and detalle.str_valor_asis:  # AsIs
                    controles_dict[control_id]["asis_values"].append(int(detalle.str_valor_asis))

                # Agregar evidencias si existen
                evidencias = Evidencia.objects.filter(int_idDetalleEvaluacion_id=detalle.int_idDetalleEvaluacion).all()
                if evidencias:
                    for evidencia in evidencias:
                        evidencia_data = {
                            "int_idEvidencia": evidencia.int_idEvidencia,
                            "nombre_archivo": os.path.basename(evidencia.str_ruta) if evidencia.str_ruta else None
                        }
                        if evidencia_data not in controles_dict[control_id]["evidencias"]:
                            controles_dict[control_id]["evidencias"].append(evidencia_data)

            # Calcular promedios y formatear resultados
            resultados = []
            for control_id, data in controles_dict.items():
                # Calcular promedio de tobe
                tobe_avg = None
                if data["tobe_values"]:
                    tobe_avg = sum(data["tobe_values"]) / len(data["tobe_values"])
                    tobe_avg = str(round(tobe_avg, 1))

                # Calcular promedio de asis
                asis_avg = None
                if data["asis_values"]:
                    asis_avg = sum(data["asis_values"]) / len(data["asis_values"])
                    asis_avg = str(round(asis_avg, 1))

                # Crear objeto de resultado
                resultado = {
                    "id_control": data["id_control"],
                    "dominio": data["dominio"],
                    "subdominio": data["subdominio"],
                    "control": data["control"],
                    "tobe": tobe_avg,
                    "asis": asis_avg,
                    "valor_industria": data["valor_industria"],
                    "evidencias": data["evidencias"]
                }

                resultados.append(resultado)

            return Response(message="Resultados promedio por control y subdominio encontrados exitosamente", data=resultados, state=True)

        except Exception as e:
            # Considerar loggear el error 'e' para depuración
            return Response(message=f"Error inesperado al generar la lista de resultados promedio por control y subdominio: {str(e)}", state=False)