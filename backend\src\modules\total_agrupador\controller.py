from src.utils.classes import Response as MiddleResponse
from src.modules.total_agrupador.models import TotalAgrupador
from src.modules.total_agrupador.serializers import TotalAgrupadorSerializer
from src.modules.estado_financiero.models import EstadoFinanciero
from src.modules.agrupadores.models import Agrupador
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Q, Sum
from datetime import datetime, timedelta
from decimal import Decimal

class TotalAgrupadorController:
    def __init__(self):
        pass

    def obtener_agrupadores_por_periodo(self, tipo_periodo, id_empresa):
        """
        Obtiene los agrupadores por periodo para una empresa específica.

        Args:
            tipo_periodo (int): ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
            id_empresa (int): ID de la empresa

        Returns:
            Response: Objeto Response con los datos de los agrupadores por periodo
        """
        try:
            # Mapear el tipo de periodo a los valores en la base de datos
            # Verificar en Model EstadoFinanciero PERIODO_CHOICES
            tipo_periodo_map = {
                1: "Anual",
                2: "Trimestral",
                3: "Mensual",
                4: "Semanal",
                5: "Diario"
            }

            # Validar el tipo de periodo
            if tipo_periodo not in tipo_periodo_map.keys():
                return MiddleResponse(
                    message=f"El tipo de periodo {tipo_periodo} no es válido. "
                    f"Los tipos de periodo válidos son: {', '.join(tipo_periodo_map.values())}",
                    state=False
                )

            # Obtener los estados financieros para la empresa y tipo de periodo
            estados_financieros = EstadoFinanciero.objects.filter(
                int_idEmpresa=id_empresa,
                int_tipoPeriodo=tipo_periodo,
                int_tipoRegistro=0  # Solo periodos contables, no simulaciones
            ).order_by('-dt_fechaFinPeriodo')  # Ordenar por fecha de fin de periodo descendente

            if not estados_financieros:
                return MiddleResponse(
                    message=f"No se encontraron estados financieros para la empresa con ID {id_empresa} y tipo de periodo {tipo_periodo}",
                    state=False
                )

            # Agrupar estados financieros por periodo
            periodos = {}
            for ef in estados_financieros:
                # Formatear el periodo contable según el tipo
                periodo_contable = self._formatear_periodo_contable(ef)

                # Agrupar por periodo contable
                if periodo_contable not in periodos:
                    periodos[periodo_contable] = {
                        "periodo_contable": periodo_contable,
                        "estados_financieros": []
                    }

                # Obtener los totales de agrupadores para este estado financiero
                totales_agrupadores = TotalAgrupador.objects.filter(
                    int_idEstadoFinanciero=ef.int_idEstadoFinanciero
                )

                if not totales_agrupadores:
                    continue

                # Preparar la estructura de datos para los agrupadores
                agrupadores_data = []

                # Si es un Estado de Situación Financiera (tipo 1), inicializar variables para calcular totales
                activo_corriente = None
                activo_no_corriente = None
                pasivo_corriente = None
                pasivo_no_corriente = None

                for total in totales_agrupadores:
                    nombre_agrupador = total.int_idAgrupador.str_nombre.lower()
                    nombre_subagrupador = total.int_idAgrupador.str_nombre_subagrupador.lower() if total.int_idAgrupador.str_nombre_subagrupador else None

                    # Guardar los valores de activos y pasivos para calcular totales después
                    if ef.int_tipoEstadoFinanciero == 1:  # Estado de Situación Financiera
                        # Buscar activo corriente
                        if "activo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                            activo_corriente = total.db_resultadoAgrupador

                        # Buscar activo no corriente
                        elif "activo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                            activo_no_corriente = total.db_resultadoAgrupador

                        # Buscar pasivo corriente
                        elif "pasivo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                            pasivo_corriente = total.db_resultadoAgrupador

                        # Buscar pasivo no corriente
                        elif "pasivo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                            pasivo_no_corriente = total.db_resultadoAgrupador

                    # Buscar el estado financiero del periodo anterior para calcular la variación
                    ef_anterior = self._obtener_estado_financiero_anterior(ef, tipo_periodo, id_empresa)
                    # Calcular el porcentaje de variación si existe un periodo anterior
                    porcentaje_variacion = None
                    if ef_anterior:
                        # Buscar el total del agrupador en el periodo anterior
                        try:
                            total_anterior = TotalAgrupador.objects.get(
                                int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero,
                                int_idAgrupador=total.int_idAgrupador
                            )
                            # Calcular la variación porcentual
                            if total_anterior.db_resultadoAgrupador != 0:
                                variacion_abs = (total.db_resultadoAgrupador - total_anterior.db_resultadoAgrupador)
                                variacion = variacion_abs / abs(total_anterior.db_resultadoAgrupador)
                                variacion = variacion * 100
                                # Formatear a 2 decimales y agregar el signo
                                signo = "+" if variacion > 0 else ""
                                porcentaje_variacion = f"{signo}{variacion:.2f}%"
                            else:
                                porcentaje_variacion = None
                        except TotalAgrupador.DoesNotExist:
                            porcentaje_variacion = None

                    # Determinar el nombre a mostrar: subagrupador si existe, sino el nombre principal
                    nombre_mostrar = total.int_idAgrupador.str_nombre_subagrupador if total.int_idAgrupador.str_nombre_subagrupador else total.int_idAgrupador.str_nombre

                    # Crear el objeto de datos para este agrupador
                    agrupador_data = {
                        "int_idTotalAgrupador": total.int_idTotalAgrupador,
                        "db_resultadoAgrupador": total.db_resultadoAgrupador,
                        "int_idAgrupador": total.int_idAgrupador.int_idAgrupador,
                        "nombre": nombre_mostrar,
                        "str_porcentaje_variacion": porcentaje_variacion
                    }

                    agrupadores_data.append(agrupador_data)

                # Calcular y agregar activos totales y pasivos totales para estados financieros tipo 1
                if ef.int_tipoEstadoFinanciero == 1:  # Estado de Situación Financiera
                    # Calcular activos totales
                    if activo_corriente is not None and activo_no_corriente is not None:
                        activos_totales = activo_corriente + activo_no_corriente

                        # Calcular la variación porcentual para activos totales
                        porcentaje_variacion = None
                        try:
                            # Obtener el estado financiero del periodo anterior
                            ef_anterior = self._obtener_estado_financiero_anterior(ef, tipo_periodo, id_empresa)

                            if ef_anterior:
                                # Buscar los componentes en el periodo anterior
                                activo_corriente_anterior = None
                                activo_no_corriente_anterior = None

                                totales_anteriores = TotalAgrupador.objects.filter(
                                    int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero
                                )

                                for total_ant in totales_anteriores:
                                    nombre_agrupador_ant = total_ant.int_idAgrupador.str_nombre.lower()
                                    nombre_subagrupador_ant = total_ant.int_idAgrupador.str_nombre_subagrupador.lower() if total_ant.int_idAgrupador.str_nombre_subagrupador else None

                                    # Buscar activo corriente
                                    if "activo" in nombre_agrupador_ant and nombre_subagrupador_ant and "corriente" in nombre_subagrupador_ant and "no" not in nombre_subagrupador_ant:
                                        activo_corriente_anterior = total_ant.db_resultadoAgrupador

                                    # Buscar activo no corriente
                                    elif "activo" in nombre_agrupador_ant and nombre_subagrupador_ant and "no corriente" in nombre_subagrupador_ant:
                                        activo_no_corriente_anterior = total_ant.db_resultadoAgrupador

                                if activo_corriente_anterior is not None and activo_no_corriente_anterior is not None:
                                    activos_totales_anterior = activo_corriente_anterior + activo_no_corriente_anterior

                                    if activos_totales_anterior != 0:
                                        variacion_abs = (activos_totales - activos_totales_anterior)
                                        variacion = variacion_abs / abs(activos_totales_anterior)
                                        variacion = variacion * 100

                                        # Formatear a 2 decimales y agregar el signo
                                        signo = "+" if variacion > 0 else ""
                                        porcentaje_variacion = f"{signo}{variacion:.2f}%"
                        except Exception:
                            porcentaje_variacion = None

                        # Agregar activos totales al resultado
                        agrupadores_data.append({
                            "int_idTotalAgrupador": None,
                            "db_resultadoAgrupador": activos_totales,
                            "int_idAgrupador": None,
                            "nombre": "activos totales",
                            "str_porcentaje_variacion": porcentaje_variacion
                        })

                    # Calcular pasivos totales
                    if pasivo_corriente is not None and pasivo_no_corriente is not None:
                        pasivos_totales = pasivo_corriente + pasivo_no_corriente

                        # Calcular la variación porcentual para pasivos totales
                        porcentaje_variacion = None
                        try:
                            # Obtener el estado financiero del periodo anterior
                            ef_anterior = self._obtener_estado_financiero_anterior(ef, tipo_periodo, id_empresa)

                            if ef_anterior:
                                # Buscar los componentes en el periodo anterior
                                pasivo_corriente_anterior = None
                                pasivo_no_corriente_anterior = None

                                totales_anteriores = TotalAgrupador.objects.filter(
                                    int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero
                                )

                                for total_ant in totales_anteriores:
                                    nombre_agrupador_ant = total_ant.int_idAgrupador.str_nombre.lower()
                                    nombre_subagrupador_ant = total_ant.int_idAgrupador.str_nombre_subagrupador.lower() if total_ant.int_idAgrupador.str_nombre_subagrupador else None

                                    # Buscar pasivo corriente
                                    if "pasivo" in nombre_agrupador_ant and nombre_subagrupador_ant and "corriente" in nombre_subagrupador_ant and "no" not in nombre_subagrupador_ant:
                                        pasivo_corriente_anterior = total_ant.db_resultadoAgrupador

                                    # Buscar pasivo no corriente
                                    elif "pasivo" in nombre_agrupador_ant and nombre_subagrupador_ant and "no corriente" in nombre_subagrupador_ant:
                                        pasivo_no_corriente_anterior = total_ant.db_resultadoAgrupador

                                if pasivo_corriente_anterior is not None and pasivo_no_corriente_anterior is not None:
                                    pasivos_totales_anterior = pasivo_corriente_anterior + pasivo_no_corriente_anterior

                                    if pasivos_totales_anterior != 0:
                                        variacion_abs = (pasivos_totales - pasivos_totales_anterior)
                                        variacion = variacion_abs / abs(pasivos_totales_anterior)
                                        variacion = variacion * 100

                                        # Formatear a 2 decimales y agregar el signo
                                        signo = "+" if variacion > 0 else ""
                                        porcentaje_variacion = f"{signo}{variacion:.2f}%"
                        except Exception:
                            porcentaje_variacion = None

                        # Agregar pasivos totales al resultado
                        agrupadores_data.append({
                            "int_idTotalAgrupador": None,
                            "db_resultadoAgrupador": pasivos_totales,
                            "int_idAgrupador": None,
                            "nombre": "pasivos totales",
                            "str_porcentaje_variacion": porcentaje_variacion
                        })

                # Agregar este estado financiero al periodo
                if agrupadores_data:
                    ef_data = {
                        "int_idEstadoFinanciero": ef.int_idEstadoFinanciero,
                        "int_tipoPeriodo": ef.int_tipoPeriodo,
                        "int_tipoEstadoFinanciero": ef.int_tipoEstadoFinanciero,
                        "dt_fechaInicioPeriodo": ef.dt_fechaInicioPeriodo,
                        "dt_fechaFinPeriodo": ef.dt_fechaFinPeriodo,
                        "agrupadores": agrupadores_data
                    }
                    periodos[periodo_contable]["estados_financieros"].append(ef_data)

            # Convertir el diccionario de periodos a una lista
            resultado = [periodo for periodo in periodos.values() if periodo["estados_financieros"]]

            if not resultado:
                return MiddleResponse(
                    message=f"No se encontraron datos de agrupadores para la empresa con ID {id_empresa} y tipo de periodo {tipo_periodo}",
                    state=False
                )

            return MiddleResponse(
                message="Datos de agrupadores por periodo obtenidos exitosamente",
                data=resultado,
                state=True
            )

        except Exception as e:
            return MiddleResponse(
                message=f"Error al obtener los agrupadores por periodo: {str(e)}",
                state=False
            )

    def _formatear_periodo_contable(self, estado_financiero: EstadoFinanciero):
        """
        Formatea el periodo contable según el tipo de periodo.

        Args:
            estado_financiero (EstadoFinanciero): Objeto EstadoFinanciero

        Returns:
            str: Periodo contable formateado
        """
        if not estado_financiero.dt_fechaInicioPeriodo or not estado_financiero.dt_fechaFinPeriodo:
            return "Periodo sin fechas"

        fecha_inicio = estado_financiero.dt_fechaInicioPeriodo
        fecha_fin = estado_financiero.dt_fechaFinPeriodo

        # Formatear el periodo contable según el tipo
        # Verificar en Model EstadoFinanciero PERIODO_CHOICES
        # 1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario
        if estado_financiero.int_tipoPeriodo == 1:
            return f"Año {fecha_fin.year}"
        elif estado_financiero.int_tipoPeriodo == 2:
            # Determinar el trimestre basado en el mes de la fecha de fin
            trimestre = (fecha_fin.month - 1) // 3 + 1
            return f"Trimestre {trimestre} - {fecha_fin.year}"
        elif estado_financiero.int_tipoPeriodo == 3:
            # Formatear el mes y año usando el nombre del mes en español
            return f"{self._obtener_nombre_mes(fecha_fin.month)} {fecha_fin.year}"
        elif estado_financiero.int_tipoPeriodo == 4:
            # Formatear la semana
            return f"Semana {fecha_fin.isocalendar()[1]} - {fecha_fin.year}"
        elif estado_financiero.int_tipoPeriodo == 5:
            # Formatear la fecha completa
            return fecha_fin.strftime("%d/%m/%Y")
        else:
            return f"Periodo {fecha_inicio.strftime('%d/%m/%Y')} - {fecha_fin.strftime('%d/%m/%Y')}"

    def obtener_total_agrupador_periodo_especifico(self, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene los agrupadores totales para un periodo específico de una empresa.

        Args:
            id_empresa (int): ID de la empresa
            tipo_periodo (int): ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
            fecha_fin (str): Fecha de fin del periodo en formato DD-MM-YYYY

        Returns:
            Response: Objeto Response con los datos de los agrupadores para el periodo específico
        """
        try:
            # Mapear el tipo de periodo a los valores en la base de datos
            tipo_periodo_map = {
                1: "Anual",
                2: "Trimestral",
                3: "Mensual",
                4: "Semanal",
                5: "Diario"
            }

            # Validar el tipo de periodo
            if tipo_periodo not in tipo_periodo_map.keys():
                return MiddleResponse(
                    message=f"El tipo de periodo {tipo_periodo} no es válido. "
                    f"Los tipos de periodo válidos son: {', '.join(tipo_periodo_map.values())}",
                    state=False
                )

            # Convertir la fecha de fin a formato datetime
            try:
                fecha_fin_formateada = datetime.strptime(fecha_fin, '%d-%m-%Y')
            except ValueError:
                return MiddleResponse(
                    message=f"El formato de fecha {fecha_fin} no es válido. El formato debe ser DD-MM-YYYY",
                    state=False
                )

            # Verificar si existen estados financieros para la fecha especificada
            estados_financieros_fecha = EstadoFinanciero.objects.filter(
                int_idEmpresa=id_empresa,
                int_tipoPeriodo=tipo_periodo,
                int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                dt_fechaFinPeriodo=fecha_fin_formateada
            )

            if not estados_financieros_fecha.exists():
                return MiddleResponse(
                    message=f"No se encontraron estados financieros para la empresa con ID {id_empresa}, tipo de periodo {tipo_periodo} y fecha {fecha_fin}",
                    state=False
                )

            # Formatear la fecha de fin para mostrar en el resultado
            fecha_fin_formateada_str = fecha_fin_formateada.strftime('%d/%m/%Y')

            # Estructura para este periodo
            periodo_data = {
                "fecha_fin": fecha_fin_formateada_str,
                "estados_financieros": []
            }

            # Procesar cada estado financiero del periodo
            for ef_tipo in estados_financieros_fecha:
                # Obtener los totales de agrupadores para este estado financiero
                totales_agrupadores = TotalAgrupador.objects.filter(
                    int_idEstadoFinanciero=ef_tipo.int_idEstadoFinanciero
                )

                if not totales_agrupadores:
                    continue

                # Preparar la estructura de datos para los agrupadores
                agrupadores_data = []

                # Si es un Estado de Situación Financiera (tipo 1), calcular activos totales y pasivos totales
                activo_corriente = None
                activo_no_corriente = None
                pasivo_corriente = None
                pasivo_no_corriente = None

                # Procesar cada total de agrupador
                for total in totales_agrupadores:

                    nombre_agrupador = total.int_idAgrupador.str_nombre.lower()
                    nombre_subagrupador = total.int_idAgrupador.str_nombre_subagrupador.lower() if total.int_idAgrupador.str_nombre_subagrupador else None

                    # Guardar los valores de activos y pasivos para calcular totales después
                    if ef_tipo.int_tipoEstadoFinanciero == 1:  # Estado de Situación Financiera
                        # Buscar activo corriente
                        if "activo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                            activo_corriente = total.db_resultadoAgrupador

                        # Buscar activo no corriente
                        elif "activo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                            activo_no_corriente = total.db_resultadoAgrupador

                        # Buscar pasivo corriente
                        elif "pasivo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                            pasivo_corriente = total.db_resultadoAgrupador

                        # Buscar pasivo no corriente
                        elif "pasivo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                            pasivo_no_corriente = total.db_resultadoAgrupador

                    # Calcular la variación porcentual respecto al periodo anterior
                    porcentaje_variacion = None
                    if ef_tipo.int_tipoEstadoFinanciero in [1, 2]:  # Solo para estados financieros tipo 1 y 2
                        try:
                            # Obtener el estado financiero del periodo anterior
                            ef_anterior = self._obtener_estado_financiero_anterior(ef_tipo, tipo_periodo, id_empresa)

                            if ef_anterior:
                                # Buscar el mismo agrupador en el periodo anterior
                                total_anterior = TotalAgrupador.objects.get(
                                    int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero,
                                    int_idAgrupador=total.int_idAgrupador
                                )

                                # Calcular la variación porcentual
                                if total_anterior.db_resultadoAgrupador != 0:
                                    variacion_abs = (total.db_resultadoAgrupador - total_anterior.db_resultadoAgrupador)
                                    variacion = variacion_abs / abs(total_anterior.db_resultadoAgrupador)
                                    variacion = variacion * 100

                                    # Formatear a 2 decimales y agregar el signo
                                    signo = "+" if variacion > 0 else ""
                                    porcentaje_variacion = f"{signo}{variacion:.2f}%"
                                else:
                                    porcentaje_variacion = None
                        except TotalAgrupador.DoesNotExist:
                            porcentaje_variacion = None

                    # Crear el objeto de datos para este agrupador
                    agrupador_data = {
                        # Devolver el onmbre del subagrupador si existe, si no mostrar el nombre del agrupador
                        "nombre": total.int_idAgrupador.str_nombre_subagrupador if total.int_idAgrupador.str_nombre_subagrupador else total.int_idAgrupador.str_nombre,
                        "valor": float(total.db_resultadoAgrupador),
                        "variacion": porcentaje_variacion
                    }

                    agrupadores_data.append(agrupador_data)

                # Calcular y agregar activos totales y pasivos totales para estados financieros tipo 1
                if ef_tipo.int_tipoEstadoFinanciero == 1:  # Estado de Situación Financiera
                    # Calcular activos totales
                    if activo_corriente is not None and activo_no_corriente is not None:
                        activos_totales = activo_corriente + activo_no_corriente

                        # Calcular la variación porcentual para activos totales
                        porcentaje_variacion = None
                        try:
                            # Obtener el estado financiero del periodo anterior
                            ef_anterior = self._obtener_estado_financiero_anterior(ef_tipo, tipo_periodo, id_empresa)

                            if ef_anterior:
                                # Buscar los componentes en el periodo anterior
                                activo_corriente_anterior = None
                                activo_no_corriente_anterior = None

                                totales_anteriores = TotalAgrupador.objects.filter(
                                    int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero
                                )

                                for total_ant in totales_anteriores:
                                    nombre_agrupador = total_ant.int_idAgrupador.str_nombre.lower()
                                    nombre_subagrupador = total_ant.int_idAgrupador.str_nombre_subagrupador.lower() if total_ant.int_idAgrupador.str_nombre_subagrupador else None

                                    # Buscar activo corriente
                                    if "activo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                                        activo_corriente_anterior = total_ant.db_resultadoAgrupador

                                    # Buscar activo no corriente
                                    elif "activo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                                        activo_no_corriente_anterior = total_ant.db_resultadoAgrupador

                                if activo_corriente_anterior and activo_no_corriente_anterior:
                                    activos_totales_anterior = activo_corriente_anterior + activo_no_corriente_anterior

                                    if activos_totales_anterior != 0:
                                        variacion_abs = (activos_totales - activos_totales_anterior)
                                        variacion = variacion_abs / abs(activos_totales_anterior)
                                        variacion = variacion * 100

                                        # Formatear a 2 decimales y agregar el signo
                                        signo = "+" if variacion > 0 else ""
                                        porcentaje_variacion = f"{signo}{variacion:.2f}%"
                        except Exception:
                            porcentaje_variacion = None

                        # Agregar activos totales al resultado
                        agrupadores_data.append({
                            "nombre": "activos totales",
                            "valor": float(activos_totales),
                            "variacion": porcentaje_variacion
                        })

                    # Calcular pasivos totales
                    if pasivo_corriente is not None and pasivo_no_corriente is not None:
                        pasivos_totales = pasivo_corriente + pasivo_no_corriente

                        # Calcular la variación porcentual para pasivos totales
                        porcentaje_variacion = None
                        try:
                            # Obtener el estado financiero del periodo anterior
                            ef_anterior = self._obtener_estado_financiero_anterior(ef_tipo, tipo_periodo, id_empresa)

                            if ef_anterior:
                                # Buscar los componentes en el periodo anterior
                                pasivo_corriente_anterior = None
                                pasivo_no_corriente_anterior = None

                                totales_anteriores = TotalAgrupador.objects.filter(
                                    int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero
                                )

                                for total_ant in totales_anteriores:
                                    nombre_agrupador = total_ant.int_idAgrupador.str_nombre.lower()
                                    nombre_subagrupador = total_ant.int_idAgrupador.str_nombre_subagrupador.lower() if total_ant.int_idAgrupador.str_nombre_subagrupador else None

                                    # Buscar pasivo corriente
                                    if "pasivo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                                        pasivo_corriente_anterior = total_ant.db_resultadoAgrupador

                                    # Buscar pasivo no corriente
                                    elif "pasivo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                                        pasivo_no_corriente_anterior = total_ant.db_resultadoAgrupador

                                if pasivo_corriente_anterior and pasivo_no_corriente_anterior:
                                    pasivos_totales_anterior = pasivo_corriente_anterior + pasivo_no_corriente_anterior

                                    if pasivos_totales_anterior != 0:
                                        variacion_abs = (pasivos_totales - pasivos_totales_anterior)
                                        variacion = variacion_abs / abs(pasivos_totales_anterior)
                                        variacion = variacion * 100

                                        # Formatear a 2 decimales y agregar el signo
                                        signo = "+" if variacion > 0 else ""
                                        porcentaje_variacion = f"{signo}{variacion:.2f}%"
                        except Exception:
                            porcentaje_variacion = None

                        # Agregar pasivos totales al resultado
                        agrupadores_data.append({
                            "nombre": "pasivos totales",
                            "valor": float(pasivos_totales),
                            "variacion": porcentaje_variacion
                        })

                # Agregar este estado financiero al periodo
                if agrupadores_data:
                    ef_data = {
                        "tipo_estado_financiero": ef_tipo.int_tipoEstadoFinanciero,
                        "id_estado_financiero": ef_tipo.int_idEstadoFinanciero,
                        "agrupadores": agrupadores_data
                    }
                    periodo_data["estados_financieros"].append(ef_data)

            # Verificar si se encontraron datos
            if not periodo_data["estados_financieros"]:
                return MiddleResponse(
                    message=f"No se encontraron datos de agrupadores para la empresa con ID {id_empresa}, tipo de periodo {tipo_periodo} y fecha {fecha_fin}",
                    state=False
                )

            return MiddleResponse(
                message="Datos de agrupadores para el periodo específico obtenidos exitosamente",
                data=periodo_data,
                state=True
            )

        except Exception as e:
            return MiddleResponse(
                message=f"Error al obtener los agrupadores para el periodo específico: {str(e)}",
                state=False
            )

    def obtener_total_agrupador(self, id_empresa, tipo_periodo, fecha_fin):
        """
        Obtiene los agrupadores totales de los últimos 12 periodos para una empresa específica.

        Args:
            id_empresa (int): ID de la empresa
            tipo_periodo (int): ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
            fecha_fin (str): Fecha de fin del periodo en formato DD-MM-YYYY

        Returns:
            Response: Objeto Response con los datos de los agrupadores por periodo
        """
        try:
            # Mapear el tipo de periodo a los valores en la base de datos
            tipo_periodo_map = {
                1: "Anual",
                2: "Trimestral",
                3: "Mensual",
                4: "Semanal",
                5: "Diario"
            }

            # Validar el tipo de periodo
            if tipo_periodo not in tipo_periodo_map.keys():
                return MiddleResponse(
                    message=f"El tipo de periodo {tipo_periodo} no es válido. "
                    f"Los tipos de periodo válidos son: {', '.join(tipo_periodo_map.values())}",
                    state=False
                )

            # Convertir la fecha del formato DD-MM-YYYY al formato YYYY-MM-DD
            try:
                # Parsear la fecha en formato DD-MM-YYYY
                fecha_obj = datetime.strptime(fecha_fin, '%d-%m-%Y')
                # Convertir al formato YYYY-MM-DD para la consulta en la base de datos
                fecha_fin_formateada = fecha_obj.strftime('%Y-%m-%d')
            except ValueError:
                return MiddleResponse(
                    message="Formato de fecha inválido. Debe ser DD-MM-YYYY",
                    state=False
                )

            # Buscar los estados financieros para la fecha dada
            estados_financieros_fecha = EstadoFinanciero.objects.filter(
                int_idEmpresa=id_empresa,
                int_tipoPeriodo=tipo_periodo,
                int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                dt_fechaFinPeriodo=fecha_fin_formateada
            )

            if not estados_financieros_fecha.exists():
                return MiddleResponse(
                    message=f"No se encontraron estados financieros para la empresa con ID {id_empresa}, tipo de periodo {tipo_periodo} y fecha {fecha_fin}",
                    state=False
                )

            # Obtener las fechas únicas de los últimos 12 periodos (incluyendo el actual)
            fechas_periodos = EstadoFinanciero.objects.filter(
                int_idEmpresa=id_empresa,
                int_tipoPeriodo=tipo_periodo,
                int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                dt_fechaFinPeriodo__lte=fecha_fin_formateada  # Fecha igual o anterior a la actual
            ).values_list('dt_fechaFinPeriodo', flat=True).distinct().order_by('-dt_fechaFinPeriodo')[:12]  # Limitar a los últimos 12 periodos

            if not fechas_periodos:
                return MiddleResponse(
                    message=f"No se encontraron estados financieros para la empresa con ID {id_empresa} y tipo de periodo {tipo_periodo}",
                    state=False
                )

            resultado = []

            # Procesar cada periodo (fecha única)
            for fecha_periodo in fechas_periodos:
                # Formatear la fecha de fin para mostrar en el resultado
                fecha_fin_formateada = fecha_periodo.strftime('%d/%m/%Y')

                # Buscar todos los estados financieros para este periodo (pueden haber varios tipos)
                estados_financieros_periodo = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo=fecha_periodo
                )

                # Estructura para este periodo
                periodo_data = {
                    "fecha_fin": fecha_fin_formateada,
                    "estados_financieros": []
                }

                # Procesar cada estado financiero del periodo
                for ef_tipo in estados_financieros_periodo:
                    # Obtener los totales de agrupadores para este estado financiero
                    totales_agrupadores = TotalAgrupador.objects.filter(
                        int_idEstadoFinanciero=ef_tipo.int_idEstadoFinanciero
                    )

                    if not totales_agrupadores:
                        continue

                    # Preparar la estructura de datos para los agrupadores
                    agrupadores_data = []

                    # Si es un Estado de Situación Financiera (tipo 1), calcular activos totales y pasivos totales
                    activo_corriente = None
                    activo_no_corriente = None
                    pasivo_corriente = None
                    pasivo_no_corriente = None

                    # Primero recorremos para encontrar los valores de activos y pasivos
                    if ef_tipo.int_tipoEstadoFinanciero == 1:
                        for total in totales_agrupadores:

                            nombre_agrupador = total.int_idAgrupador.str_nombre.lower()
                            nombre_subagrupador = total.int_idAgrupador.str_nombre_subagrupador.lower() if total.int_idAgrupador.str_nombre_subagrupador else None

                            # Buscar activo corriente
                            if "activo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                                activo_corriente = total.db_resultadoAgrupador

                            # Buscar activo no corriente
                            elif "activo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                                activo_no_corriente = total.db_resultadoAgrupador

                            # Buscar pasivo corriente
                            elif "pasivo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                                pasivo_corriente = total.db_resultadoAgrupador

                            # Buscar pasivo no corriente
                            elif "pasivo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                                pasivo_no_corriente = total.db_resultadoAgrupador

                    # Ahora procesamos todos los agrupadores y agregamos los calculados
                    for total in totales_agrupadores:
                        # Buscar el estado financiero del periodo anterior para calcular la variación
                        ef_anterior = self._obtener_estado_financiero_anterior(ef_tipo, tipo_periodo, id_empresa)

                        # Calcular el porcentaje de variación si existe un periodo anterior
                        porcentaje_variacion = None
                        if ef_anterior:
                            # Buscar el total del agrupador en el periodo anterior
                            try:
                                total_anterior = TotalAgrupador.objects.get(
                                    int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero,
                                    int_idAgrupador=total.int_idAgrupador
                                )

                                # Calcular la variación porcentual
                                if total_anterior.db_resultadoAgrupador != 0:
                                    variacion_abs = (total.db_resultadoAgrupador - total_anterior.db_resultadoAgrupador)
                                    variacion = variacion_abs / abs(total_anterior.db_resultadoAgrupador)
                                    variacion = variacion * 100

                                    # Formatear a 2 decimales y agregar el signo
                                    signo = "+" if variacion > 0 else ""
                                    porcentaje_variacion = f"{signo}{variacion:.2f}%"
                                else:
                                    porcentaje_variacion = None
                            except TotalAgrupador.DoesNotExist:
                                porcentaje_variacion = None

                        # Crear el objeto de datos para este agrupador
                        agrupador_data = {
                            "nombre_agrupador": total.int_idAgrupador.str_nombre,
                            "nombre_subagrupador": total.int_idAgrupador.str_nombre_subagrupador,
                            "valor": float(total.db_resultadoAgrupador),
                            "variacion": porcentaje_variacion
                        }

                        agrupadores_data.append(agrupador_data)

                    # Agregar activos totales y pasivos totales si es un Estado de Situación Financiera
                    if ef_tipo.int_tipoEstadoFinanciero == 1:
                        # Calcular activos totales si tenemos los componentes
                        if activo_corriente is not None and activo_no_corriente is not None:
                            activos_totales = activo_corriente + activo_no_corriente

                            # Buscar el estado financiero del periodo anterior para calcular la variación
                            ef_anterior = self._obtener_estado_financiero_anterior(ef_tipo, tipo_periodo, id_empresa)

                            # Calcular el porcentaje de variación si existe un periodo anterior
                            porcentaje_variacion = None
                            if ef_anterior:
                                # Buscar los componentes en el periodo anterior
                                activo_corriente_anterior = None
                                activo_no_corriente_anterior = None

                                totales_anteriores = TotalAgrupador.objects.filter(
                                    int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero
                                )

                                for total_ant in totales_anteriores:
                                    nombre_agrupador = total_ant.int_idAgrupador.str_nombre.lower()
                                    nombre_subagrupador = total_ant.int_idAgrupador.str_nombre_subagrupador.lower() if total_ant.int_idAgrupador.str_nombre_subagrupador else None

                                    # Buscar activo corriente
                                    if "activo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                                        activo_corriente_anterior = total_ant.db_resultadoAgrupador

                                    # Buscar activo no corriente
                                    elif "activo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                                        activo_no_corriente_anterior = total_ant.db_resultadoAgrupador

                                # Calcular activos totales del periodo anterior
                                if activo_corriente_anterior is not None and activo_no_corriente_anterior is not None:
                                    activos_totales_anterior = activo_corriente_anterior + activo_no_corriente_anterior

                                    # Calcular la variación porcentual
                                    if activos_totales_anterior != 0:
                                        variacion_abs = (activos_totales - activos_totales_anterior)
                                        variacion = variacion_abs / abs(activos_totales_anterior)
                                        variacion = variacion * 100

                                        # Formatear a 2 decimales y agregar el signo
                                        signo = "+" if variacion > 0 else ""
                                        porcentaje_variacion = f"{signo}{variacion:.2f}%"

                            # Agregar activos totales al resultado
                            agrupadores_data.append({
                                "nombre_agrupador": "activos totales",
                                "nombre_subagrupador": None,
                                "valor": float(activos_totales),
                                "variacion": porcentaje_variacion
                            })

                        # Calcular pasivos totales si tenemos los componentes
                        if pasivo_corriente is not None and pasivo_no_corriente is not None:
                            pasivos_totales = pasivo_corriente + pasivo_no_corriente

                            # Buscar el estado financiero del periodo anterior para calcular la variación
                            ef_anterior = self._obtener_estado_financiero_anterior(ef_tipo, tipo_periodo, id_empresa)

                            # Calcular el porcentaje de variación si existe un periodo anterior
                            porcentaje_variacion = None
                            if ef_anterior:
                                # Buscar los componentes en el periodo anterior
                                pasivo_corriente_anterior = None
                                pasivo_no_corriente_anterior = None

                                totales_anteriores = TotalAgrupador.objects.filter(
                                    int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero
                                )

                                for total_ant in totales_anteriores:
                                    nombre_agrupador = total_ant.int_idAgrupador.str_nombre.lower()
                                    nombre_subagrupador = total_ant.int_idAgrupador.str_nombre_subagrupador.lower() if total_ant.int_idAgrupador.str_nombre_subagrupador else None

                                    # Buscar pasivo corriente
                                    if "pasivo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                                        pasivo_corriente_anterior = total_ant.db_resultadoAgrupador

                                    # Buscar pasivo no corriente
                                    elif "pasivo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                                        pasivo_no_corriente_anterior = total_ant.db_resultadoAgrupador

                                # Calcular pasivos totales del periodo anterior
                                if pasivo_corriente_anterior is not None and pasivo_no_corriente_anterior is not None:
                                    pasivos_totales_anterior = pasivo_corriente_anterior + pasivo_no_corriente_anterior

                                    # Calcular la variación porcentual
                                    if pasivos_totales_anterior != 0:
                                        variacion_abs = (pasivos_totales - pasivos_totales_anterior)
                                        variacion = variacion_abs / abs(pasivos_totales_anterior)
                                        variacion = variacion * 100

                                        # Formatear a 2 decimales y agregar el signo
                                        signo = "+" if variacion > 0 else ""
                                        porcentaje_variacion = f"{signo}{variacion:.2f}%"

                            # Agregar pasivos totales al resultado
                            agrupadores_data.append({
                                "nombre_agrupador": "pasivos totales",
                                "nombre_subagrupador": None,
                                "valor": float(pasivos_totales),
                                "variacion": porcentaje_variacion
                            })

                    # Agregar este estado financiero al periodo
                    if agrupadores_data:
                        ef_data = {
                            "tipo_estado_financiero": ef_tipo.int_tipoEstadoFinanciero,
                            "id_estado_financiero": ef_tipo.int_idEstadoFinanciero,
                            "agrupadores": agrupadores_data
                        }
                        periodo_data["estados_financieros"].append(ef_data)

                # Agregar este periodo al resultado si tiene estados financieros
                if periodo_data["estados_financieros"]:
                    resultado.append(periodo_data)

            return MiddleResponse(
                message="Datos de agrupadores totales obtenidos exitosamente",
                data=resultado,
                state=True
            )

        except Exception as e:
            return MiddleResponse(
                message=f"Error al obtener los agrupadores totales: {str(e)}",
                state=False
            )

    def obtener_agrupadores_simulacion(self, id_estado_financiero):
        """
        Obtiene los agrupadores para un estado financiero de simulación específico.

        Args:
            id_estado_financiero (int): ID del estado financiero de simulación

        Returns:
            MiddleResponse: Objeto Response con los datos de los agrupadores de la simulación
        """
        try:
            # Verificar que el estado financiero existe
            try:
                estado_financiero = EstadoFinanciero.objects.get(int_idEstadoFinanciero=id_estado_financiero)
            except ObjectDoesNotExist:
                return MiddleResponse(
                    message=f"No se encontró el estado financiero con ID {id_estado_financiero}",
                    state=False
                )

            # Verificar que sea una simulación
            if estado_financiero.int_tipoRegistro != 1:
                return MiddleResponse(
                    message=f"El estado financiero con ID {id_estado_financiero} no es una simulación",
                    state=False
                )

            # Obtener los totales de agrupadores para este estado financiero
            totales_agrupadores = TotalAgrupador.objects.filter(
                int_idEstadoFinanciero=id_estado_financiero
            )

            if not totales_agrupadores.exists():
                return MiddleResponse(
                    message=f"No se encontraron agrupadores para el estado financiero con ID {id_estado_financiero}",
                    state=False
                )

            # Preparar la estructura de datos para los agrupadores
            agrupadores_data = []

            # Procesar cada total de agrupador
            for total in totales_agrupadores:
                # Crear el objeto de datos para este agrupador
                agrupador_data = {
                    "nombre_agrupador": total.int_idAgrupador.str_nombre,
                    "nombre_subagrupador": total.int_idAgrupador.str_nombre_subagrupador,
                    "valor": float(total.db_resultadoAgrupador)
                }

                agrupadores_data.append(agrupador_data)

            return MiddleResponse(
                message="Datos de agrupadores para la simulación obtenidos exitosamente",
                data=agrupadores_data,
                state=True
            )

        except Exception as e:
            return MiddleResponse(
                message=f"Error al obtener los agrupadores para la simulación: {str(e)}",
                state=False
            )

    def obtener_agrupadores_por_mes(self, id_empresa, id_mes):
        """
        Obtiene los agrupadores por periodo mensual para una empresa específica,
        filtrando por un mes específico y mostrando los últimos 12 años.

        Args:
            id_empresa (int): ID de la empresa
            id_mes (int): Número del mes (1-12)

        Returns:
            Response: Objeto Response con los datos de los agrupadores por periodo
        """
        try:
            # Validar el mes
            if id_mes < 1 or id_mes > 12:
                return MiddleResponse(
                    message=f"El mes {id_mes} no es válido. Debe ser un número entre 1 y 12.",
                    state=False
                )

            # Obtener los estados financieros mensuales para la empresa
            estados_financieros = EstadoFinanciero.objects.filter(
                int_idEmpresa=id_empresa,
                int_tipoPeriodo=3,  # Mensual
                int_tipoRegistro=0  # Solo periodos contables, no simulaciones
            ).order_by('-dt_fechaFinPeriodo')  # Ordenar por fecha de fin de periodo descendente

            if not estados_financieros:
                return MiddleResponse(
                    message=f"No se encontraron estados financieros mensuales para la empresa con ID {id_empresa}",
                    state=False
                )

            # Filtrar por el mes específico
            estados_financieros_mes = [
                ef for ef in estados_financieros if ef.dt_fechaFinPeriodo.month == id_mes
            ]

            if not estados_financieros_mes:
                return MiddleResponse(
                    message=f"No se encontraron estados financieros para el mes {id_mes} en la empresa con ID {id_empresa}",
                    state=False
                )

            # Agrupar por año (para obtener los últimos 12 años)
            años_disponibles = sorted(list(set([ef.dt_fechaFinPeriodo.year for ef in estados_financieros_mes])), reverse=True)

            # Limitar a los últimos 12 años
            años_a_mostrar = años_disponibles[:12] if len(años_disponibles) > 12 else años_disponibles

            resultado = []

            # Procesar cada año
            for año in años_a_mostrar:
                # Filtrar estados financieros para este año y mes
                efs_año_mes = [ef for ef in estados_financieros_mes if ef.dt_fechaFinPeriodo.year == año]

                # Estructura para este periodo
                periodo_data = {
                    "periodo_contable": f"{self._obtener_nombre_mes(id_mes)} {año}",
                    "estados_financieros": []
                }

                # Procesar cada estado financiero de este periodo
                for ef in efs_año_mes:
                    # Obtener los totales de agrupadores para este estado financiero
                    totales_agrupadores = TotalAgrupador.objects.filter(
                        int_idEstadoFinanciero=ef.int_idEstadoFinanciero
                    )

                    if not totales_agrupadores:
                        continue

                    # Preparar la estructura de datos para los agrupadores
                    agrupadores_data = []

                    # Si es un Estado de Situación Financiera (tipo 1), inicializar variables para calcular totales
                    activo_corriente = None
                    activo_no_corriente = None
                    pasivo_corriente = None
                    pasivo_no_corriente = None

                    for total in totales_agrupadores:
                        nombre_agrupador = total.int_idAgrupador.str_nombre.lower()
                        nombre_subagrupador = total.int_idAgrupador.str_nombre_subagrupador.lower() if total.int_idAgrupador.str_nombre_subagrupador else None

                        # Guardar los valores de activos y pasivos para calcular totales después
                        if ef.int_tipoEstadoFinanciero == 1:  # Estado de Situación Financiera
                            # Buscar activo corriente
                            if "activo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                                activo_corriente = total.db_resultadoAgrupador

                            # Buscar activo no corriente
                            elif "activo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                                activo_no_corriente = total.db_resultadoAgrupador

                            # Buscar pasivo corriente
                            elif "pasivo" in nombre_agrupador and nombre_subagrupador and "corriente" in nombre_subagrupador and "no" not in nombre_subagrupador:
                                pasivo_corriente = total.db_resultadoAgrupador

                            # Buscar pasivo no corriente
                            elif "pasivo" in nombre_agrupador and nombre_subagrupador and "no corriente" in nombre_subagrupador:
                                pasivo_no_corriente = total.db_resultadoAgrupador
                        # Buscar el estado financiero del mismo mes del año anterior para calcular la variación
                        ef_anterior = self._obtener_estado_financiero_año_anterior(ef, id_empresa)

                        # Calcular el porcentaje de variación si existe un periodo anterior
                        porcentaje_variacion = None
                        if ef_anterior:
                            # Buscar el total del agrupador en el periodo anterior
                            try:
                                total_anterior = TotalAgrupador.objects.get(
                                    int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero,
                                    int_idAgrupador=total.int_idAgrupador
                                )

                                # Calcular la variación porcentual
                                if total_anterior.db_resultadoAgrupador != 0:
                                    variacion_abs = (total.db_resultadoAgrupador - total_anterior.db_resultadoAgrupador)
                                    variacion = variacion_abs / abs(total_anterior.db_resultadoAgrupador)
                                    variacion = variacion * 100

                                    # Formatear a 2 decimales y agregar el signo
                                    signo = "+" if variacion > 0 else ""
                                    porcentaje_variacion = f"{signo}{variacion:.2f}%"
                                else:
                                    porcentaje_variacion = None
                            except TotalAgrupador.DoesNotExist:
                                porcentaje_variacion = None

                        # Determinar el nombre a mostrar: subagrupador si existe, sino el nombre principal
                        nombre_mostrar = total.int_idAgrupador.str_nombre_subagrupador if total.int_idAgrupador.str_nombre_subagrupador else total.int_idAgrupador.str_nombre

                        # Crear el objeto de datos para este agrupador
                        agrupador_data = {
                            "int_idTotalAgrupador": total.int_idTotalAgrupador,
                            "db_resultadoAgrupador": total.db_resultadoAgrupador,
                            "int_idAgrupador": total.int_idAgrupador.int_idAgrupador,
                            "nombre": nombre_mostrar,
                            "str_porcentaje_variacion": porcentaje_variacion
                        }

                        agrupadores_data.append(agrupador_data)

                    # Calcular y agregar activos totales y pasivos totales para estados financieros tipo 1
                    if ef.int_tipoEstadoFinanciero == 1:  # Estado de Situación Financiera
                        # Calcular activos totales
                        if activo_corriente is not None and activo_no_corriente is not None:
                            activos_totales = activo_corriente + activo_no_corriente

                            # Calcular la variación porcentual para activos totales
                            porcentaje_variacion = None
                            try:
                                # Obtener el estado financiero del año anterior
                                ef_anterior = self._obtener_estado_financiero_año_anterior(ef, id_empresa)

                                if ef_anterior:
                                    # Buscar los componentes en el periodo anterior
                                    activo_corriente_anterior = None
                                    activo_no_corriente_anterior = None

                                    totales_anteriores = TotalAgrupador.objects.filter(
                                        int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero
                                    )

                                    for total_ant in totales_anteriores:
                                        nombre_agrupador_ant = total_ant.int_idAgrupador.str_nombre.lower()
                                        nombre_subagrupador_ant = total_ant.int_idAgrupador.str_nombre_subagrupador.lower() if total_ant.int_idAgrupador.str_nombre_subagrupador else None

                                        # Buscar activo corriente
                                        if "activo" in nombre_agrupador_ant and nombre_subagrupador_ant and "corriente" in nombre_subagrupador_ant and "no" not in nombre_subagrupador_ant:
                                            activo_corriente_anterior = total_ant.db_resultadoAgrupador

                                        # Buscar activo no corriente
                                        elif "activo" in nombre_agrupador_ant and nombre_subagrupador_ant and "no corriente" in nombre_subagrupador_ant:
                                            activo_no_corriente_anterior = total_ant.db_resultadoAgrupador

                                    if activo_corriente_anterior is not None and activo_no_corriente_anterior is not None:
                                        activos_totales_anterior = activo_corriente_anterior + activo_no_corriente_anterior

                                        if activos_totales_anterior != 0:
                                            variacion_abs = (activos_totales - activos_totales_anterior)
                                            variacion = variacion_abs / abs(activos_totales_anterior)
                                            variacion = variacion * 100

                                            # Formatear a 2 decimales y agregar el signo
                                            signo = "+" if variacion > 0 else ""
                                            porcentaje_variacion = f"{signo}{variacion:.2f}%"
                            except Exception:
                                porcentaje_variacion = None

                            # Agregar activos totales al resultado
                            agrupadores_data.append({
                                "int_idTotalAgrupador": None,
                                "db_resultadoAgrupador": activos_totales,
                                "int_idAgrupador": None,
                                "nombre": "activos totales",
                                "str_porcentaje_variacion": porcentaje_variacion
                            })

                        # Calcular pasivos totales
                        if pasivo_corriente is not None and pasivo_no_corriente is not None:
                            pasivos_totales = pasivo_corriente + pasivo_no_corriente

                            # Calcular la variación porcentual para pasivos totales
                            porcentaje_variacion = None
                            try:
                                # Obtener el estado financiero del año anterior
                                ef_anterior = self._obtener_estado_financiero_año_anterior(ef, id_empresa)

                                if ef_anterior:
                                    # Buscar los componentes en el periodo anterior
                                    pasivo_corriente_anterior = None
                                    pasivo_no_corriente_anterior = None

                                    totales_anteriores = TotalAgrupador.objects.filter(
                                        int_idEstadoFinanciero=ef_anterior.int_idEstadoFinanciero
                                    )

                                    for total_ant in totales_anteriores:
                                        nombre_agrupador_ant = total_ant.int_idAgrupador.str_nombre.lower()
                                        nombre_subagrupador_ant = total_ant.int_idAgrupador.str_nombre_subagrupador.lower() if total_ant.int_idAgrupador.str_nombre_subagrupador else None

                                        # Buscar pasivo corriente
                                        if "pasivo" in nombre_agrupador_ant and nombre_subagrupador_ant and "corriente" in nombre_subagrupador_ant and "no" not in nombre_subagrupador_ant:
                                            pasivo_corriente_anterior = total_ant.db_resultadoAgrupador

                                        # Buscar pasivo no corriente
                                        elif "pasivo" in nombre_agrupador_ant and nombre_subagrupador_ant and "no corriente" in nombre_subagrupador_ant:
                                            pasivo_no_corriente_anterior = total_ant.db_resultadoAgrupador

                                    if pasivo_corriente_anterior is not None and pasivo_no_corriente_anterior is not None:
                                        pasivos_totales_anterior = pasivo_corriente_anterior + pasivo_no_corriente_anterior

                                        if pasivos_totales_anterior != 0:
                                            variacion_abs = (pasivos_totales - pasivos_totales_anterior)
                                            variacion = variacion_abs / abs(pasivos_totales_anterior)
                                            variacion = variacion * 100

                                            # Formatear a 2 decimales y agregar el signo
                                            signo = "+" if variacion > 0 else ""
                                            porcentaje_variacion = f"{signo}{variacion:.2f}%"
                            except Exception:
                                porcentaje_variacion = None

                            # Agregar pasivos totales al resultado
                            agrupadores_data.append({
                                "int_idTotalAgrupador": None,
                                "db_resultadoAgrupador": pasivos_totales,
                                "int_idAgrupador": None,
                                "nombre": "pasivos totales",
                                "str_porcentaje_variacion": porcentaje_variacion
                            })

                    # Agregar este estado financiero al periodo
                    if agrupadores_data:
                        ef_data = {
                            "int_idEstadoFinanciero": ef.int_idEstadoFinanciero,
                            "int_tipoPeriodo": ef.int_tipoPeriodo,
                            "int_tipoEstadoFinanciero": ef.int_tipoEstadoFinanciero,
                            "dt_fechaInicioPeriodo": ef.dt_fechaInicioPeriodo,
                            "dt_fechaFinPeriodo": ef.dt_fechaFinPeriodo,
                            "agrupadores": agrupadores_data
                        }
                        periodo_data["estados_financieros"].append(ef_data)

                # Agregar este periodo al resultado si tiene estados financieros
                if periodo_data["estados_financieros"]:
                    resultado.append(periodo_data)

            if not resultado:
                return MiddleResponse(
                    message=f"No se encontraron datos de agrupadores para el mes {id_mes} en la empresa con ID {id_empresa}",
                    state=False
                )

            return MiddleResponse(
                message="Datos de agrupadores por periodo mensual obtenidos exitosamente",
                data=resultado,
                state=True
            )

        except Exception as e:
            return MiddleResponse(
                message=f"Error al obtener los agrupadores por periodo mensual: {str(e)}",
                state=False
            )

    def _obtener_nombre_mes(self, numero_mes):
        """
        Obtiene el nombre del mes a partir de su número.

        Args:
            numero_mes (int): Número del mes (1-12)

        Returns:
            str: Nombre del mes
        """
        nombres_meses = {
            1: "Enero",
            2: "Febrero",
            3: "Marzo",
            4: "Abril",
            5: "Mayo",
            6: "Junio",
            7: "Julio",
            8: "Agosto",
            9: "Septiembre",
            10: "Octubre",
            11: "Noviembre",
            12: "Diciembre"
        }
        return nombres_meses.get(numero_mes, "Mes desconocido")

    def _obtener_estado_financiero_anterior(self, estado_financiero: EstadoFinanciero, tipo_periodo, id_empresa):
        """
        Obtiene el estado financiero del periodo anterior según el tipo de periodo.
        Por ejemplo, si es mensual, obtiene el mes anterior; si es anual, obtiene el año anterior.

        Args:
            estado_financiero (EstadoFinanciero): Estado financiero actual
            tipo_periodo (int): ID del Tipo de periodo (1: anual, 2: trimestral, 3: mensual, 4: semanal, 5: diario)
            id_empresa (int): ID de la empresa

        Returns:
            EstadoFinanciero: Estado financiero del periodo anterior o None si no existe
        """
        try:
            fecha_actual = estado_financiero.dt_fechaFinPeriodo

            # Determinar la fecha del periodo anterior según el tipo de periodo
            if tipo_periodo == 1:  # Anual
                # Para periodos anuales, buscar el año anterior
                año_anterior = fecha_actual.year - 1
                # Filtrar por el año anterior manteniendo el mismo mes y día
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                    dt_fechaFinPeriodo__year=año_anterior,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero  # Mismo tipo de estado financiero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 2:  # Trimestral
                # Para periodos trimestrales, buscar el trimestre anterior
                # Determinar el trimestre actual (1-4)
                trimestre_actual = (fecha_actual.month - 1) // 3 + 1
                año = fecha_actual.year

                if trimestre_actual == 1:
                    # Si es el primer trimestre, el anterior es el cuarto del año pasado
                    trimestre_anterior = 4
                    año = año - 1
                else:
                    # Si no, es el trimestre anterior del mismo año
                    trimestre_anterior = trimestre_actual - 1

                # Calcular el mes de inicio del trimestre anterior
                mes_inicio = (trimestre_anterior - 1) * 3 + 1
                # Calcular el mes de fin del trimestre anterior
                mes_fin = trimestre_anterior * 3

                # Filtrar por el trimestre anterior
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=año,
                    dt_fechaFinPeriodo__month__gte=mes_inicio,
                    dt_fechaFinPeriodo__month__lte=mes_fin,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 3:  # Mensual
                # Para periodos mensuales, buscar el mes anterior
                if fecha_actual.month == 1:
                    # Si es enero, el mes anterior es diciembre del año pasado
                    mes_anterior = 12
                    año_anterior = fecha_actual.year - 1
                else:
                    # Si no, es el mes anterior del mismo año
                    mes_anterior = fecha_actual.month - 1
                    año_anterior = fecha_actual.year

                # Filtrar por el mes anterior
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=año_anterior,
                    dt_fechaFinPeriodo__month=mes_anterior,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 4:  # Semanal
                # Para periodos semanales, buscar la semana anterior
                # Restar 7 días a la fecha actual
                fecha_anterior = fecha_actual - timedelta(days=7)

                # Filtrar por la semana anterior (aproximadamente)
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__year=fecha_anterior.year,
                    dt_fechaFinPeriodo__month=fecha_anterior.month,
                    dt_fechaFinPeriodo__day__gte=max(1, fecha_anterior.day - 3),  # Aproximación
                    dt_fechaFinPeriodo__day__lte=min(31, fecha_anterior.day + 3),  # Aproximación
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            elif tipo_periodo == 5:  # Diario
                # Para periodos diarios, buscar el día anterior
                fecha_anterior = fecha_actual - timedelta(days=1)

                # Filtrar por el día anterior
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo=fecha_anterior,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            else:
                # Para cualquier otro tipo de periodo, usar el método original
                efs_anteriores = EstadoFinanciero.objects.filter(
                    int_idEmpresa=id_empresa,
                    int_tipoPeriodo=tipo_periodo,
                    int_tipoRegistro=0,
                    dt_fechaFinPeriodo__lt=fecha_actual,
                    int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero
                ).order_by('-dt_fechaFinPeriodo')

            # Retornar el primer resultado si existe
            if efs_anteriores.exists():
                return efs_anteriores.first()
            return None

        except Exception as e:
            print(f"Error al obtener estado financiero anterior: {str(e)}")
            return None

    def _obtener_estado_financiero_año_anterior(self, estado_financiero: EstadoFinanciero, id_empresa):
        """
        Obtiene el estado financiero del mismo mes pero del año anterior.
        Específicamente para el endpoint de agrupadores por mes.

        Args:
            estado_financiero (EstadoFinanciero): Estado financiero actual
            id_empresa (int): ID de la empresa

        Returns:
            EstadoFinanciero: Estado financiero del mismo mes del año anterior o None si no existe
        """
        try:
            # Obtener el mes y día de la fecha actual
            fecha_actual = estado_financiero.dt_fechaFinPeriodo
            mes_actual = fecha_actual.month

            # Calcular el año anterior
            año_anterior = fecha_actual.year - 1

            # Buscar estados financieros del mismo mes pero del año anterior
            efs_año_anterior = EstadoFinanciero.objects.filter(
                int_idEmpresa=id_empresa,
                int_tipoPeriodo=3,  # Mensual (asumimos que siempre es mensual para este caso)
                int_tipoRegistro=0,  # Solo periodos contables, no simulaciones
                dt_fechaFinPeriodo__year=año_anterior,
                dt_fechaFinPeriodo__month=mes_actual,
                int_tipoEstadoFinanciero=estado_financiero.int_tipoEstadoFinanciero  # Mismo tipo de estado financiero
            ).order_by('-dt_fechaFinPeriodo')

            # Retornar el primero si existe
            if efs_año_anterior.exists():
                return efs_año_anterior.first()
            return None
        except Exception:
            return None