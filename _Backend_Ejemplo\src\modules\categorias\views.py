from django.shortcuts import render
from rest_framework import viewsets
from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema

from src.utils.classes import Response as APIResponse
from .models import Categoria
from .serializers import CategoriaSerializer
from .controller import Categoria<PERSON>ontroller
# Create your views here.

class CategoriaView(viewsets.ModelViewSet):
    queryset = Categoria.objects.all()
    serializer_class = CategoriaSerializer
    permission_classes = [permissions.AllowAny]
    controller = CategoriaController()
    http_method_names = ["get", "post", "patch"]

    @swagger_auto_schema(
        operation_description="Endpoint para obtener categorias por suscripción",
    )
    @action(detail=False, methods=["get"], url_path="suscripcion/<str:suscripcion_id>")
    def categorias_by_suscripcion(self, request, suscripcion_id, *args, **kwargs):
        try:
            # serializer = CategoriaSerializer(data=request.data)
            # serializer.is_valid(raise_exception=True)
            # serializer.validated_data
            response: APIResponse = self.controller.get_by_suscripcion(suscripcion_id)
            if not response.state:
                return Response(
                    data=response.message, status=status.HTTP_400_BAD_REQUEST
                )
            return Response(
                data=CategoriaSerializer(response.data, many=True).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(data=str(e), status=status.HTTP_400_BAD_REQUEST)