# Generated by Django 5.1 on 2025-03-31 17:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('detalles_evaluacion', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Eviencia',
            fields=[
                ('int_idEvidencia', models.AutoField(primary_key=True, serialize=False)),
                ('str_nombre', models.Char<PERSON>ield(max_length=255)),
                ('str_peso', models.Char<PERSON><PERSON>(max_length=255)),
                ('str_ruta', models.<PERSON>r<PERSON>ield(max_length=255)),
                ('str_extension', models.Char<PERSON>ield(max_length=255)),
                ('int_idDetalleEvaluacion', models.ForeignKey(db_column='int_idDetalleEvaluacion', on_delete=django.db.models.deletion.CASCADE, related_name='evidencias', to='detalles_evaluacion.detalleevaluacion')),
            ],
            options={
                'verbose_name': 'evidencia',
                'verbose_name_plural': 'evidencias',
                'db_table': 'tr_evidencias',
                'managed': True,
            },
        ),
    ]
