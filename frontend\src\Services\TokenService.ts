import axios from 'axios'
import Cookies from 'js-cookie'
import CryptoJ<PERSON> from 'crypto-js'

const base_url = import.meta.env.VITE_SEGURIDAD_URL



const encryptionKey = import.meta.env.VITE_ENCRYPTION_KEY;
const baseRedireccionar = import.meta.env.VITE_ENCRYPTION_KEY;

export const encrypt = (data: string) =>
  CryptoJS.AES.encrypt(data, encryptionKey).toString();

export const decrypt = (ciphertext: string) => {
  try {
    const bytes = CryptoJS.AES.decrypt(ciphertext, encryptionKey);
    return bytes.toString(CryptoJS.enc.Utf8);
  } catch (e) {
     
  }
};

export const validateToken = async (): Promise<any> => {
  const updateLastExecutionTime = () => {
    const now = new Date().toISOString();
    localStorage.setItem('lastExecutionTime', now);
  };
  const token = Cookies.get('TokenFinanciera') 
  const refresh_token =  Cookies.get('refreshTokenFinanciera')

  try {
    const sesion =  decrypt(Cookies.get("sesionFinanciera"))
    const response = await axios.get(
      base_url + `seguridad/validar/sesion/${sesion}/`,

      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    )
    if (response.status === 200) {
      updateLastExecutionTime()
      return true
    }
  } catch (error) {
    console.log(error)
    const refreshResponse = await axios.get(
      base_url + `seguridad/token/`,
 
      {
        headers: {
          Authorization: `Bearer ${refresh_token}`
        }
      }
    )

    if (refreshResponse.status >= 200 && refreshResponse.status < 300) {
      const data = refreshResponse.data
      Cookies.set('TokenFinanciera', data.token)
      updateLastExecutionTime()
      return true
    }
    updateLastExecutionTime()
    return false
  }
}

export const updateSesion = async (idSession:any) => {
  await validateToken()
  try {
    const response = await axios.post(
      base_url + 'usuarios/sesion_update_token/' + idSession + '/',
      {
        token: await get_transfer_token()
      },
      {
        headers: {
          Authorization: `Bearer ${getTokenFromCookie()}`
        }
      }
    )
    return response
  } catch (e:any) {
    return e
  }
}

export const get_transfer_token = async () => {

  await validateToken()
      const id_sesion =   decrypt(Cookies.get("sesionFinanciera"));

  try {
    const response = await axios.get(
      base_url + 'seguridad/transferir/' + id_sesion + '/',
      {
        headers: {
          Authorization: `Bearer ${getTokenFromCookie()}`
        }
      }
    )
    if (response.status === 200) {
      return response.data
    }
  } catch (e: any) {
    return
  }
}

export const checkSesion = async (idSession:any) => {
  await validateToken()
  const response = await axios.get(
    base_url + `seguridad/validar/sesion/${idSession}/`,
    {
      headers: {
        Authorization: `Bearer ${getTokenFromCookie()}`
      }
    }
  )
  return response
}

export const getTokenFromCookie = () => {

  const token = Cookies.get('TokenFinanciera')

  return token
}
export const logout = async () => {
  try {
    const id_sesion =   decrypt(Cookies.get("sesionFinanciera"));
    await fetch(`${base_url}usuarios/logout/`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${getTokenFromCookie()}`,
      },
      body: JSON.stringify({ id_sesiones: id_sesion }),
    });

    // Limpiar localStorage y todas las cookies
    localStorage.clear();
    Object.keys(Cookies.get()).forEach((cookie) => Cookies.remove(cookie));

    window.location.replace(
      `${baseRedireccionar}auth?logout=1`
    );
  } catch (e) {
    localStorage.clear();
    Object.keys(Cookies.get()).forEach((cookie) => Cookies.remove(cookie));
  }
};