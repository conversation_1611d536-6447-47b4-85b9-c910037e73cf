from django.db import models

from src.modules.evaluaciones.models import Evaluacion
from src.modules.usuarios.models import Usuario

# Create your models here.
class Evaluado(models.Model):
    class Meta:
        db_table = 'tr_evaluados'
        managed = True
        verbose_name = 'evaluado'
        verbose_name_plural = 'evaluados'
        
    int_idEvaluado = models.AutoField(primary_key=True)
    int_idUsuarios = models.ForeignKey(Usuario, on_delete=models.CASCADE, db_column='int_idUsuarios', related_name='evaluados')
    int_idEvaluacion = models.ForeignKey(Evaluacion, on_delete=models.CASCADE, db_column='int_idEvaluacion', related_name='evaluados')
    int_idTipoEvaluado = models.IntegerField()
    bool_estado = models.BooleanField(null=True, default=False)
