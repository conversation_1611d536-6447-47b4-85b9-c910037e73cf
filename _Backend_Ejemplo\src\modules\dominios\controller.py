from src.modules.dominios.models import Dominio
from src.utils.classes import Response


class DominioController:
    def __init__(self): ...

    def get_by_framework(self, framework_id: int):
        try:
            dominios = Dominio.objects.filter(
                int_idFramework_id=framework_id
            ).all()
            if not dominios:
                return Response("No se encontraron dominios", state=False)
            return Response(data=dominios, state=True)

        except Exception as e:
            return Response(str(e), e)