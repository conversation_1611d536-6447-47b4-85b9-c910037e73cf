# Generated by Django 5.2.1 on 2025-05-14 12:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('estado_financiero', '0001_initial'),
        ('ratios', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RatioEF',
            fields=[
                ('int_idRatioEF', models.AutoField(primary_key=True, serialize=False)),
                ('db_valorRatio', models.DecimalField(decimal_places=2, max_digits=18)),
                ('int_idEstadoFinanciero', models.ForeignKey(db_column='int_idEstadoFinanciero', on_delete=django.db.models.deletion.CASCADE, related_name='ratios_ef', to='estado_financiero.estadofinanciero')),
                ('int_idRatios', models.ForeignKey(db_column='int_idRatios', on_delete=django.db.models.deletion.CASCADE, related_name='ratios_ef', to='ratios.ratio')),
            ],
            options={
                'verbose_name': 'ratio ef',
                'verbose_name_plural': 'ratios ef',
                'db_table': 'tr_ratiosef',
                'managed': True,
            },
        ),
    ]
