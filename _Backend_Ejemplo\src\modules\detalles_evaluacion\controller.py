from src.modules.evaluados.models import Evaluado
from src.utils.classes import Response
from src.modules.detalles_evaluacion.models import DetalleEvaluacion
from .serializers import DetallesEvaluacionSerializer # Importar serializer
from src.modules.evaluaciones.models import Evaluacion
from src.modules.dominios.models import Dominio
from src.modules.sub_dominios.models import SubDominio
from src.modules.controles.models import Control
from src.modules.controles.serializers import ControlSerializer

class DetalleController:
    def __init__(self):
        ...
        
    def crear_detalles(self, evaluado: Evaluado):
        try:
            print(f"Creando detalles para el Evaluado ID {evaluado.int_idEvaluado}")
            evaluacion = Evaluacion.objects.filter(int_idEvaluacion=evaluado.int_idEvaluacion.int_idEvaluacion).first()
            framework_id = evaluacion.int_idFramework.int_idFramework

            dominios_ids = Dominio.objects.filter(int_idFramework_id=framework_id).values_list('int_idDominio', flat=True)
            if not dominios_ids:
                 return Response(f"No se encontraron dominios para el Framework ID {framework_id}", state=False)
            
            subdominios_ids = SubDominio.objects.filter(int_idDominio_id__in=dominios_ids).values_list('int_idSubDominio', flat=True)
            if not subdominios_ids.exists():
                return Response(f"No se encontraron subdominios para los Dominios ID {dominios_ids}", state=False)
            
            controles = Control.objects.filter(int_idSubDominio_id__in=subdominios_ids)
            if not controles.exists():
                return Response(f"No se encontraron controles para los Subdominios ID {subdominios_ids}", state=False)

            # print(evaluacion.int_idFramework.dominios.sub_dominios.controles.all())
            
            # Iterar directamente sobre el QuerySet de objetos Control
            for control_obj in controles: # 'control_obj' es una instancia de Control
                # Crear el primer detalle (tipo 1)
                print(f"Creando detalle para el Control ID {control_obj.int_idControl}")
                DetalleEvaluacion.objects.create(
                    int_idControl = control_obj,
                    int_idEvaluacion = evaluacion,
                    int_idEvaluado = evaluado,
                    str_valor_tobe= None,
                    str_valor_asis= None,
                )
        except Exception as e:
            print(e)
            return Response(str(e), e)
        
    def cambiar_estado(self, detalle_id: int):
        """Cambia el estado (activo/inactivo) de un Detalle de Evaluacion.

        Args:
            detalle_id (int): ID del detalle de evaluacion a modificar.

        Returns:
            Response: Respuesta indicando el resultado de la operación.
        """
        try:
            detalle = DetalleEvaluacion.objects.get(int_idDetalleEvaluacion=detalle_id)
            # Cambia el estado booleano
            detalle.bool_estado = not detalle.bool_estado
            detalle.save()
            nuevo_estado = "activo" if detalle.bool_estado else "inactivo"
            return Response(
                f"Estado del framework cambiado a {nuevo_estado}", state=True
            )
        except detalle.DoesNotExist:
            return Response(
                f"Detalle con ID {detalle_id} no encontrado", state=False
            )
        except Exception as e:
            # Considerar registrar el error aquí para depuración
            return Response(f"Error al cambiar estado: {str(e)}", state=False)
        
    
    def get_by_subdominio(self, subdominio_id: int):
        """Obtener los Detalles de Evaluacion por subdominio.

        Args:
            subdominio_id (int): ID del subdomino.

        Returns:
            Response: Respuesta indicando el resultado de la operación.
        """
        try:
            # Obtener lista plana de IDs de control para el subdominio
            controles_ids = Control.objects.filter(int_idSubDominio_id=subdominio_id).values_list('int_idControl', flat=True)

            if not controles_ids:
                 return Response(f"No se encontraron controles para el subdominio ID {subdominio_id}", state=False)

            # Filtrar Detalles de Evaluacion usando los IDs de control
            detalles_qs = DetalleEvaluacion.objects.filter(int_idControl_id__in=controles_ids)

            if not detalles_qs.exists():
                return Response(f"No se encontraron detalles de evaluación para los controles del subdominio ID {subdominio_id}", state=False)

            # Serializar el QuerySet
            serializer = DetallesEvaluacionSerializer(detalles_qs, many=True)

            # Devolver los datos serializados
            return Response(data=serializer.data, state=True)

        # except Control.DoesNotExist: # .filter().values_list() no lanza DoesNotExist
        #      return Response(f"Subdominio con ID {subdominio_id} no encontrado o no tiene controles asociados.", state=False)
        except Exception as e:
            # Asegurarse de que siempre se pase una cadena como mensaje de error
            error_message = f"Error inesperado al obtener detalles por subdominio: {str(e)}"
            # Considerar registrar el error 'e' aquí para una mejor depuración (e.g., import logging; logging.exception(error_message))
            return Response(error_message, state=False)
