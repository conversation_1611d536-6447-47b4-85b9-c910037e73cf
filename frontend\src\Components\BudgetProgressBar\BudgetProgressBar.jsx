import React from "react";

const BudgetProgressBar = ({
  totalBudget,
  usedBudget,
  showDetails = true,
}) => {
  const percentage = Math.min(Math.round((usedBudget / totalBudget) * 100), 100);
  
  const formatNumber = (number) => {
    return new Intl.NumberFormat("es-PE", {
      style: "currency",
      currency: "PEN",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(number);
  };

  return (
    <div className="w-full flex flex-col md:flex-row gap-4 md:gap-8">
      {/* Contenedor de barras */}
      <div className="block space-y-2 w-full md:w-1/2 relative">
        {/* Líneas verticales de fondo */}
        <div 
          className="absolute inset-0 w-full h-[calc(100%-1.5rem)]"
          style={{
            background: `
              repeating-linear-gradient(
                to right,
                #E5E7EB,
                #E5E7EB 1px,
                transparent 1px,
                transparent calc(${100/4}%)
              )
            `,
            opacity: 0.5,
          }}
        />
        
        {/* Barra amarilla superior con cartelito */}
        <div className="relative h-4 w-full">
          {/* Cartelito negro */}
          <div 
            className="absolute -top-7 text-white bg-black px-2 py-1 rounded text-xs whitespace-nowrap"
            style={{
              left: `${percentage}%`,
              transform: 'translateX(-50%)'
            }}
          >
            {formatNumber(usedBudget)}
          </div>
          <div 
            className="absolute h-full rounded-full transition-all duration-300 ease-in-out bg-[#FFE065]"
            style={{
              width: `${percentage}%`,
            }}
          >
            {/* Círculo azul al final de la barra amarilla */}
            <div 
              className="absolute -right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 rounded-full bg-[#156CFF] shadow-[0_0_3px_3px_rgba(21,108,255,0.3)]"
            />
          </div>
        </div>
        
        {/* Barra azul inferior fija */}
        <div className="relative h-4 w-full">
          <div className="absolute w-full h-full rounded-full bg-[#188fff41]" />
        </div>

        {/* Marcadores de porcentaje */}
        <div className="flex justify-between w-full mt-1 text-[0.625rem] md:text-xs text-gray-500">
          <span>0%</span>
          <span>25%</span>
          <span>50%</span>
          <span>75%</span>
          <span>100%</span>
        </div>
      </div>

      {/* Detalles a la derecha */}
      {showDetails && (
        <div className="flex flex-col justify-between w-full md:w-1/2 h-auto md:h-8 gap-2 md:gap-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-[#FFE065]" />
              <span className="text-xs md:text-sm text-gray-600">Presupuesto utilizado</span>
            </div>
            <span className="text-xs md:text-sm text-gray-600 ml-2">{formatNumber(usedBudget)}</span>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-[#188fff41]" />
              <span className="text-xs md:text-sm text-gray-600">Presupuesto total</span>
            </div>
            <span className="text-xs md:text-sm text-gray-600 ml-2">{formatNumber(totalBudget)}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default BudgetProgressBar;

