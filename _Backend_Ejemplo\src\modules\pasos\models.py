from django.db import models

from src.modules.niveles.models import Nivel

# Create your models here.
class Paso(models.Model):
    class Meta:
        db_table = 'tr_pasos'
        managed = True
        verbose_name = 'pasos'
        verbose_name_plural = 'pasoss'
        
    int_idPaso = models.AutoField(primary_key=True)
    int_idNivel = models.ForeignKey(Nivel, on_delete=models.CASCADE, db_column='int_idNivel', related_name="pasos")
    str_descripcion = models.TextField()
