from django.db import models

from src.modules.frameworks.models import Framework


# Create your models here.
class Dominio(models.Model):
    class Meta:
        db_table = "tr_dominio"
        managed = True
        verbose_name = "dominio"
        verbose_name_plural = "dominios"

    int_idDominio = models.AutoField(primary_key=True)
    str_nombre = models.CharField(max_length=255)
    int_idFramework = models.ForeignKey(
        Framework,
        on_delete=models.CASCADE,
        related_name="dominios",
        db_column="int_idFramework",
    )
