import React from "react";
import Modal from "../../../Components/Modal/Modal";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { ArrowDownTrayIcon } from "@heroicons/react/24/outline";

const EvidenciasModal = ({ isOpen, onClose, evidencias, onDownload, loading, position }) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      customWidth="24rem"
      customPosition={position}
    >
      <div className="w-full bg-white rounded-lg shadow-lg overflow-hidden">
        {/* Header with title and close button */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-800">Evidencias</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Table */}
        <div className="overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50 text-gray-500 text-xs uppercase">
                <th className="px-4 py-3 font-medium text-left w-[40px]">#</th>
                <th className="px-4 py-3 font-medium text-left">NOMBRE</th>
                <th className="px-4 py-3 font-medium text-center w-[100px]">ACCIONES</th>
              </tr>
            </thead>
            <tbody>
              {evidencias.map((evidencia, index) => (
                <tr key={evidencia.int_idEvidencia} className="border-t border-gray-200">
                  <td className="px-4 py-3 text-sm text-gray-700">
                    {index + 1}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-700">
                    <span className="truncate block max-w-[180px]" title={evidencia.str_nombre}>
                      {evidencia.str_nombre}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-center">
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        onDownload(evidencia.int_idEvidencia, evidencia.str_nombre);
                      }}
                      className="text-blue-500 hover:text-blue-700"
                      disabled={loading}
                    >
                      {loading ? (
                        <span className="inline-block animate-pulse">...</span>
                      ) : (
                        <ArrowDownTrayIcon className="h-5 w-5 inline-block" />
                      )}
                    </button>
                  </td>
                </tr>
              ))}
              {evidencias.length === 0 && (
                <tr className="border-t border-gray-200">
                  <td colSpan={3} className="px-4 py-3 text-sm text-gray-500 text-center">
                    No hay evidencias disponibles
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </Modal>
  );
};

export default EvidenciasModal;
