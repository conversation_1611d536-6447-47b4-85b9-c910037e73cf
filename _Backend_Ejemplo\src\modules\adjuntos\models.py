from django.db import models

from src.modules.pasos.models import Paso


# Create your models here.
class Adjunto(models.Model):
    class Meta:
        db_table = "tr_adjuntos"
        managed = True
        verbose_name = "adjunto"
        verbose_name_plural = "adjuntos"

    int_idAdjunto = models.AutoField(primary_key=True)
    str_nombre = models.CharField(max_length=255)
    str_peso = models.CharField(max_length=255)
    str_ruta = models.CharField(max_length=255)
    str_extension = models.CharField(max_length=255)
    int_idPaso = models.ForeignKey(
        Paso,
        models.CASCADE,
        related_name="adjuntos",
        db_column="int_idPaso",
    )
