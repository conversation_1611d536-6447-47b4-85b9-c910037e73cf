from django.db import models

# Create your models here.
class Usuario(models.Model):
    class Meta:
        db_table = 'tm_usuarios'
        managed = True
        verbose_name = 'Usuario'
        verbose_name_plural = 'Usuarios'
        
    int_idUsuarios = models.AutoField(primary_key=True)
    str_Nombres = models.CharField(max_length=254)
    str_Apellidos = models.CharField(max_length=255)
    str_Correo = models.CharField(max_length=150)
    str_Documento = models.CharField(max_length=20)
    str_UnidadNegocio = models.CharField(max_length=255)
    str_Clave = models.CharField(max_length=255)
    int_idEspecialidad = models.IntegerField()
    int_Estado = models.BooleanField()
    str_Codigo = models.CharField(max_length=8)
    dt_FechaCreacion = models.DateTimeField()
    dt_FechaModificacion = models.DateTimeField(null=True)
    int_idUsuarioCreacion = models.IntegerField()
    int_idUsuarioModificacion = models.IntegerField(null=True)
