"""
URL configuration for gestion_historicos project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from rest_framework import permissions

schema_view = get_schema_view(
    openapi.Info(
        title="Regulus Documentation",
        default_version="v0.1",
        description="Endpoints for Regulus API documentation",
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    path(
        "swagger/",
        schema_view.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui",
    ),
    path("categoria/", include("src.modules.categorias.urls")),
    path("framework/", include("src.modules.frameworks.urls")),
    path("dominio/", include("src.modules.dominios.urls")),
    path("subdominio/", include("src.modules.sub_dominios.urls")),
    path("controles/", include("src.modules.controles.urls")),
    path("niveles/", include("src.modules.niveles.urls")),
    path("pasos/", include("src.modules.pasos.urls")),
    path("plantilla/", include("src.modules.plantillas.urls")),
    path("evaluaciones/", include("src.modules.evaluaciones.urls")),
    path("detalles_evaluacion/", include("src.modules.detalles_evaluacion.urls")),
    path("evaluados/", include("src.modules.evaluados.urls")),
    path("adjuntos/", include("src.modules.adjuntos.urls")),
    path("evidencias/", include("src.modules.evidencias.urls")),
    
]
