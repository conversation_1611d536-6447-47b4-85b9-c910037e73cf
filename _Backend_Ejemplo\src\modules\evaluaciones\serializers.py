from rest_framework import serializers
from src.modules.empresas.serializers import EmpresaSerializer
from src.modules.frameworks.models import Framework
from src.modules.usuarios.models import Usuario
from src.modules.evaluados.serializers import EvaluadoReducidoSerializer
from .models import Evaluacion
from datetime import datetime


class FrameworkReducidoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Framework
        fields = ['int_idFramework', 'str_nombre', 'bool_estado']


class UsuarioReducidoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Usuario
        fields = ['int_idUsuarios', 'str_Nombres', 'str_Apellidos']


class EvaluacionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Evaluacion
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["int_idFramework"] = FrameworkReducidoSerializer(
            instance.int_idFramework
        ).data
        representation["int_idUsuarios"] = UsuarioReducidoSerializer(
            instance.int_idUsuarios
        ).data
        # representation["int_idEmpresa"] = EmpresaSerializer(
        #     instance.int_idEmpresa
        # ).data

        return representation

    def validate(self, data):
        """
        Valida que la fecha de fin sea posterior a la fecha de inicio
        """
        fecha_inicio = data.get("dt_fechaInicio")
        fecha_fin = data.get("dt_fechaFin")
        print(f'fecha_inicio: {fecha_inicio} fecha_fin: {fecha_fin}')

        if fecha_fin and fecha_inicio and fecha_fin < fecha_inicio:
            raise ValueError("La fecha de fin debe ser posterior a la fecha de inicio")

        return data
