from django.db import models

from src.modules.suscripciones.models import Suscripcion
from src.modules.categorias.models import Categoria


# Create your models here.
class Framework(models.Model):
    class Meta:
        db_table = "tm_frameworks"
        managed = True
        verbose_name = "framework"
        verbose_name_plural = "frameworks"

    int_idFramework = models.AutoField(primary_key=True)
    str_nombre = models.CharField(max_length=255)
    str_descripcion = models.CharField(max_length=255)
    bool_estado = models.BooleanField(default=True)
    int_idCategoria = models.ForeignKey(
        Categoria,
        db_column="int_idCategoria",
        related_name="frameworks",
        on_delete=models.CASCADE,
    )

    str_idSuscripcion = models.ForeignKey(
        Suscripcion,
        on_delete=models.CASCADE,
        db_column="str_idSuscripcion",
        related_name="frameworks",
    )
