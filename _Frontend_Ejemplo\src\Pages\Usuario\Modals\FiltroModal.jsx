import React, { useState, useEffect } from "react";
import ProyectosService from "../../../Services/ProyectosService";
import Select from "react-select";

const FiltroModal = ({ open, handleClose, handleSubmit, clearFiltersExternal, initialFilters = {} }) => {
  const [filters, setFilters] = useState({
    proyecto: "", // Cambiado de 'nombre' a 'proyecto' para coincidir con el backend
    fecha_inicio: "",
    fecha_fin: "",
    prioridad: null,
    estado: null
  });

  // Actualizar los filtros cuando se reciben filtros iniciales
  useEffect(() => {
    if (open && initialFilters) {
      setFilters(prev => ({
        ...prev,
        ...initialFilters
      }));
    }
  }, [open, initialFilters]);

  // Effect to handle external filter clearing
  useEffect(() => {
    if (clearFiltersExternal) {
      clearFilters();
    }
  }, [clearFiltersExternal]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePrioridadChange = (selectedOption) => {
    setFilters(prev => ({
      ...prev,
      prioridad: selectedOption ? selectedOption.value : null
    }));
  };

  const handleEstadoChange = (value) => {
    setFilters(prev => ({
      ...prev,
      estado: prev.estado === value ? null : value
    }));
  };

  const clearFilters = () => {
    // Reset all filters to their initial state
    setFilters({
      proyecto: "", // Cambiado de 'nombre' a 'proyecto' para coincidir con el backend
      fecha_inicio: "",
      fecha_fin: "",
      prioridad: null,
      estado: null
    });
  };

  const onSubmit = async () => {
    try {
      // Filter out empty values
      const filtersToSubmit = Object.fromEntries(
        Object.entries(filters).filter(([_, value]) =>
          value !== null && value !== ""
        )
      );

      // Call the API to filter tasks
      const filteredTasks = await ProyectosService.filterTareas(filtersToSubmit);
      console.log("Modal Filtro");
      console.log("Tareas filtradas Modal:", filteredTasks);

      // Check if any tasks were found
      if (filteredTasks.length === 0) {
        // Pass the empty array, error message, and applied filters to the parent component
        handleSubmit(filteredTasks, "No se encontraron tareas que coincidan con los filtros aplicados", filters);
      } else {
        // Pass the filtered tasks and applied filters to the parent component
        handleSubmit(filteredTasks, null, filters);
      }

      // Close the modal after applying filters
      handleClose();
    } catch (error) {
      console.error("Error al filtrar tareas:", error);

      // Check if it's a "not found" error
      if (error.isNotFound) {
        // Pass an empty array, the error message, and applied filters to the parent component
        handleSubmit([], error.message || "No se encontraron tareas que coincidan con los filtros aplicados", filters);
      } else {
        // For other errors, pass an error message and applied filters
        handleSubmit([], "Error al aplicar los filtros. Por favor, inténtalo de nuevo.", filters);
      }

      // Close the modal
      handleClose();
    }
  };

  // Expose clearFilters to parent component
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      window.clearFilterModal = clearFilters;
    }
    return () => {
      if (typeof window !== 'undefined') {
        delete window.clearFilterModal;
      }
    };
  }, []);

  if (!open) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto"
      onClick={handleClose} // Close when clicking outside
    >
      <div
        className="relative w-full max-w-[30rem] max-h-[50rem] m-4 md:m-0"
        onClick={e => e.stopPropagation()} // Prevent closing when clicking inside
      >
        <div className="bg-white rounded-xl shadow-xl p-6 relative">
          {/* Contenido del Modal */}
          <div className="p-4 mb-4">
            {/* Contenido principal del modal */}
            <h2 className="text-xl font-bold mb-4">Filtro de Tareas</h2>

            {/* Filtros */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Nombre del Proyecto
                </label>
                <input
                  type="text"
                  name="proyecto"
                  value={filters.proyecto}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                  placeholder="Buscar por nombre de proyecto"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Fecha de Inicio
                </label>
                <input
                  type="date"
                  name="fecha_inicio"
                  value={filters.fecha_inicio}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Fecha de Fin
                </label>
                <input
                  type="date"
                  name="fecha_fin"
                  value={filters.fecha_fin}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prioridad
                </label>
                <Select
                  options={[
                    { value: "1", label: "Alta" },
                    { value: "2", label: "Media" },
                    { value: "3", label: "Baja" }
                  ]}
                  placeholder="Seleccionar prioridad"
                  onChange={handlePrioridadChange}
                  className="mt-1"
                  classNamePrefix="react-select"
                  value={filters.prioridad ?
                    {
                      value: filters.prioridad,
                      label: filters.prioridad === "1" ? "Alta" :
                             filters.prioridad === "2" ? "Media" : "Baja"
                    } : null
                  }
                  styles={{
                    control: (baseStyles) => ({
                      ...baseStyles,
                      borderColor: '#D1D5DB',
                      borderRadius: '0.375rem',
                      minHeight: '2.5rem',
                      height: '2.5rem',
                      boxShadow: 'none',
                    }),
                    valueContainer: (baseStyles) => ({
                      ...baseStyles,
                      padding: '0 8px',
                      height: '2.5rem',
                    }),
                    input: (baseStyles) => ({
                      ...baseStyles,
                      margin: '0',
                      padding: '0',
                    }),
                    indicatorsContainer: (baseStyles) => ({
                      ...baseStyles,
                      height: '2.5rem md:6rem',
                      padding: '0 1px 0 0',
                    }),
                    dropdownIndicator: (baseStyles) => ({
                      ...baseStyles,
                      padding: '0 2px 0 2px',
                      width: '2rem',
                    }),
                    placeholder: (baseStyles) => ({
                      ...baseStyles,
                      color: '#6B7280',
                    }),
                    option: (baseStyles, { data, isFocused, isSelected }) => ({
                      ...baseStyles,
                      backgroundColor: isSelected
                        ? data.value === "1" ? '#FEF2F2'
                        : data.value === "2" ? '#FFFBEB'
                        : '#ECFDF5'
                        : isFocused ? '#F3F4F6' : 'transparent',
                      color: data.value === "1" ? '#EF4444'
                            : data.value === "2" ? '#F59E0B'
                            : '#10B981',
                      fontWeight: isSelected ? 500 : 400,
                    }),
                    singleValue: (baseStyles, { data }) => ({
                      ...baseStyles,
                      color: data.value === "1" ? '#EF4444'
                            : data.value === "2" ? '#F59E0B'
                            : '#10B981',
                      fontWeight: 500,
                    }),
                    menu: (baseStyles) => ({
                      ...baseStyles,
                      zIndex: 9999,
                    }),
                  }}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Estado
                </label>
                <div className="flex flex-wrap gap-4 items-center mt-2">
                  <div className="flex gap-2 items-center">
                    <input
                      type="checkbox"
                      id="estado-nuevo"
                      checked={filters.estado === "3"}
                      onChange={() => handleEstadoChange("3")}
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="estado-nuevo" className="text-sm font-medium text-gray-700">
                      Nuevo
                    </label>
                  </div>
                  <div className="flex gap-2 items-center">
                    <input
                      type="checkbox"
                      id="estado-proceso"
                      checked={filters.estado === "2"}
                      onChange={() => handleEstadoChange("2")}
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="estado-proceso" className="text-sm font-medium text-gray-700">
                      En Proceso
                    </label>
                  </div>
                  <div className="flex gap-2 items-center">
                    <input
                      type="checkbox"
                      id="estado-terminado"
                      checked={filters.estado === "1"}
                      onChange={() => handleEstadoChange("1")}
                      className="rounded border-gray-300"
                    />
                    <label htmlFor="estado-terminado" className="text-sm font-medium text-gray-700">
                      Terminado
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Botones de acción */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={clearFilters}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition cursor-pointer"
            >
              Limpiar
            </button>
            <button
              onClick={onSubmit}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition cursor-pointer"
            >
              Aplicar Filtros
            </button>
          </div>

          {/* Botón de cierre en la esquina superior derecha */}
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 text-gray-600 hover:text-gray-900 transition cursor-pointer"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default FiltroModal;
