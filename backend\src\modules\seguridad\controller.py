import requests
from src.utils.classes import Config, Response


class SeguridadController:
    def __init__(self) -> None:
        self.url = Config("SEGURIDAD").get_object_by_data("URL").data
        self.token = None
    
    def consultar_sesion(self, token: str, sesion_id: str) -> Response:
        """Consultar la sesión del usuario"""
        try:
            url = self.url + f"usuarios/info/session/{sesion_id}/app/reg/"
            headers = {"Authorization": token}
            response = requests.get(url, headers=headers, verify=False)
            
            if response.status_code != 200:
                return Response(
                    message="Error al consultar la sesión", 
                    data=response.json(), 
                    state=False
                )
                
            return Response(data=response.json(), state=True)
            
        except Exception as e:
            return Response(
                message="Error al consultar la sesión", 
                data=str(e), 
                state=False
            )
