from django.http import FileResponse
from rest_framework import status
from rest_framework.response import Response
from src.utils.classes import Response as ResponseAPI
from src.modules.gestor_archivos.controller import GestorArchivosController
from src.modules.detalles_evaluacion.models import DetalleEvaluacion
from .models import Evidencia
import os

class EvidenciaController:
    def __init__(self, archivo=None, detalle_id=None, *args, **kwargs):
        self.ruta_archivos = r'src\assets\evaluacion'
        
        self.archivo = archivo
        self.nombre_archivo_original = self.archivo.name
        self.nombre_archivo_base = os.path.splitext(self.nombre_archivo_original)[0]
        self.extension_archivo = os.path.splitext(self.nombre_archivo_original)[1]

        self.detalle_id = detalle_id
        self.evaluacion_id = DetalleEvaluacion.objects.filter(int_idDetalleEvaluacion=detalle_id).first().int_idEvaluacion.int_idEvaluacion
        self.evidencia_folder = os.path.join(
            self.ruta_archivos, f'{self.evaluacion_id}', 'Detalle', f'{self.detalle_id}', "Evidencias"
        )

    def registrar_evidencia_detalle(self):
        """
        Guarda el archivo en la carpeta correspondiente y registra el archivo en la base de datos.
        """
        try:
            # 1. Guarda el archivo en la carpeta correspondiente
            gestorArchivo = GestorArchivosController() 
            response = gestorArchivo.save_docs(
                self.evidencia_folder,    
                self.archivo,
                self.nombre_archivo_original, 
                self.nombre_archivo_base, 
                self.extension_archivo
                )

            # 2. Registra el archivo en la base de datos
            Evidencia(
                str_nombre=self.nombre_archivo_base,
                str_peso=self.archivo.size,
                str_ruta=response.data,
                str_extension=self.extension_archivo,
                int_idDetalleEvaluacion=DetalleEvaluacion.objects.filter(
                    int_idDetalleEvaluacion=self.detalle_id
                    ).first(),
            ).save()

            # 3. Retorna la respuesta
            return ResponseAPI("Guardado correctamente", None, state=True)
        except Exception as e:
            return ResponseAPI(str(e), e, state=False)
    

class EvidenciaDownloadController:
    def __init__(self, *args, **kwargs):
        ...

    def get_docs(self, path):
        if not os.path.exists(path):
            return Response("No se encontró el archivo", status=status.HTTP_404_NOT_FOUND)

        nombre_archivo = os.path.basename(path)

        archivo_response = FileResponse(open(path, "rb"), as_attachment=True)
        archivo_response["Content-Disposition"] = f'attachment; filename="{nombre_archivo}"'
        archivo_response["Content-Type"] = "application/octet-stream"

        return archivo_response
    

class EvidenciaDeleteController:
    def __init__(self, *args, **kwargs):
        ...

    def delete_adjunto(self, evidencia_id):
        evidencia = Evidencia.objects.filter(
            int_idEvidencia=evidencia_id
        ).first()  # Assume `id` is the primary key field name

        if not evidencia:
            raise ResponseAPI("Evidencia no encontrada.", None, state=False)

        ruta_evidencia = evidencia.str_ruta
        if ruta_evidencia:
            try:
                print(os.path.join(ruta_evidencia))
                os.remove(os.path.join(ruta_evidencia))
                print(f"Archivo eliminado: {ruta_evidencia}")
            except FileNotFoundError:
                print(
                    f"Archivo no encontrado en el sistema de archivos: {ruta_evidencia}"
                )
            except Exception as e:
                print(f"Error al eliminar el archivo: {e}")
                return ResponseAPI("Error al eliminar el archivo.", None, state=False)

        # Delete the record from the database
        evidencia.delete()
        return ResponseAPI("Archivo eliminado con éxito.", None, state=True)
    

