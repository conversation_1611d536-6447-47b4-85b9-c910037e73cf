from rest_framework import serializers
from .models import Evidencia
import os

class EvidenciaSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = Evidencia
        fields = "__all__"


class EvidenciaNombreSerializer(serializers.ModelSerializer):
    nombre_archivo = serializers.SerializerMethodField()

    class Meta:
        model = Evidencia
        fields = ['int_idEvidencia', 'nombre_archivo']

    def get_nombre_archivo(self, obj):
        if obj.str_ruta:
            return os.path.basename(obj.str_ruta)
        return None
