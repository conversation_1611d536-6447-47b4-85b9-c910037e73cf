from django.db import models
from src.modules.estado_financiero.models import EstadoFinanciero
from src.modules.ratios.models import Ratio

class RatioEF(models.Model):
    class Meta:
        db_table = 'tr_ratiosef'
        managed = True
        verbose_name = 'ratio ef'
        verbose_name_plural = 'ratios ef'

    int_idRatioEF = models.AutoField(primary_key=True)
    int_idEstadoFinanciero = models.ForeignKey(EstadoFinanciero, on_delete=models.CASCADE, db_column='int_idEstadoFinanciero', related_name='ratios_ef')
    db_valorRatio = models.DecimalField(max_digits=18, decimal_places=4)
    int_idRatios = models.ForeignKey(Ratio, on_delete=models.CASCADE, db_column='int_idRatios', related_name='ratios_ef')

    def __str__(self):
        return f"{self.int_idEstadoFinanciero} - {self.int_idRatios}: {self.db_valorRatio}"