import os
from typing import Any
from django.http import HttpResponse
from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from src.utils.classes import Response as APIResponse
from rest_framework.decorators import api_view
from drf_yasg.utils import swagger_auto_schema
from src.modules.frameworks.serializers import FrameworkPlantillaSerializer
from .models import Control
from .serializers import ControlSerializer
from rest_framework.decorators import action
from django.db.models import Q
import requests
from .controller import ControlController
from src.utils.classes import Config

from drf_yasg import openapi


class ControlView(viewsets.ModelViewSet):
    queryset = Control.objects.all()
    serializer_class = ControlSerializer
    permission_classes = [permissions.AllowAny]
    controller = ControlController()
    http_method_names = ["get","patch", "post"]

    @swagger_auto_schema(
        operation_description="Obtiene todos los controles de un subdominio"
    )
    @action(methods=["GET"], detail=False, url_path="subdominio/<int:subdominio_id>")
    def get_by_sub_dominio(self, request, subdominio_id, *args, **kwargs):
        try:
            response: APIResponse = self.controller.get_by_sub_dominio(subdominio_id)
            if not response.state:
                return Response(response.message, status=status.HTTP_400_BAD_REQUEST)
            return Response(
                data=ControlSerializer(response.data, many=True).data,
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(str(e), status=status.HTTP_400_BAD_REQUEST)
