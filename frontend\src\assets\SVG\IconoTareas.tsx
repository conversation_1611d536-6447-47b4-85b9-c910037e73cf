import React from "react";

const IconoTareas = ({size,color}) => {
  return (
    <svg
      enable-background="new 0 0 32 32"
      id="Layer_3"
      version="1.1"
      viewBox="0 0 32 32"
      xmlns="http://www.w3.org/2000/svg"
      height={size}

      width={size}
    >
      <g>
        <path d="M20,5h-1c0-1.654-1.346-3-3-3s-3,1.346-3,3h-1c-0.552,0-1,0.447-1,1v1H8.821C7.266,7,6,8.266,6,9.821v17.357   C6,28.734,7.266,30,8.821,30h14.357C24.734,30,26,28.734,26,27.179V9.821C26,8.266,24.734,7,23.179,7H21V6C21,5.447,20.552,5,20,5z    M16,4c0.551,0,1,0.448,1,1h-2C15,4.448,15.449,4,16,4z M13,7h6v2h-6V7z M23.179,9C23.631,9,24,9.368,24,9.821v17.357   C24,27.632,23.631,28,23.179,28H8.821C8.369,28,8,27.632,8,27.179V9.821C8,9.368,8.369,9,8.821,9H11v1c0,0.553,0.448,1,1,1h8   c0.552,0,1-0.447,1-1V9H23.179z" fill={color}/>
        <path d="M13,13h-3c-0.552,0-1,0.447-1,1v3c0,0.553,0.448,1,1,1h3c0.552,0,1-0.447,1-1v-3C14,13.447,13.552,13,13,13z M12,16h-1v-1   h1V16z" fill={color}/>
        <path d="M15,15.5c0,0.553,0.448,1,1,1h6c0.552,0,1-0.447,1-1s-0.448-1-1-1h-6C15.448,14.5,15,14.947,15,15.5z" />
        <path d="M13,20h-3c-0.552,0-1,0.447-1,1v3c0,0.553,0.448,1,1,1h3c0.552,0,1-0.447,1-1v-3C14,20.447,13.552,20,13,20z M12,23h-1v-1   h1V23z" fill={color}/>
        <path d="M22,21.5h-6c-0.552,0-1,0.447-1,1s0.448,1,1,1h6c0.552,0,1-0.447,1-1S22.552,21.5,22,21.5z" fill={color}/>
      </g>
    </svg>
  );
};

export default IconoTareas;
