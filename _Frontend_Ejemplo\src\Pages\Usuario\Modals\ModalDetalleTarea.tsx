import React, { useState, useRef, useEffect } from "react";
import Card from "../../../Components/Card/Card";
import ProgressCircle from "../../../Components/ProgressCircle/ProgressCircle";
import IconoUpload from "../../../assets/SVG/IconoUpload";
import IconoUploadFile from "../../../assets/SVG/IconoUploadFile";
import ProyectosService from "../../../Services/ProyectosService";
import Select from "react-select";

// Función para formatear fechas en formato DD/MM/YYYY
const formatearFecha = (fechaString: string | undefined): string => {
  if (!fechaString) return "-";

  try {
    // Verificar si la fecha tiene el formato esperado (YYYY-MM-DD)
    if (!/^\d{4}-\d{2}-\d{2}$/.test(fechaString)) return "-";

    // Dividir la fecha en sus componentes
    const [anio, mes, dia] = fechaString.split('-');

    // Retornar en formato DD/MM/YYYY
    return `${dia}/${mes}/${anio}`;
  } catch (error) {
    console.error("Error al formatear fecha:", error);
    return "-";
  }
};

// Función para formatear valores como moneda en soles
const formatearMoneda = (valor: string | number | undefined): string => {
  if (!valor) return "S/ 0.00";

  try {
    // Convertir a número si es string
    const numero = typeof valor === 'string' ? parseFloat(valor) : valor;

    // Verificar si es un número válido
    if (isNaN(numero)) return "S/ 0.00";

    // Formatear con 2 decimales y separador de miles
    return `S/ ${numero.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`;
  } catch (error) {
    console.error("Error al formatear moneda:", error);
    return "S/ 0.00";
  }
};

// Función para formatear valores como moneda en soles sin el prefijo "S/"
const formatearMonedaInput = (valor: string | number | undefined): string => {
  if (!valor) return "0.00";

  try {
    // Convertir a número si es string
    const numero = typeof valor === 'string' ? parseFloat(valor) : valor;

    // Verificar si es un número válido
    if (isNaN(numero)) return "0.00";

    // Formatear con 2 decimales y separador de miles
    return numero.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  } catch (error) {
    console.error("Error al formatear moneda para input:", error);
    return "0.00";
  }
};

interface TareaSeleccionada {
  id: number;
  titulo: string;
  nombre: string;
  descripcion: string;
  dependencia: string | null;
  horasTrabajadas: string;
  fechaInicio: string;
  fechaFin: string;
  fechaInicioReal: string;
  fechaFinReal: string;
  estado: string;
  presupuesto: string | number;
  gasto: string | number;
  avance: number;
  prioridad: number;
  prioridadNombre: string;
  prioridadColor: string;
  proyecto: {
    nombre: string;
  };
  usuario: string;
}

interface ModalDetalleTareaProps {
  openModal: boolean;
  setOpenModal: (open: boolean) => void;
  getEstadoConfig: (estado: string) => { texto: string; color: string };
  tareaSeleccionada: TareaSeleccionada;
  onTaskUpdated?: () => void; // Función opcional para notificar al componente padre que la tarea fue actualizada
}

const ModalDetalleTarea = ({
  openModal,
  setOpenModal,
  getEstadoConfig,
  tareaSeleccionada,
  onTaskUpdated,
}: ModalDetalleTareaProps) => {
  // State for form values
  const [descripcion, setDescripcion] = useState("");
  const [avance, setAvance] = useState("0");
  const [gasto, setGasto] = useState("0.00");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<string | null>(null);

  // State for loading and error handling
  const [isLoading, setIsLoading] = useState(false);
  const [isFinishingTask, setIsFinishingTask] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // References to form elements
  const descripcionRef = useRef<HTMLTextAreaElement>(null);
  const avanceRef = useRef<any>(null);
  const gastoRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Actualizar los estados cuando cambie la tarea seleccionada
  useEffect(() => {
    if (tareaSeleccionada) {
      setDescripcion(tareaSeleccionada.descripcion || "");
      setAvance(String(tareaSeleccionada.avance ? Math.round(tareaSeleccionada.avance * 100) : 0));
      setGasto(formatearMonedaInput(tareaSeleccionada.gasto || 0));
    }
  }, [tareaSeleccionada]);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
      setUploadStatus(`Archivo seleccionado: ${event.target.files[0].name}`);
    }
  };

  // Handle file drop
  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      setSelectedFile(event.dataTransfer.files[0]);
      setUploadStatus(`Archivo seleccionado: ${event.dataTransfer.files[0].name}`);
    }
  };

  // Handle drag events
  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(false);
  };

  // Handle click on upload button
  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Upload evidence file
  const uploadEvidenceFile = async () => {
    if (!selectedFile) {
      return null;
    }

    try {
      setUploadStatus("Subiendo archivo...");
      const response = await ProyectosService.uploadEvidencia(tareaSeleccionada.id, selectedFile);
      setUploadStatus("Archivo subido correctamente");
      setSelectedFile(null);
      return response;
    } catch (err) {
      console.error("Error uploading evidence:", err);
      setUploadStatus("Error al subir el archivo");
      setError(err instanceof Error ? err.message : "Error al subir la evidencia");
      return null;
    }
  };

  // Handle form submission
  const handleSaveChanges = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      // Usar los valores actuales de los estados
      const currentDescripcion = descripcion;
      const currentAvance = parseFloat(avance) / 100; // Convertir de porcentaje a decimal

      // Obtener el valor de gasto sin formato
      let currentGasto = gasto.replace(/,/g, '');

      // Prepare data for API call
      const updateData = {
        str_descripcion: currentDescripcion,
        db_avance: currentAvance,
        str_gasto: currentGasto
      };

      // Call the API to update the task
      await ProyectosService.updateTarea(tareaSeleccionada.id, updateData);

      // Upload evidence file if selected
      if (selectedFile) {
        await uploadEvidenceFile();
      }

      // Update local state
      setDescripcion(currentDescripcion);
      setAvance(String(Math.round(currentAvance * 100)));
      setGasto(formatearMonedaInput(currentGasto));

      setSuccess("Tarea actualizada correctamente");

      // Notificar al componente padre que la tarea fue actualizada
      if (onTaskUpdated) {
        onTaskUpdated();
      }

      // Close modal after a short delay
      setTimeout(() => {
        setOpenModal(false);
        setSuccess(null);
        setUploadStatus(null);
        setError(null);
      }, 1500);

    } catch (err) {
      console.error("Error updating task:", err);
      setError(err instanceof Error ? err.message : "Error al actualizar la tarea");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle finishing a task
  const handleFinishTask = async () => {
    try {
      setIsFinishingTask(true);
      setError(null);
      setSuccess(null);

      // Call the API to finish the task
      const result = await ProyectosService.terminarTarea(tareaSeleccionada.id);

      setSuccess(result.message || "Tarea terminada correctamente");

      // Notificar al componente padre que la tarea fue actualizada
      if (onTaskUpdated) {
        onTaskUpdated();
      }

      // Close modal after a short delay
      setTimeout(() => {
        setOpenModal(false);
        setSuccess(null);
        setError(null);
      }, 1500);

    } catch (err) {
      console.error("Error finishing task:", err);
      setError(err instanceof Error ? err.message : "Error al terminar la tarea");
    } finally {
      setIsFinishingTask(false);
    }
  };

  return (
    <>
      {openModal && tareaSeleccionada && (
        <div className="fixed inset-0 z-40 ">
          <div
            className="fixed inset-0 z-50 flex items-center bg-gray-500/50 justify-center "
            onClick={() => {setOpenModal(false); setSuccess(null); setError(null);}}
          >
            <div
              className="bg-white rounded-lg shadow-xl w-[95%] sm:w-[90%] md:w-[85%] lg:w-[80%] xl:w-[75%] p-4 sm:p-6 md:p-8 lg:p-10 relative max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex flex-col justify-between md:flex-row gap-4 md:gap-7 w-full min-h-[10rem]">
                <div className="w-full md:w-[80%]">
                  <Card
                    shadow={""}
                    bg_color={"#FFFFFF"}
                    className="shadow-md overflow-x-auto"
                    padding="p-3"
                  >
                    <div className="flex flex-row justify-between items-center w-full">
                      <div className="text-sm font-medium text-black">
                        {tareaSeleccionada.proyecto.nombre}
                      </div>
                      <span
                        className="py-2 px-6 min-w-[5rem] rounded-md text-white"
                        style={{
                          backgroundColor: getEstadoConfig(
                            tareaSeleccionada.estado
                          ).color,
                        }}
                      >
                        {getEstadoConfig(tareaSeleccionada.estado).texto}
                      </span>
                    </div>
                    <div className="flex flex-col gap-1 justify-start items-start mt-4">
                      <div className="text-normal font-medium text-black poppins-font-italic">
                        Tarea
                      </div>
                      <div className="text-lg font-medium text-black poppins-font-600">
                        {tareaSeleccionada.nombre}
                      </div>
                    </div>
                  </Card>
                </div>
                <div className="w-full md:w-[20%] flex justify-center items-center">
                  <div className="relative">
                    <ProgressCircle
                      percentage={tareaSeleccionada.avance ? tareaSeleccionada.avance * 100 : 0}
                      strokeWidth={10}
                      size={120}
                    />
                  </div>
                </div>
              </div>
              <div className="flex w-full mt-5">
                <div className="border-1 border-[#D0D5DD] rounded-[0.75rem] poppins-font overflow-x-auto w-full">
                  <div className="min-w-[640px]"> {/* Ensure minimum width for small screens */}
                    <table className="w-full">
                      <thead>
                        <tr>
                          <th className="px-3 sm:px-4 md:px-6 lg:px-10 py-3 sm:py-4 md:py-5 poppins-font-600 text-[#272727] cursor-pointer bg-[#FBFCFF] text-xs sm:text-sm md:text-base border-r-1 border-[#f9f0f0] text-left">
                            Descripción
                          </th>
                          <th className="px-3 sm:px-4 md:px-6 lg:px-10 py-3 sm:py-4 md:py-5 poppins-font-600 text-[#272727] cursor-pointer bg-[#FBFCFF] text-xs sm:text-sm md:text-base border-r-1 border-[#f9f0f0] text-left">
                            Fecha de Inicio
                          </th>
                          <th className="px-3 sm:px-4 md:px-6 lg:px-10 py-3 sm:py-4 md:py-5 poppins-font-600 text-[#272727] cursor-pointer bg-[#FBFCFF] text-xs sm:text-sm md:text-base border-r-1 border-[#f9f0f0] text-left">
                            Fecha de Fin
                          </th>
                          <th className="px-3 sm:px-4 md:px-6 lg:px-10 py-3 sm:py-4 md:py-5 poppins-font-600 text-[#272727] cursor-pointer bg-[#FBFCFF] text-xs sm:text-sm md:text-base border-r-1 border-[#f9f0f0] text-left">
                            Presupuesto
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="border-y-1 border-[#D0D5DD]">
                          <td className="poppins-font text-[#272727] py-3 sm:py-4 md:py-5 px-3 sm:px-4 md:px-6 lg:px-10 text-xs sm:text-sm md:text-base text-left border-r-1 border-[#f9f0f0]">
                            {tareaSeleccionada.descripcion}
                          </td>
                          <td className="poppins-font text-[#272727] py-3 sm:py-4 md:py-5 px-3 sm:px-4 md:px-6 lg:px-10 text-xs sm:text-sm md:text-base text-left border-r-1 border-[#f9f0f0]">
                            {formatearFecha(tareaSeleccionada.fechaInicio)}
                          </td>
                          <td className="poppins-font text-[#272727] py-3 sm:py-4 md:py-5 px-3 sm:px-4 md:px-6 lg:px-10 text-xs sm:text-sm md:text-base text-left border-r-1 border-[#f9f0f0]">
                            {formatearFecha(tareaSeleccionada.fechaFin)}
                          </td>
                          <td className="poppins-font text-[#272727] py-3 sm:py-4 md:py-5 px-3 sm:px-4 md:px-6 lg:px-10 text-xs sm:text-sm md:text-base text-left border-r-1 border-[#f9f0f0]">
                            {formatearMoneda(tareaSeleccionada.presupuesto)}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-col md:flex-row gap-4 md:gap-7 w-full mt-5">
                <div className="flex flex-col gap-3 w-full md:w-1/3 lg:w-[30%]">
                  <div className="text-sm font-medium text-black poppins-font-600">
                    Evidencias
                  </div>
                  <div
                    className={`flex flex-col border-1 ${isDragging ? 'border-[#2a9c5a]' : 'border-[#47D691]'} bg-[#33FF00]/5 px-3 py-4 sm:py-6 md:py-8 rounded-md justify-center items-center gap-2`}
                    onDrop={handleDrop}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                  >
                    {selectedFile ? (
                      <>
                        <IconoUploadFile size={"2.5rem"} color={"#47D691"} />
                        <div className="text-xs sm:text-sm font-medium text-[#47D691] poppins-font-600 text-center">
                          {selectedFile.name}
                        </div>
                      </>
                    ) : (
                      <>
                        <IconoUpload size={"2.5rem"} color={"#47D691"} />
                        <div className="text-xs sm:text-sm font-medium text-[#47D691] poppins-font-600 text-center">
                          Arrastre para subir
                        </div>
                      </>
                    )}
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileSelect}
                      className="hidden"
                    />
                    <button
                      className="bg-[#47D691] cursor-pointer text-white rounded-md w-full max-w-[120px] text-xs sm:text-sm py-1 px-3 sm:px-5 mt-3 flex items-center justify-center"
                      onClick={handleUploadClick}
                    >
                      Subir
                    </button>
                    {uploadStatus && (
                      <div className="text-xs mt-2 text-center">
                        {uploadStatus}
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex flex-col gap-3 w-full md:w-[40%] mt-4 md:mt-0">
                  <div className="text-sm font-medium text-black poppins-font-600">
                    Descripción
                  </div>
                  <textarea
                    ref={descripcionRef}
                    className="flex flex-col border-1 border-[#7BACFF] px-3 py-3 rounded-md justify-start items-start gap-1 outline-none h-[120px] md:h-full"
                    placeholder="Descripción del desarrollo de la tarea.."
                    value={descripcion}
                    onChange={(e) => setDescripcion(e.target.value)}
                  ></textarea>
                </div>
                <div className="flex flex-col gap-3 w-full md:w-1/3 lg:w-[30%] mt-4 md:mt-0">
                  {/* <div className="text-sm font-medium text-black poppins-font-600">
                    Estado
                  </div>
                  <div className="relative w-full">
                    <select
                      className="appearance-none w-full border-1 border-[#7BACFF] rounded-lg p-2 pr-8 outline-none md:text-base text-sm"
                      defaultValue={tareaSeleccionada.estado}
                    >
                      <option value="nuevo">Nuevo</option>
                      <option value="proceso">En Proceso</option>
                      <option value="terminado">Terminado</option>
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 border-l-1 border-[#7BACFF]">
                      <svg
                        className="fill-current h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                      >
                        <path
                          d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"
                          fill={"#7BACFF"}
                        />
                      </svg>
                    </div>
                  </div> */}
                  <div className="text-sm font-medium text-black poppins-font-600">
                    Avance
                  </div>
                  <div className="w-full">
                    <Select
                      ref={avanceRef}
                      value={{ value: avance, label: `${avance}%` }}
                      options={[
                        { value: "0", label: "0%" },
                        { value: "10", label: "10%" },
                        { value: "20", label: "20%" },
                        { value: "30", label: "30%" },
                        { value: "40", label: "40%" },
                        { value: "50", label: "50%" },
                        { value: "60", label: "60%" },
                        { value: "70", label: "70%" },
                        { value: "80", label: "80%" },
                        { value: "90", label: "90%" },
                        { value: "100", label: "100%" }
                      ]}
                      onChange={(option: any) => setAvance(option.value)}
                      className="w-full"
                      classNamePrefix="react-select"
                      maxMenuHeight={160} // Mostrar máximo 4 opciones
                      styles={{
                        control: (baseStyles) => ({
                          ...baseStyles,
                          borderColor: '#7BACFF',
                          borderRadius: '0.5rem',
                          minHeight: '2.5rem',
                          height: '2.5rem',
                          boxShadow: 'none',
                        }),
                        valueContainer: (baseStyles) => ({
                          ...baseStyles,
                          padding: '0 8px',
                          height: '2.5rem',
                        }),
                        input: (baseStyles) => ({
                          ...baseStyles,
                          margin: '0',
                          padding: '0',
                        }),
                        indicatorsContainer: (baseStyles) => ({
                          ...baseStyles,
                          height: '2.5rem',
                          padding: '0 1px 0 1px',
                        }),
                        dropdownIndicator: (baseStyles) => ({
                          ...baseStyles,
                          padding: '0 4px 0 2px',
                          width: '2rem',
                        }),
                        placeholder: (baseStyles) => ({
                          ...baseStyles,
                          color: '#91A8E2',
                        }),
                        option: (baseStyles, { isFocused }) => ({
                          ...baseStyles,
                          backgroundColor: isFocused ? '#F8FAFB' : 'transparent',
                          color: '#91A8E2',
                          padding: '8px 12px',
                          height: '40px', // Altura fija para cada opción
                        }),
                        menu: (baseStyles) => ({
                          ...baseStyles,
                          zIndex: 9999,
                        }),
                        menuList: (baseStyles) => ({
                          ...baseStyles,
                          maxHeight: '160px', // Altura para mostrar aproximadamente 4 opciones (40px por opción)
                        }),
                        singleValue: (baseStyles) => ({
                          ...baseStyles,
                          fontSize: '0.875rem',
                        }),
                      }}
                    />
                  </div>
                  <div className="text-sm font-medium text-black poppins-font-600 mt-2">
                    Costo
                  </div>
                  <div className="relative w-full">
                    <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-600">
                      S/
                    </span>
                    <input
                      ref={gastoRef}
                      type="text"
                      className="border-1 border-[#7BACFF] rounded-lg p-2 pl-8 w-full outline-none text-sm"
                      value={gasto}
                      placeholder="0.00"
                      onFocus={(e) => {
                        // Al recibir el foco, si el valor es "0.00", limpiar el campo
                        if (e.target.value === "0.00") {
                          e.target.value = "";
                        }
                      }}
                      onBlur={(e) => {
                        // Al perder el foco, asegurar que el valor tenga el formato correcto
                        try {
                          const value = e.target.value.replace(/[^0-9.]/g, '');
                          if (value) {
                            const num = parseFloat(value);
                            if (!isNaN(num)) {
                              const formattedValue = formatearMonedaInput(num);
                              setGasto(formattedValue);
                            } else {
                              setGasto("0.00");
                            }
                          } else {
                            setGasto("0.00");
                          }
                        } catch (error) {
                          console.error("Error al formatear valor en onBlur:", error);
                          setGasto("0.00");
                        }
                      }}
                      onChange={(e) => {
                        // Guardar la posición del cursor antes de modificar el valor
                        const cursorPosition = e.target.selectionStart || 0;
                        const oldValue = e.target.value;

                        // Eliminar todos los caracteres no numéricos excepto el punto decimal
                        let value = e.target.value.replace(/[^0-9.]/g, '');

                        // Si hay múltiples puntos decimales, conservar solo el primero
                        const parts = value.split('.');
                        if (parts.length > 2) {
                          value = parts[0] + '.' + parts.slice(1).join('');
                        }

                        // Si hay decimales, limitar a 2 dígitos
                        if (parts.length > 1 && parts[1].length > 2) {
                          value = parts[0] + '.' + parts[1].substring(0, 2);
                        }

                        // Si el valor es válido, formatearlo
                        let newValue = value;
                        if (value) {
                          const num = parseFloat(value);
                          if (!isNaN(num)) {
                            // Formatear con separadores de miles y 2 decimales
                            newValue = formatearMonedaInput(num);
                          }
                        } else {
                          // Si el campo está vacío, mostrar 0.00
                          newValue = "0.00";
                        }

                        // Actualizar el estado
                        setGasto(newValue);

                        // Calcular la nueva posición del cursor
                        if (oldValue !== newValue && cursorPosition > 0) {
                          try {
                            // Contar cuántas comas hay antes de la posición del cursor
                            const oldCommasBeforeCursor = (oldValue.substring(0, cursorPosition).match(/,/g) || []).length;
                            const newCommasBeforeCursor = (newValue.substring(0, cursorPosition).match(/,/g) || []).length;
                            const commaDiff = newCommasBeforeCursor - oldCommasBeforeCursor;

                            // Ajustar la posición del cursor considerando las comas añadidas/eliminadas
                            const newCursorPosition = cursorPosition + commaDiff;

                            // Usar setTimeout para asegurar que el setSelectionRange se ejecute después de que el valor se actualice
                            setTimeout(() => {
                              e.target.setSelectionRange(newCursorPosition, newCursorPosition);
                            }, 0);
                          } catch (error) {
                            console.error("Error al ajustar la posición del cursor:", error);
                          }
                        }
                      }}
                    />
                  </div>
                </div>
              </div>
              {/* Mostrar mensajes de error o éxito */}
              {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-3 py-2 sm:px-4 rounded mt-4 text-xs sm:text-sm text-center sm:text-left">
                  {error}
                </div>
              )}
              {success && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-3 py-2 sm:px-4 rounded mt-4 text-xs sm:text-sm text-center sm:text-left">
                  {success}
                </div>
              )}

              <div className="flex flex-col sm:flex-row justify-end gap-3 sm:gap-4 w-full mt-5">
                <button
                  className="bg-[#E9E9E9] cursor-pointer text-black rounded-md w-full sm:w-auto sm:max-w-[120px] text-xs sm:text-sm py-2 px-4"
                  onClick={() => {setOpenModal(false); setSuccess(null); setError(null)}}
                  disabled={isLoading || isFinishingTask}
                >
                  Cancelar
                </button>
                <button
                  className={`bg-[#DCFFD3] cursor-pointer text-black rounded-md w-full sm:w-auto sm:max-w-[120px] text-xs sm:text-sm py-2 px-4 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handleSaveChanges}
                  disabled={isLoading || isFinishingTask}
                >
                  {isLoading ? 'Guardando...' : 'Guardar'}
                </button>
                <button
                  className={`bg-[#47D691] cursor-pointer text-white rounded-md w-full sm:w-auto sm:max-w-[160px] text-xs sm:text-sm py-2 px-4 ${isFinishingTask ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handleFinishTask}
                  disabled={isLoading || isFinishingTask}
                >
                  {isFinishingTask ? 'Terminando...' : 'Terminar Tarea'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ModalDetalleTarea;
