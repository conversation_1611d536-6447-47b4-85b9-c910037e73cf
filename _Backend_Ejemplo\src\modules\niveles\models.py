from django.db import models

from src.modules.controles.models import Control


# Create your models here.
class Nivel(models.Model):
    class Meta:
        db_table = "tr_niveles"
        managed = True
        verbose_name = "nivel"
        verbose_name_plural = "niveles"

    int_idNivel = models.AutoField(primary_key=True)
    str_descripcion = models.TextField()
    int_valor = models.IntegerField()
    int_orden = models.SmallIntegerField()
    int_idControl = models.ForeignKey(
        Control,
        on_delete=models.CASCADE,
        db_column="int_idControl",
        related_name="niveles",
    )
