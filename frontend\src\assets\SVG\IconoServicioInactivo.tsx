import React from "react";

const IconoServicioInactivo = ({size}) => {
  return (
    <svg
      enable-background="new 0 0 64 64"
      height={size}
      version="1.1"
      viewBox="0 0 64 64"
      width={size}
      xml:space="preserve"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
    >
      <g id="Layer_1">
        <g>
          <circle cx="32" cy="32" fill="#C75C5C" r="32" />
        </g>
        <g opacity="0.2">
          <path
            d="M16.954,50c-4.4,0-6.2-3.118-4-6.928L28,17.012c2.2-3.811,5.8-3.811,8,0l15.046,26.06    c2.2,3.811,0.4,6.928-4,6.928H16.954z"
            fill="#231F20"
          />
        </g>
        <g>
          <path
            d="M16.954,48c-4.4,0-6.2-3.118-4-6.928L28,15.012c2.2-3.811,5.8-3.811,8,0l15.046,26.06    c2.2,3.811,0.4,6.928-4,6.928H16.954z"
            fill="#F5CF87"
          />
        </g>
        <g>
          <path
            d="M34,32c0,1.105-0.895,2-2,2l0,0c-1.105,0-2-0.895-2-2v-8c0-1.105,0.895-2,2-2l0,0c1.105,0,2,0.895,2,2V32z    "
            fill="#4F5D73"
          />
        </g>
        <g>
          <path
            d="M34,40c0,1.105-0.895,2-2,2l0,0c-1.105,0-2-0.895-2-2l0,0c0-1.105,0.895-2,2-2l0,0    C33.105,38,34,38.895,34,40L34,40z"
            fill="#4F5D73"
          />
        </g>
      </g>
      <g id="Layer_2" />
    </svg>
  );
};

export default IconoServicioInactivo;
