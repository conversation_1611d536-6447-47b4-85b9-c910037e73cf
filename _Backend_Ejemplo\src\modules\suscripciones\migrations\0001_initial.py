# Generated by Django 5.1 on 2025-03-31 17:04

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Suscripcion',
            fields=[
                ('str_idSuscripcion', models.Char<PERSON>ield(max_length=10, primary_key=True, serialize=False)),
                ('str_Nombre', models.Char<PERSON>ield(max_length=255)),
                ('dt_FechaCreacion', models.DateTimeField()),
                ('dt_FechaModificacion', models.DateTimeField(null=True)),
                ('int_idUsuarioCreacion', models.IntegerField()),
                ('int_idUsuarioModificacion', models.IntegerField(null=True)),
            ],
            options={
                'verbose_name': 'suscripcion',
                'verbose_name_plural': 'suscripciones',
                'db_table': 'tm_suscripcion',
                'managed': True,
            },
        ),
    ]
