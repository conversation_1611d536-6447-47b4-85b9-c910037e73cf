from rest_framework import viewsets, status, permissions
from rest_framework.response import Response
from rest_framework.decorators import action
from drf_yasg.utils import swagger_auto_schema
from .models import Evidencia
from .serializers import EvidenciaSerializer
from .controller import EvidenciaController, EvidenciaDownloadController, EvidenciaDeleteController
from src.utils.classes import Response as ResponseAPI


class EvidenciaView(viewsets.ModelViewSet):
    """
    ViewSet para gestionar las evidencias.
    """
    queryset = Evidencia.objects.all()
    serializer_class = EvidenciaSerializer
    permission_classes = [permissions.AllowAny]

    @swagger_auto_schema(operation_description="Crear un nueva evidencia de detalle evaluacion")
    @action(
        detail=False,
        methods=["post"],
        url_path="upload/detalle/<int:detalle_id>",
    )
    def subir_evidencia_detalle(self, request, *args, **kwargs):
        """
        Crear un nueva evidencia.
        """
        detalle_id = kwargs.get("detalle_id")
        file = request.FILES.get("archivo")
        controller = EvidenciaController(file, detalle_id)

        response: ResponseAPI = controller.registrar_evidencia_detalle()

        if not response.state:
            return Response(response.message, status=status.HTTP_400_BAD_REQUEST)

        return Response(response.message, status=status.HTTP_201_CREATED)


    @swagger_auto_schema(operation_description="Descargar adjunto")
    @action(
        detail=False,
        methods=["get"],
        url_path="<int:evidencia_id>/download",
    )
    def download_evidencia_detalle(self, request, *args, **kwargs):
        evidencia_id = kwargs.get("evidencia_id")
        controller = EvidenciaDownloadController()
        evidencia = Evidencia.objects.filter(int_idEvidencia=evidencia_id).first()
    
        if not evidencia:
            return Response(
                "No existe la evidencia.",
                status=status.HTTP_404_NOT_FOUND,
            )

        file = controller.get_docs(evidencia.str_ruta)

        return file
    
    @swagger_auto_schema(operation_description="Eliminar una evidencia")
    def destroy(self, request, *args, **kwargs):
        evidencia_id = self.kwargs.get("pk")  # Get the ID from the URL keyword arguments
        controller = EvidenciaDeleteController()
        response: ResponseAPI = controller.delete_adjunto(evidencia_id)

        return Response(
            response.message,
            status=status.HTTP_204_NO_CONTENT,
        )
    

