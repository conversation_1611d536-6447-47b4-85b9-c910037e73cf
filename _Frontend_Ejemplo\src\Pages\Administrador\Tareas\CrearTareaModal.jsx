import React, { useState, useEffect } from "react";
import Modal from "../../../Components/Modal/Modal";
import Input from "../../../Components/Input/Input";
import Button from "../../../Components/Button/Button";
import Select from "react-select";
import foto from "../../../assets/avatars/300-1.jpg";
import ProyectosService from "../../../Services/ProyectosService";

const CrearTareaModal = ({ isOpen, handleCloseModal, onSubmit, proyecto, tareas = [], isEditMode = false, editTaskData = null }) => {
  const [nombre, setNombre] = useState("");
  const [descripcion, setDescripcion] = useState("");
  const [fecha_inicio, setFecha_inicio] = useState("");
  const [fecha_fin, setFecha_fin] = useState("");
  const [presupuesto, setPresupuesto] = useState(0);
  const [tareaVinculada, setTareaVinculada] = useState(null);
  const [responsable, setResponsable] = useState(null);
  const [isDependiente, setIsDependiente] = useState(false);
  const [prioridad, setPrioridad] = useState(null);

  // Estados para cargar usuarios
  const [usuarios, setUsuarios] = useState([]);
  const [loadingUsuarios, setLoadingUsuarios] = useState(false);
  const [errorUsuarios, setErrorUsuarios] = useState(null);

  // Estados para validación y creación de tareas
  const [validationErrors, setValidationErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);

  // Opciones para el campo de prioridad
  const prioridadOptions = [
    { value: "1", label: "Alta" },
    { value: "2", label: "Media" },
    { value: "3", label: "Baja" }
  ];

  // Cargar usuarios cuando se abre el modal
  useEffect(() => {
    if (isOpen) {
      fetchUsuarios();
    }
  }, [isOpen]);

  // Reiniciar el estado de dependencia si no hay tareas disponibles
  useEffect(() => {
    if (tareas.length === 0 && isDependiente) {
      setIsDependiente(false);
      setTareaVinculada(null);
    }
  }, [tareas, isDependiente]);

  // Función para reiniciar todos los campos del formulario
  const resetForm = () => {
    setNombre("");
    setDescripcion("");
    setFecha_inicio("");
    setFecha_fin("");
    setPresupuesto(0);
    setTareaVinculada(null);
    setResponsable(null);
    setIsDependiente(false);
    setPrioridad(null);
    setValidationErrors({});
    setSubmitError(null);
  };

  // Reiniciar estados cuando se abre el modal o cargar datos de edición
  useEffect(() => {
    if (isOpen) {
      if (isEditMode && editTaskData) {
        console.log("editTaskData", editTaskData);
        // Cargar datos para edición
        setNombre(editTaskData.str_nombre || "");
        setDescripcion(editTaskData.str_descripcion || "");
        setPresupuesto(editTaskData.str_presupuesto || 0);
        setResponsable(editTaskData.int_idUsuarios ? editTaskData.int_idUsuarios.toString() : null);
        setFecha_inicio(editTaskData.dt_fechaInicio || "");
        setFecha_fin(editTaskData.dt_fechaFin || "");
        setPrioridad(editTaskData.int_idPrioridad ? editTaskData.int_idPrioridad.toString() : null);

        // No modificamos fecha_inicio y fecha_fin en modo edición
        // ya que no son campos editables según el requerimiento
      } else {
        // Reiniciar formulario para creación
        resetForm();
      }
    }
  }, [isOpen, isEditMode, editTaskData, usuarios]);

  // Cargar responsable despues de que se carguen los usuarios


  // Función para cargar usuarios
  const fetchUsuarios = async () => {
    setLoadingUsuarios(true);
    setErrorUsuarios(null);
    try {
      const data = await ProyectosService.getUsuarios();
      console.log("usuarios", data);
      setUsuarios(data);
    } catch (err) {
      console.error("Error fetching users:", err);
      setErrorUsuarios(err.message || "Error al cargar los usuarios");
    } finally {
      setLoadingUsuarios(false);
    }
  };

  // Función para validar los campos del formulario
  const validateForm = () => {
    const errors = {};

    if (!nombre.trim()) {
      errors.nombre = "El nombre de la tarea es obligatorio";
    }

    if (!descripcion.trim()) {
      errors.descripcion = "La descripción de la tarea es obligatoria";
    }

    // Solo validar fechas en modo creación, no en modo edición
    if (!isEditMode) {
      if (!fecha_inicio) {
        errors.fecha_inicio = "La fecha de inicio es obligatoria";
      } else if (proyecto?.fecha_inicio) {
        // Convertir fecha del proyecto de formato DD/MM/YYYY a YYYY-MM-DD para comparación
        const convertirFechaAFormatoISO = (fechaString) => {
          if (!fechaString || fechaString === "-") return null;
          const [dia, mes, anio] = fechaString.split('/');
          return `${anio}-${mes}-${dia}`;
        };

        const proyectoFechaInicio = convertirFechaAFormatoISO(proyecto.fecha_inicio);

        // Verificar que la fecha de inicio de la tarea no sea anterior a la fecha de inicio del proyecto
        if (proyectoFechaInicio && fecha_inicio < proyectoFechaInicio) {
          errors.fecha_inicio = "La fecha de inicio no puede ser anterior a la fecha de inicio del proyecto";
        }
      }

      if (!fecha_fin) {
        errors.fecha_fin = "La fecha de fin es obligatoria";
      } else if (fecha_inicio && fecha_fin && new Date(fecha_fin) < new Date(fecha_inicio)) {
        errors.fecha_fin = "La fecha de fin debe ser posterior a la fecha de inicio";
      } else if (proyecto?.fecha_fin) {
        // Convertir fecha del proyecto de formato DD/MM/YYYY a YYYY-MM-DD para comparación
        const convertirFechaAFormatoISO = (fechaString) => {
          if (!fechaString || fechaString === "-") return null;
          const [dia, mes, anio] = fechaString.split('/');
          return `${anio}-${mes}-${dia}`;
        };

        const proyectoFechaFin = convertirFechaAFormatoISO(proyecto.fecha_fin);

        // Verificar que la fecha de fin de la tarea no sea posterior a la fecha de fin del proyecto
        if (proyectoFechaFin && fecha_fin > proyectoFechaFin) {
          errors.fecha_fin = "La fecha de fin no puede ser posterior a la fecha de fin del proyecto";
        }
      }
    }

    if (!responsable) {
      errors.responsable = "Debe seleccionar un responsable";
    }

    if (!isEditMode && isDependiente && !tareaVinculada) {
      errors.tareaVinculada = "Debe seleccionar una tarea vinculada";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Función para actualizar una tarea existente
  const handleUpdateTask = async () => {
    // Validar el formulario
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Preparar datos para actualizar (solo los campos permitidos)
      const updateData = {
        str_descripcion: descripcion,
        // convertir a int
        int_idUsuarios: parseInt(responsable)
      };

      // Añadir campos opcionales solo si tienen valor
      if (presupuesto && presupuesto !== "0") {
        updateData.str_presupuesto = presupuesto.toString();
      }

      if (prioridad) {
        updateData.int_idPrioridad = parseInt(prioridad);
      }

      // Llamar a la API para actualizar la tarea
      await ProyectosService.updateTarea(editTaskData.id, updateData);

      // Cerrar el modal
      handleCloseModal();

      // Recargar las tareas (si hay una función onSubmit, la usamos para esto)
      if (onSubmit) {
        onSubmit();
      }
    } catch (error) {
      console.error("Error updating task:", error);
      setSubmitError(error.message || "Error al actualizar la tarea");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Función para manejar la creación de tarea a través de onSubmit
  const handleCreateTask = () => {
    // Validar el formulario
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Preparar datos para enviar
      const tareaData = {
        str_nombre: nombre,
        str_descripcion: descripcion,
        dt_fechaInicio: fecha_inicio,
        dt_fechaFin: fecha_fin,
        int_idUsuarios: responsable,
        int_idProyecto: proyecto?.id
      };

      // Añadir campos opcionales solo si tienen valor
      if (presupuesto && presupuesto !== "0") {
        tareaData.str_presupuesto = presupuesto.toString();
      }

      if (prioridad) {
        tareaData.int_idPrioridad = parseInt(prioridad);
      }

      if (isDependiente && tareaVinculada) {
        tareaData.int_idTareaPadre = parseInt(tareaVinculada);
      }

      // Llamar a la función onSubmit pasada como prop
      if (onSubmit) {
        onSubmit(tareaData);

        // Limpiar el formulario después de crear la tarea
        resetForm();

        // Cerrar el modal
        handleCloseModal();
      } else {
        setSubmitError("No se pudo crear la tarea: función de envío no disponible");
      }
    } catch (error) {
      console.error("Error preparing task data:", error);
      setSubmitError("Error al preparar los datos de la tarea");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Función que decide si crear o actualizar según el modo
  const handleSubmit = () => {
    if (isEditMode) {
      handleUpdateTask();
    } else {
      handleCreateTask();
    }
  };
  return (
    <Modal isOpen={isOpen} onClose={handleCloseModal} titulo={isEditMode ? "Editar Tarea" : "Nueva Tarea"} customWidth="75rem">
      <div className="w-full h-auto bg-white overflow-y-auto pb-4">
        <div className="flex flex-col md:flex-row w-full px-4 md:px-8 lg:px-13 gap-4 md:gap-8">
          {/* Left Column */}
          <div className="flex flex-col w-full md:w-1/2 space-y-4 md:space-y-6">
            {/* Nombre */}
            <div className="space-y-2">
              <div className="poppins-font-500">
                Nombre <span className="text-red-500 ml-1">*</span>
              </div>
              <Input
                placeholder="Nombre de Tarea"
                className={`text-[#91A8E2] border-[#7BACFF] border-1 w-full h-[2.5rem] rounded-[0.5rem] px-4 poppins-font ${
                  validationErrors.nombre ? "border-red-500" : ""
                } ${isEditMode ? "bg-gray-100" : ""}`}
                value={nombre}
                onChange={(e) => setNombre(e.target.value)}
                disabled={isEditMode}
              />
              {validationErrors.nombre && (
                <div className="text-red-500 text-xs">{validationErrors.nombre}</div>
              )}
            </div>

            {/* Fechas */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="space-y-2 w-full sm:w-1/2">
                <div className="flex poppins-font-500">
                  Fecha de Inicio
                  <span className="text-red-500 ml-1">*</span>
                </div>
                {proyecto?.fecha_inicio && (
                  <div className="text-gray-500 text-xs">
                    Fecha de inicio del proyecto: {proyecto.fecha_inicio}
                  </div>
                )}
                <Input
                  disabled={isEditMode}
                  placeholder="Seleccione una fecha"
                  type={"date"}
                  className={`text-[#91A8E2] text-sm border-[#7BACFF] border-1 w-full h-[2.5rem] rounded-[0.5rem] px-4 poppins-font ${
                    validationErrors.fecha_inicio ? "border-red-500" : ""
                  } ${isEditMode ? "bg-gray-100" : ""}`}
                  value={fecha_inicio}
                  onChange={(e) => {
                    const newDate = e.target.value;
                    setFecha_inicio(newDate);

                    // Validación inmediata de la fecha de inicio
                    if (proyecto?.fecha_inicio && newDate) {
                      const convertirFechaAFormatoISO = (fechaString) => {
                        if (!fechaString || fechaString === "-") return null;
                        const [dia, mes, anio] = fechaString.split('/');
                        return `${anio}-${mes}-${dia}`;
                      };

                      const proyectoFechaInicio = convertirFechaAFormatoISO(proyecto.fecha_inicio);

                      if (proyectoFechaInicio && newDate < proyectoFechaInicio) {
                        setValidationErrors(prev => ({
                          ...prev,
                          fecha_inicio: "La fecha de inicio no puede ser anterior a la fecha de inicio del proyecto"
                        }));
                      } else {
                        setValidationErrors(prev => {
                          const newErrors = {...prev};
                          delete newErrors.fecha_inicio;
                          return newErrors;
                        });
                      }
                    }
                  }}
                />
                {validationErrors.fecha_inicio && (
                  <div className="text-red-500 text-xs">{validationErrors.fecha_inicio}</div>
                )}
              </div>
              <div className="space-y-2 w-full sm:w-1/2">
                <div className="flex poppins-font-500">
                  Fecha de Fin
                  <span className="text-red-500 ml-1">*</span>
                </div>
                {proyecto?.fecha_fin && (
                  <div className="text-gray-500 text-xs">
                    Fecha de fin del proyecto: {proyecto.fecha_fin}
                  </div>
                )}
                <Input
                  disabled={isEditMode}
                  placeholder="Seleccione una fecha"
                  type={"date"}
                  className={`text-[#91A8E2] text-sm border-[#7BACFF] border-1 w-full h-[2.5rem] rounded-[0.5rem] px-4 poppins-font ${
                    validationErrors.fecha_fin ? "border-red-500" : ""
                  } ${isEditMode ? "bg-gray-100" : ""}`}
                  value={fecha_fin}
                  onChange={(e) => {
                    const newDate = e.target.value;
                    setFecha_fin(newDate);

                    // Validación inmediata de la fecha de fin
                    const errors = {};

                    // Verificar que la fecha de fin sea posterior a la fecha de inicio
                    if (fecha_inicio && newDate && new Date(newDate) < new Date(fecha_inicio)) {
                      errors.fecha_fin = "La fecha de fin debe ser posterior a la fecha de inicio";
                    }
                    // Verificar que la fecha de fin no sea posterior a la fecha de fin del proyecto
                    else if (proyecto?.fecha_fin && newDate) {
                      const convertirFechaAFormatoISO = (fechaString) => {
                        if (!fechaString || fechaString === "-") return null;
                        const [dia, mes, anio] = fechaString.split('/');
                        return `${anio}-${mes}-${dia}`;
                      };

                      const proyectoFechaFin = convertirFechaAFormatoISO(proyecto.fecha_fin);

                      if (proyectoFechaFin && newDate > proyectoFechaFin) {
                        errors.fecha_fin = "La fecha de fin no puede ser posterior a la fecha de fin del proyecto";
                      }
                    }

                    if (errors.fecha_fin) {
                      setValidationErrors(prev => ({
                        ...prev,
                        fecha_fin: errors.fecha_fin
                      }));
                    } else {
                      setValidationErrors(prev => {
                        const newErrors = {...prev};
                        delete newErrors.fecha_fin;
                        return newErrors;
                      });
                    }
                  }}
                />
                {validationErrors.fecha_fin && (
                  <div className="text-red-500 text-xs">{validationErrors.fecha_fin}</div>
                )}
              </div>
            </div>

            {/* Descripción */}
            <div className="space-y-2">
              <div className="flex poppins-font-500">
                Descripción <span className="text-red-500 ml-1">*</span>
              </div>
              <Input
                isTextArea={true}
                rows={6}
                disabled={false}
                placeholder="Descripción del desarrollo de la tarea"
                className={`text-[#91A8E2] border-[#7BACFF] border-1 w-full rounded-[0.5rem] px-4 py-2 poppins-font ${
                  validationErrors.descripcion ? "border-red-500" : ""
                }`}
                value={descripcion}
                onChange={(e) => setDescripcion(e.target.value)}
              />
              {validationErrors.descripcion && (
                <div className="text-red-500 text-xs">{validationErrors.descripcion}</div>
              )}
            </div>
          </div>

          {/* Right Column */}
          <div className="flex flex-col w-full md:w-1/2 space-y-4 md:space-y-6">
            {/* Responsable */}
            <div className="space-y-2">
              <div className="poppins-font-500">
                Responsable <span className="text-red-500 ml-1">*</span>
              </div>
              <Select
                placeholder={loadingUsuarios ? "Cargando usuarios..." : "Seleccione un nombre"}
                className={validationErrors.responsable ? "react-select-error" : ""}
                classNamePrefix="react-select"
                options={usuarios.map(user => ({ value: user.value, label: user.label }))}
                isDisabled={loadingUsuarios}
                value={responsable ? usuarios.find(user => user.value === responsable) : null}
                onChange={(option) => setResponsable(option.value)}
                styles={{
                  control: (baseStyles, state) => ({
                    ...baseStyles,
                    borderColor: validationErrors.responsable ? '#ef4444' : '#7BACFF',
                    borderRadius: '0.5rem',
                    minHeight: '2.5rem',
                    boxShadow: state.isFocused ? '0 0 0 1px #7BACFF' : 'none',
                    '&:hover': {
                      borderColor: state.isFocused ? '#7BACFF' : '#7BACFF'
                    }
                  }),
                  option: (baseStyles, { isSelected, isFocused }) => ({
                    ...baseStyles,
                    backgroundColor: isSelected ? '#F8FAFB' : isFocused ? '#F8FAFB' : 'white',
                    color: '#91A8E2',
                    fontFamily: 'Poppins, sans-serif',
                    '&:active': {
                      backgroundColor: '#F8FAFB'
                    }
                  }),
                  menu: (baseStyles) => ({
                    ...baseStyles,
                    zIndex: 9999,
                    maxHeight: '10rem'
                  }),
                  menuList: (baseStyles) => ({
                    ...baseStyles,
                    maxHeight: '10rem'
                  }),
                  singleValue: (baseStyles) => ({
                    ...baseStyles,
                    color: '#91A8E2',
                    fontFamily: 'Poppins, sans-serif'
                  }),
                  placeholder: (baseStyles) => ({
                    ...baseStyles,
                    color: '#91A8E2',
                    fontFamily: 'Poppins, sans-serif'
                  })
                }}
              />
              {errorUsuarios && (
                <div className="text-red-500 text-sm">{errorUsuarios}</div>
              )}
              {validationErrors.responsable && (
                <div className="text-red-500 text-xs">{validationErrors.responsable}</div>
              )}
            </div>

            {/* Presupuesto */}
            <div className="space-y-2">
              <div className="poppins-font-500">Presupuesto</div>
              <Input
                placeholder="Presupuesto de Tarea"
                type={"number"}
                className="border-[#7BACFF] text-[#91A8E2] border-1 w-full h-[2.5rem] rounded-[0.5rem] px-4 poppins-font"
                value={presupuesto}
                onChange={(e) => setPresupuesto(e.target.value)}
              />
            </div>

            {/* Tipo de Tarea - Solo visible en modo creación */}
            {!isEditMode && (
              <div className="space-y-2">
                <div className="flex poppins-font-500">Tipo de Tarea</div>
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                  <div
                    className={`flex gap-2 p-3 w-full sm:w-[9.375rem] h-[2.5rem] border-[#91A8E2] rounded-[0.5rem] border-1 items-center poppins-font flex-shrink-0 ${
                      tareas.length > 0
                        ? "cursor-pointer hover:bg-[#F8FAFB] transition-colors duration-200"
                        : "opacity-60 cursor-not-allowed"
                    }`}
                    onClick={() => {
                      if (tareas.length > 0) {
                        setIsDependiente(!isDependiente);
                      }
                    }}
                  >
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={isDependiente}
                        onChange={(e) => {
                          if (tareas.length > 0) {
                            setIsDependiente(e.target.checked);
                          }
                        }}
                        disabled={tareas.length === 0}
                        className={`form-checkbox h-4 w-4 rounded-full border-[#91A8E2] border-2 focus:ring-[#1890FF] transition duration-150 ease-in-out ${
                          tareas.length > 0 ? "cursor-pointer" : "cursor-not-allowed"
                        }`}
                      />
                    </div>
                    <div className="text-[#91A8E2] select-none text-sm">
                      Dependiente
                    </div>
                  </div>
                  <div className="flex-grow w-full">
                    <Select
                      isDisabled={!isDependiente || tareas.length === 0}
                      placeholder={
                        tareas.length === 0
                          ? "No hay tareas disponibles"
                          : "Seleccionar Tarea vinculada"
                      }
                      className={validationErrors.tareaVinculada && isDependiente ? "react-select-error" : ""}
                      classNamePrefix="react-select"
                      options={tareas.map((tarea) => ({
                        value: tarea.int_idTarea?.toString(),
                        label: tarea.str_nombre,
                      }))}
                      value={tareaVinculada ? tareas.map((tarea) => ({
                        value: tarea.int_idTarea?.toString(),
                        label: tarea.str_nombre,
                      })).find(option => option.value === tareaVinculada) : null}
                      onChange={(option) => setTareaVinculada(option.value)}
                      styles={{
                        control: (baseStyles, state) => ({
                          ...baseStyles,
                          borderColor: validationErrors.tareaVinculada && isDependiente ? '#ef4444' : '#7BACFF',
                          borderRadius: '0.5rem',
                          minHeight: '2.5rem',
                          boxShadow: state.isFocused ? '0 0 0 1px #7BACFF' : 'none',
                          '&:hover': {
                            borderColor: state.isFocused ? '#7BACFF' : '#7BACFF'
                          }
                        }),
                        option: (baseStyles, { isSelected, isFocused }) => ({
                          ...baseStyles,
                          backgroundColor: isSelected ? '#F8FAFB' : isFocused ? '#F8FAFB' : 'white',
                          color: '#91A8E2',
                          fontFamily: 'Poppins, sans-serif',
                          '&:active': {
                            backgroundColor: '#F8FAFB'
                          }
                        }),
                        menu: (baseStyles) => ({
                          ...baseStyles,
                          zIndex: 9999,
                          maxHeight: '10rem'
                        }),
                        menuList: (baseStyles) => ({
                          ...baseStyles,
                          maxHeight: '10rem'
                        }),
                        singleValue: (baseStyles) => ({
                          ...baseStyles,
                          color: '#91A8E2',
                          fontFamily: 'Poppins, sans-serif'
                        }),
                        placeholder: (baseStyles) => ({
                          ...baseStyles,
                          color: '#91A8E2',
                          fontFamily: 'Poppins, sans-serif'
                        })
                      }}
                    />
                  </div>
                </div>
                {tareas.length === 0 && (
                  <div className="text-gray-500 text-xs italic mt-1">
                    No hay tareas disponibles para vincular. Cree primero una tarea independiente.
                  </div>
                )}
                {validationErrors.tareaVinculada && isDependiente && (
                  <div className="text-red-500 text-xs">{validationErrors.tareaVinculada}</div>
                )}
              </div>
            )}

            {/* Prioridad */}
            <div className="space-y-2">
              <div className="flex poppins-font-500">Prioridad</div>
              <Select
                placeholder="Seleccione una opción"
                className="w-full"
                classNamePrefix="react-select"
                options={prioridadOptions}
                value={prioridad ? prioridadOptions.find(option => option.value === prioridad) : null}
                onChange={(option) => setPrioridad(option.value)}
                styles={{
                  control: (baseStyles, state) => ({
                    ...baseStyles,
                    borderColor: '#7BACFF',
                    borderRadius: '0.5rem',
                    minHeight: '2.5rem',
                    boxShadow: state.isFocused ? '0 0 0 1px #7BACFF' : 'none',
                    '&:hover': {
                      borderColor: state.isFocused ? '#7BACFF' : '#7BACFF'
                    }
                  }),
                  option: (baseStyles, { isSelected, isFocused, data }) => ({
                    ...baseStyles,
                    backgroundColor: isSelected ? '#F8FAFB' : isFocused ? '#F8FAFB' : 'white',
                    color: data.value === "1" ? "#FF4D4F" : data.value === "2" ? "#FAAD14" : data.value === "3" ? "#52C41A" : "#91A8E2",
                    fontWeight: isSelected ? 500 : 400,
                    fontFamily: 'Poppins, sans-serif',
                    '&:active': {
                      backgroundColor: '#F8FAFB'
                    }
                  }),
                  menu: (baseStyles) => ({
                    ...baseStyles,
                    zIndex: 9999,
                    maxHeight: '10rem'
                  }),
                  menuList: (baseStyles) => ({
                    ...baseStyles,
                    maxHeight: '10rem'
                  }),
                  singleValue: (baseStyles, { data }) => ({
                    ...baseStyles,
                    color: data.value === "1" ? "#FF4D4F" : data.value === "2" ? "#FAAD14" : data.value === "3" ? "#52C41A" : "#91A8E2",
                    fontWeight: 500,
                    fontFamily: 'Poppins, sans-serif'
                  }),
                  placeholder: (baseStyles) => ({
                    ...baseStyles,
                    color: '#91A8E2',
                    fontFamily: 'Poppins, sans-serif'
                  })
                }}
              />
            </div>
          </div>
        </div>

        {/* Error message */}
        {submitError && (
          <div className="w-full px-4 md:px-8 lg:px-13 mt-4">
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
              <span className="block sm:inline">{submitError}</span>
            </div>
          </div>
        )}

        {/* Priority note. Solo si no estamos en modo edición. */}
        {!isEditMode && (
          <div className="w-full px-4 md:px-8 lg:px-13 mt-2">
            <div className="text-gray-500 text-xs italic">
              Nota: Si no selecciona una prioridad, por defecto se creará como Prioridad Alta.
            </div>
          </div>
        )}

        {/* Buttons */}
        <div className="flex w-full px-4 md:px-8 lg:px-13 justify-end py-4 md:py-6 mt-2 md:mt-4">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-5 w-full sm:w-auto">
            <Button
              text_button="Cancelar"
              className="cursor-pointer bg-[#E9E9E9] poppins-font rounded-lg p-2 w-full sm:w-[7.1875rem]"
              accion={() => {
                handleCloseModal();
              }}
            />
            <Button
              text_button={isEditMode ? "Guardar" : "Crear"}
              className="cursor-pointer bg-[#1890FF] poppins-font-500 text-white rounded-lg p-2 w-full sm:w-[7.1875rem]"
              disabled={isSubmitting}
              accion={handleSubmit}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default CrearTareaModal;
