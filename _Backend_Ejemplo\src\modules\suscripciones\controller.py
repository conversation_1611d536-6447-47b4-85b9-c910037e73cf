from src.utils.classes import Response
from src.modules.suscripciones.models import Suscripcion


class SuscripcionController:
    def get_by_id(self, suscripcion_id: str):
        try:
            suscripcion = Suscripcion.objects.filter(
                str_idSuscripcion=suscripcion_id
            ).first()
            if not suscripcion:
                return Response("No se encontró la suscripción")
            return Response(suscripcion, state=True)
        except Exception as e:
            return Response(str(e), e)
