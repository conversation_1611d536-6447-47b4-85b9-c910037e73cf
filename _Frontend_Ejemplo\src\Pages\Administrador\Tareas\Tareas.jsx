import React, { useState, useEffect } from "react";
import Button from "../../../Components/Button/Button";
import Card from "../../../Components/Card/Card";
import { useGoTo } from "../../../Services/Globales";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import BudgetProgressBar from "../../../Components/BudgetProgressBar/BudgetProgressBar";
import ProgressCircle from "../../../Components/ProgressCircle/ProgressCircle";
import IconoAgregar from "../../../assets/SVG/IconoAgregar";
import CustomPaginator from "../../../Components/Paginator/Paginator";
import { ConfigSolid } from "../../../assets/SVG/ConfigSolid";
import FlagIcon from "../../../assets/SVG/FlagIcon";
import img_sin_proyecto from "../../../assets/SVG/wuu.svg";
import foto from "../../../assets/avatars/300-1.jpg";
import { RoutesPrivate } from "../../../Routes/ProtectedRoute";
import CrearTareaModal from "./CrearTareaModal";
import TareaModal from "./TareaModal";
import { useLocation } from "react-router-dom";
import ProyectosService from "../../../Services/ProyectosService";
import IconoEditar from "../../../assets/SVG/IconoEditar";

const Tareas = () => {
  const location = useLocation();
  const proyecto = location.state?.proyecto;

  const [openModal, setOpenModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [openEditModal, setOpenEditModal] = useState(false);
  const [editData, setEditData] = useState(null);
  const [tareas, setTareas] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [estadisticas, setEstadisticas] = useState({ presupuesto: 0, gasto_real: 0 });
  const [totalPages, setTotalPages] = useState(1);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editTaskData, setEditTaskData] = useState(null);

  const { goTo } = useGoTo();

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleCloseModal = () => {
    setOpenModal(!openModal);
    setIsEditMode(false);
    setEditTaskData(null);
  };
  const handleCloseEditModal = () => {
    setOpenEditModal(!openEditModal);
  };

  // Function to handle editing a task
  const handleEditTask = (tarea) => {
    console.log("tarea", tarea);
    setEditTaskData({
      id: tarea.int_idTarea,
      str_nombre: tarea.str_nombre,
      str_descripcion: tarea.str_descripcion,
      int_idUsuarios: tarea.int_idUsuarios?.id,
      dt_fechaInicio: tarea.dt_fechaInicio,
      dt_fechaFin: tarea.dt_fechaFin,
      str_presupuesto: tarea.str_presupuesto,
      int_idPrioridad: tarea.int_idPrioridad,
      int_idProyecto: tarea.int_idProyecto?.id
    });
    setIsEditMode(true);
    setOpenModal(true);
  };

  // Función para cargar tareas del proyecto
  const fetchTareas = async () => {
    if (!proyecto?.id) return;

    setLoading(true);
    setError(null);

    try {
      const data = await ProyectosService.getTareasByProyecto(proyecto.id);
      setTareas(data);
      // Calcular el número total de páginas (5 tareas por página)
      setTotalPages(Math.ceil(data.length / 5));
      // Resetear a la primera página cuando se cargan nuevas tareas
      setCurrentPage(1);
    } catch (err) {
      console.error("Error fetching tasks:", err);

      // No mostrar mensaje de error para el caso específico de "No se encontraron tareas"
      if (err.isNotFound) {
        // Solo establecer tareas como array vacío sin mostrar mensaje de error
        setError(null);
      } else {
        // Para otros errores, mostrar el mensaje
        setError(err.message || "Error al cargar las tareas");
      }

      setTareas([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // Función para crear una nueva tarea
  const handleCreateTask = async (tareaData) => {
    setLoading(true);
    setError(null);

    try {
      // Verificar que las fechas de la tarea estén dentro del rango del proyecto
      if (proyecto?.fecha_inicio && proyecto?.fecha_fin) {
        // Convertir fechas del proyecto de formato DD/MM/YYYY a YYYY-MM-DD para comparación
        const convertirFechaAFormatoISO = (fechaString) => {
          if (!fechaString || fechaString === "-") return null;
          const [dia, mes, anio] = fechaString.split('/');
          return `${anio}-${mes}-${dia}`;
        };

        const proyectoFechaInicio = convertirFechaAFormatoISO(proyecto.fecha_inicio);
        const proyectoFechaFin = convertirFechaAFormatoISO(proyecto.fecha_fin);
        const tareaFechaInicio = tareaData.dt_fechaInicio;
        const tareaFechaFin = tareaData.dt_fechaFin;

        // Verificar que la fecha de inicio de la tarea no sea anterior a la fecha de inicio del proyecto
        if (proyectoFechaInicio && tareaFechaInicio < proyectoFechaInicio) {
          throw new Error("La fecha de inicio de la tarea no puede ser anterior a la fecha de inicio del proyecto");
        }

        // Verificar que la fecha de fin de la tarea no sea posterior a la fecha de fin del proyecto
        if (proyectoFechaFin && tareaFechaFin > proyectoFechaFin) {
          throw new Error("La fecha de fin de la tarea no puede ser posterior a la fecha de fin del proyecto");
        }
      }

      // Llamar al servicio para crear la tarea
      await ProyectosService.createTarea(tareaData);

      // Recargar la lista de tareas después de crear una nueva
      await fetchTareas();

    } catch (err) {
      console.error("Error creating task:", err);
      setError(err.message || "Error al crear la tarea");
    } finally {
      setLoading(false);
    }
  };

  // Cargar tareas cuando cambia el proyecto
  useEffect(() => {
    fetchTareas();
  }, [proyecto]);

  // Paginar tareas - 5 tareas por página
  const paginatedTareas = tareas.slice(
    (currentPage - 1) * 5,
    currentPage * 5
  );

  // Fetch project statistics
  useEffect(() => {
    const fetchEstadisticas = async () => {
      if (!proyecto?.id) return;

      try {
        const data = await ProyectosService.getProyectoEstadisticas(proyecto.id);
        setEstadisticas(data);
      } catch (err) {
        console.error("Error fetching project statistics:", err);
        // We don't set error state here to avoid showing an error message
        // Just keep the default values
      }
    };

    fetchEstadisticas();
  }, [proyecto]);

  // Función para formatear fechas en formato DD/MM/YYYY sin usar Date
  const formatearFecha = (fechaString) => {
    if (!fechaString) return "-";

    try {
      // Verificar si la fecha tiene el formato esperado (YYYY-MM-DD)
      if (!/^\d{4}-\d{2}-\d{2}$/.test(fechaString)) return "-";

      // Dividir la fecha en sus componentes
      const [anio, mes, dia] = fechaString.split('-');

      // Retornar en formato DD/MM/YYYY
      return `${dia}/${mes}/${anio}`;
    } catch (error) {
      console.error("Error al formatear fecha:", error);
      return "-";
    }
  };

  const columns = [
    {
      key: "numeracion",
      header: "#",
    },
    {
      key: "nombre",
      header: "Nombre",
      render: (value) => <strong>{value}</strong>,
    },
    {
      key: "descripcion",
      header: "Descripcion",
    },
    {
      key: "fecha_inicio",
      header: "Fecha de Inicio",
    },
    {
      key: "inicio_real",
      header: "Inicio Real",
    },
    {
      key: "fecha_fin",
      header: "Fecha de Fin",
    },
    {
      key: "fin_real",
      header: "Fin Real",
    },
    {
      key: "responsable",
      header: "Responsable",
    },
    {
      key: "presupuesto",
      header: "Presupuesto",
    },
    {
      key: "horas_trabajadas",
      header: "Horas Trabajadas",
    },
    {
      key: "prioridad",
      header: "Prioridad",
    },
    {
      key: "estado",
      header: "Estado",
    },
    {
      key: "dependencia",
      header: "Dependencia",
    },
    {
      key: "acciones",
      header: "Acciones",
    },
  ];

  return (
    <div className="p-3 sm:p-4 md:p-5 w-full max-w-full">
      {/* Título */}
      <div className="flex justify-between gap-2 sm:gap-4 md:gap-8">
        <div className="text-black text-xl sm:text-2xl md:text-[1.25rem] font-normal poppins-font pb-3 sm:pb-4 md:pb-5 flex-1 md:flex-4/6">
          <div
            className="flex gap-2 cursor-pointer items-center"
            onClick={() => goTo(RoutesPrivate.INICIO)}
          >
            <ArrowLeftIcon className="size-4 sm:size-5" />
            <span className="text-base sm:text-lg md:text-xl">Proyectos</span>
          </div>
        </div>
      </div>

      {/* CARDS */}
      <div className="flex flex-col sm:flex-col md:flex-row gap-3 sm:gap-4 md:gap-7">
        {/* Proyecto Card */}
        <div className="w-full md:w-[36.9375rem] h-auto md:h-[10.5625rem] mb-3 sm:mb-4 md:mb-0">
          <Card
            bg_color={"FFFFFF"}
            className="w-full border-[#D0D5DD] border-1"
            padding="p-3 sm:p-4 md:p-6"
          >
            <div className="flex-row gap-2 sm:gap-3 md:gap-4 items-start justify-start">
              <div
                className={`flex gap-1 sm:gap-2 items-center poppins-font text-xs sm:text-sm md:text-[1.125rem] ${
                  proyecto?.estado === 2
                    ? "text-[#FFCE80]"
                    : proyecto?.estado === 3
                    ? "text-[#1890FF]"
                    : "text-[#A7FF90]"
                }`}
              >
                <div
                  className={`rounded-full size-[0.75rem] sm:size-[0.875rem] md:size-[1rem] ${
                    proyecto?.estado === 2
                      ? "bg-[#FFCE80]"
                      : proyecto?.estado === 3
                      ? "bg-[#1890FF]"
                      : "bg-[#A7FF90]"
                  }`}
                />
                {proyecto?.estado === 2
                  ? "En Proceso"
                  : proyecto?.estado === 3
                  ? "Nuevo"
                  : "Terminado"}
              </div>

              <div className="text-black text-base sm:text-lg md:text-[1.5rem] font-normal poppins-font-500 mt-2">
                {proyecto?.proyecto}
              </div>
            </div>
          </Card>
        </div>

        {/* Control de Gastos Card */}
        <div className="w-full md:w-[39rem] h-auto md:h-[10.5625rem] mb-3 sm:mb-4 md:mb-0">
          <Card
            bg_color={"FFFFFF"}
            shadow="None"
            className="w-full border-[#D0D5DD] border-1"
            padding="py-3 sm:py-4 md:py-[1.5rem] px-3 sm:px-4 md:px-[1.4375rem]"
          >
            <div className="flex-row items-start justify-start">
              <div className="text-black text-base sm:text-lg md:text-[1.25rem] poppins-font-500">
                Control de Gastos
              </div>
              <div className="py-2 sm:py-3 md:py-8">
                <BudgetProgressBar
                  totalBudget={estadisticas.presupuesto}
                  usedBudget={estadisticas.gasto_real}
                />
              </div>
            </div>
          </Card>
        </div>

        {/* Progress Circle Card */}
        <div className="bg-[#F8FAFB] border-1 border-[#D0D5DD] w-full sm:w-full md:w-[17.9375rem] h-[8rem] sm:h-[9rem] md:h-[10.5625rem] flex items-center justify-center">
          <ProgressCircle
            percentage={proyecto?.avance || 0}
            size={window.innerWidth < 640 ? 70 : window.innerWidth < 768 ? 80 : 90}
          />
        </div>
      </div>

      {/* Search y Filtrar */}
      <div className="flex flex-col sm:flex-col md:flex-row gap-2 pt-3 sm:pt-4 md:pt-5 justify-between items-center md:items-start">
        <div className="w-full md:w-[22.3125rem] mb-2 md:mb-0 poppins-font-600-italic text-xl sm:text-[1.5rem] md:text-[1.75rem] text-center md:text-left">
          Tareas
        </div>
        <div className="w-full sm:w-auto">
          <Button
            className="bg-[#1890FF] cursor-pointer text-white rounded-[0.5rem] w-full sm:w-[8.625rem] h-[2.5rem] text-[0.875rem]"
            icon={<IconoAgregar size="1.5rem" />}
            accion={() => {
              handleCloseModal();
            }}
            text_button="Nueva Tarea"
          />
        </div>
      </div>

      {/* Tabla o contenido principal */}
      <div className="pt-3 sm:pt-4 md:pt-5">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-3 sm:px-4 py-2 sm:py-3 rounded mb-3 sm:mb-4 text-sm sm:text-base">
            <p>{error}</p>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center py-8 sm:py-10">
            <div className="animate-spin rounded-full h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12 border-t-2 border-b-2 border-[#1890FF]"></div>
          </div>
        ) : (
          <div className="border-1 border-[#D0D5DD] rounded-[0.5rem] sm:rounded-[0.75rem] poppins-font">
            <div className="overflow-x-auto">
              <table className="w-full min-w-[800px]"> {/* Ensure minimum width for horizontal scrolling */}
                <thead>
                  <tr>
                    {columns.map((col) => (
                      <th
                        key={col.key}
                        className={`px-2 py-3 sm:py-4 md:py-5 poppins-font-600 text-[#272727] cursor-pointer bg-[#FBFCFF] text-xs sm:text-sm md:text-[0.875rem] border-r-1 border-[#f9f0f0] text-left ${
                          col.width || ""
                        }`}
                      >
                        {col.header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {tareas.length > 0 ? (
                  paginatedTareas.map((tarea, rowIndex) => (
                    <tr
                      key={tarea.int_idTarea || rowIndex}
                      className="border-y-1 border-[#D0D5DD] hover:bg-gray-50"
                    >
                      {/* Columna de numeración */}
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-center border-r-1 border-[#f9f0f0] text-gray-600">
                        {(currentPage - 1) * 5 + rowIndex + 1}
                      </td>

                      {/* Resto de las columnas */}
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-left border-r-1 border-[#f9f0f0] font-medium">
                        {tarea.str_nombre}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-left border-r-1 border-[#f9f0f0]">
                        {tarea.str_descripcion}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-left border-r-1 border-[#f9f0f0]">
                        {formatearFecha(tarea.dt_fechaInicio)}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-left border-r-1 border-[#f9f0f0]">
                        {formatearFecha(tarea.dt_fechaInicioReal)}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-left border-r-1 border-[#f9f0f0]">
                        {formatearFecha(tarea.dt_fechaFin)}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-left border-r-1 border-[#f9f0f0]">
                        {formatearFecha(tarea.dt_fechaFinReal)}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-left border-r-1 border-[#f9f0f0]">
                        <div className="flex gap-1 items-center">
                          {tarea.int_idUsuarios && (
                            <>
                              <div className="h-[1.5rem] w-[1.5rem] sm:h-[2rem] sm:w-[2rem] md:h-[2.3125rem] md:w-[2.25rem]">
                                <img
                                  src={foto}
                                  className="rounded-full w-full h-full object-cover"
                                  alt="Foto de perfil"
                                />
                              </div>
                              <div className="text-xs sm:text-sm truncate max-w-[80px] sm:max-w-[120px] md:max-w-full">
                                {`${tarea.int_idUsuarios.nombres} ${tarea.int_idUsuarios.apellidos}`}
                              </div>
                            </>
                          )}
                        </div>
                      </td>

                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-right border-r-1 border-[#f9f0f0]">
                        {tarea.str_presupuesto ? `S/ ${tarea.str_presupuesto}` : "-"}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-right border-r-1 border-[#f9f0f0]">
                        {tarea.str_horasTrabajadas || "0"}
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-left border-r-1 border-[#f9f0f0]">
                        <div className="flex gap-1 sm:gap-2 items-center">
                          {tarea.int_idPrioridad === 1 ? (
                            <div className="flex items-center">
                              <FlagIcon color="#FF4D4F" size={window.innerWidth < 640 ? 12 : 16} />
                              <span className="ml-1 text-xs sm:text-sm">Alta</span>
                            </div>
                          ) : tarea.int_idPrioridad === 2 ? (
                            <div className="flex items-center">
                              <FlagIcon color="#FFCE80" size={window.innerWidth < 640 ? 12 : 16} />
                              <span className="ml-1 text-xs sm:text-sm">Media</span>
                            </div>
                          ) : tarea.int_idPrioridad === 3 ? (
                            <div className="flex items-center">
                              <FlagIcon color="#1890FF" size={window.innerWidth < 640 ? 12 : 16} />
                              <span className="ml-1 text-xs sm:text-sm">Baja</span>
                            </div>
                          ) : (
                            ""
                          )}
                        </div>
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 border-r-1 border-[#f9f0f0]">
                        <div className="flex gap-1 sm:gap-2 items-center text-xs sm:text-sm md:text-left">
                          <div
                            className={`rounded-full ${
                              tarea.int_idEstado === 3
                                ? "bg-[#1890FF]"
                                : tarea.int_idEstado === 2
                                ? "bg-[#FFCE80]"
                                : tarea.int_idEstado === 1
                                ? "bg-[#47D691]"
                                : "-"
                            } w-[0.625rem] h-[0.625rem] sm:w-[0.75rem] sm:h-[0.75rem] md:w-[0.875rem] md:h-[0.875rem]`}
                          />
                          {tarea.int_idEstado === 3
                            ? "Nuevo"
                            : tarea.int_idEstado === 2
                            ? "En Proceso"
                            : tarea.int_idEstado === 1
                            ? "Terminada"
                            : "-"}
                        </div>
                      </td>
                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-left border-r-1 border-[#f9f0f0] truncate max-w-[100px] sm:max-w-[150px] md:max-w-full">
                        {tarea.int_idTareaPadre
                          ? tarea.int_idTareaPadre.nombre
                          : "Sin dependencia"}
                      </td>

                      <td className="py-3 sm:py-4 md:py-5 px-2 sm:px-3 md:px-4 text-xs sm:text-sm md:text-left">
                        {/* Botones de acción */}
                        <div className="flex gap-2">
                          {/* Botón de edición */}
                          <div
                            onClick={() => handleEditTask(tarea)}
                            className="cursor-pointer w-[1.5rem] h-[1.5rem] sm:w-[1.75rem] sm:h-[1.75rem] md:w-[2rem] md:h-[2rem] border-1 border-[#C1C0C0] bg-[#F8FAFB] rounded-[0.375rem] sm:rounded-[0.5rem] flex items-center justify-center"
                          >
                            <IconoEditar size="1rem" color="#C1C0C0" />
                          </div>

                          {/* Botón de configuración */}
                          <div
                            onClick={() => {
                              handleCloseEditModal();
                              setEditData({
                                id: tarea.int_idTarea,
                                nombre: tarea.str_nombre,
                                descripcion: tarea.str_descripcion,
                                fecha_inicio: formatearFecha(tarea.dt_fechaInicio),
                                fecha_fin: formatearFecha(tarea.dt_fechaFin),
                                inicio_real: formatearFecha(tarea.dt_fechaInicioReal),
                                fin_real: formatearFecha(tarea.dt_fechaFinReal),
                                avance: tarea.db_avance,
                                responsable: tarea.int_idUsuarios
                                  ? {
                                      nombre: `${tarea.int_idUsuarios.nombres} ${tarea.int_idUsuarios.apellidos}`,
                                      correo: tarea.int_idUsuarios.correo,
                                      id: tarea.int_idUsuarios.id,
                                      foto: foto
                                    }
                                  : null,
                                proyecto: tarea.int_idProyecto
                                  ? {
                                    id: tarea.int_idProyecto.id,
                                    nombre: tarea.int_idProyecto.nombre
                                    }
                                  : null,
                                presupuesto: tarea.str_presupuesto,
                                horas_trabajadas: tarea.str_horasTrabajadas,
                                prioridad: tarea.int_idPrioridad,
                                estado: tarea.int_idEstado,
                                dependencia: tarea.int_idTareaPadre ? tarea.int_idTareaPadre.id : null,
                              });
                            }}
                            className="cursor-pointer w-[1.5rem] h-[1.5rem] sm:w-[1.75rem] sm:h-[1.75rem] md:w-[2rem] md:h-[2rem] border-1 border-[#C1C0C0] bg-[#F8FAFB] rounded-[0.375rem] sm:rounded-[0.5rem] flex items-center justify-center"
                          >
                            <ConfigSolid color="#C1C0C0" />
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={columns.length}
                      className="text-center py-8 sm:py-12 md:py-16 text-gray-500 text-xs sm:text-sm md:text-base"
                    >
                      <div className="flex justify-center">
                        <img
                          src={img_sin_proyecto}
                          className="w-[8rem] h-[7rem] sm:w-[10rem] sm:h-[9rem] md:w-[11.8125rem] md:h-[10.75rem]"
                          alt="No hay tareas"
                        />
                      </div>
                      <div className="pt-2 sm:pt-3 md:pt-4">
                        <div className="poppins-font-500 text-[#272727] text-sm sm:text-base md:text-lg">
                          Parece que todavía no hay tareas registradas para este
                          proyecto
                        </div>
                        <div className="poppins-font-500 text-[#909090] text-xs sm:text-sm md:text-base">
                          ¡Es el momento perfecto para planificar!
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
              </table>
            </div>
          {tareas.length > 0 && (
            <div className="flex justify-center sm:justify-end px-2 sm:px-3 md:px-5 py-2 sm:py-3">
              <CustomPaginator
                totalPages={totalPages}
                currentPage={currentPage}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </div>
        )}
      </div>
      {/* Modales */}
      <CrearTareaModal
        isOpen={openModal}
        handleCloseModal={() => {
          handleCloseModal();
          // Recargar tareas después de cerrar el modal (tanto en creación como en edición)
          fetchTareas();
        }}
        proyecto={proyecto}
        tareas={tareas}
        onSubmit={isEditMode ? fetchTareas : handleCreateTask}
        isEditMode={isEditMode}
        editTaskData={editTaskData}
      />
      <TareaModal
        isOpen={openEditModal}
        handleCloseModal={handleCloseEditModal}
        editData={editData}
      />
    </div>
  );
};

export default Tareas;
