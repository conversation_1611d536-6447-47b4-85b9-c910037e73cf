import React from "react";

const Modal = ({
  isOpen,
  onClose,
  children,
  titulo = null,
  edidData = null,
  customPosition = null, // New prop for custom positioning
  customWidth = null // New prop for custom width
}) => {
  if (!isOpen) return null;

  const handleBackdropClick = (e) => {
    // Close modal when clicking on the backdrop
    if (onClose) {
      onClose();
    }
  };

  const handleContentClick = (e) => {
    // Prevent clicks inside the modal from closing it
    e.stopPropagation();
  };

  // Apply custom positioning styles if provided
  const customPositionStyle = customPosition ? {
    position: 'fixed',
    ...customPosition,
    transform: 'none',
    margin: 0
  } : {};

  // Apply custom width if provided
  const customWidthStyle = customWidth ? {
    maxWidth: customWidth
  } : { maxWidth: '75rem' };

  return (
    <div
      className="fixed inset-0 z-50 flex items-start sm:items-center justify-center overflow-x-hidden overflow-y-auto outline-none focus:outline-none bg-black/30 pt-4 sm:pt-0"
      onClick={handleBackdropClick}
    >
      <div
        className="relative w-full mx-4 md:mx-auto max-h-[90vh] my-4"
        onClick={handleContentClick}
        style={{...customWidthStyle, ...customPositionStyle}}
      >
        <div className="relative flex flex-col w-full bg-white rounded-[0.5rem] shadow-lg overflow-hidden max-h-[90vh]">
          {titulo && (
            <div className="poppins-font-600 text-[1.25rem] bg-[#F8FAFB] p-4 sm:p-6 m-2 sm:m-4 text-left sticky top-0 z-10">
              {titulo}
            </div>
          )}
          <div className="w-full overflow-y-auto">{children}</div>
        </div>
      </div>
    </div>
  );
};

export default Modal;
