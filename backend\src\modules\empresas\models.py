from django.db import models

class Empresa(models.Model):
    class Meta:
        db_table = 'tc_empresas'
        managed = True
        verbose_name = 'empresa'
        verbose_name_plural = 'empresas'
        
    int_idEmpresa = models.AutoField(primary_key=True)
    str_NombreEmpresa = models.CharField(max_length=255)
    str_RazonSocial = models.CharField(max_length=255)
    str_Ruc = models.CharField(max_length=25)
    dt_FechaCreacion = models.DateField()
    dt_FechaModificacion = models.DateTimeField(null=True)
    int_idUsuarioCreacion = models.IntegerField()
    int_idUsuarioModificacion = models.IntegerField(null=True)
    str_idSuscripcion = models.CharField(max_length=10, null=True)
    str_Pais = models.Char<PERSON>ield(max_length=100)
    str_Moneda = models.CharField(max_length=50)
    str_SimboloMoneda = models.Char<PERSON>ield(max_length=10)
    
    def __str__(self):
        return self.str_NombreEmpresa
