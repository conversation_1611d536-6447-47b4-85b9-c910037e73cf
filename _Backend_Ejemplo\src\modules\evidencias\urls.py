from django.urls import path, include
from rest_framework.routers import DefaultRouter

from src.modules.evidencias.views import EvidenciaView


router = DefaultRouter()
router.register("", EvidenciaView, basename="evidencia")

urlpatterns = [
    path("", include(router.urls)),
    path("upload/detalle/<int:detalle_id>/", EvidenciaView.as_view({"post": "subir_evidencia_detalle"})),
    path("<int:evidencia_id>/download/", EvidenciaView.as_view({"get": "download_evidencia_detalle"})),
]