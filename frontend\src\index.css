 
@import "tailwindcss";
 
body {
 margin: 0;
 padding: 0;
}

.poppins-font {
 font-family: "Poppins", sans-serif;
 font-optical-sizing: auto;
 font-weight: 400;
 font-style: normal;
}
.poppins-font-italic {
 font-family: "Poppins", sans-serif;
 font-optical-sizing: auto;
 font-weight: 400;
 font-style: italic;
}
.poppins-font-500 {
 font-family: "Poppins", sans-serif;
 font-optical-sizing: auto;
 font-weight: 500;
 font-style: normal;
}
.poppins-font-500-italic {
 font-family: "Poppins", sans-serif;
 font-optical-sizing: auto;
 font-weight: 500;
 font-style: italic;
}

.poppins-font-600 {
 font-family: "Poppins", sans-serif;
 font-optical-sizing: auto;
 font-weight: 600;
 font-style: normal;
}

.poppins-font-600-italic{
 font-family: "Poppins", sans-serif;
 font-optical-sizing: auto;
 font-weight: 600;
 font-style: italic;
}


/* Base 100% scaling (96dpi) */
@media screen and (max-resolution: 96dpi) {
 html {
   font-size: 16px; /* 1rem = 16px */
 }
}

/* 110% scaling (~105dpi) */
@media screen and (min-resolution: 105dpi) and (max-resolution: 110dpi) {
 html {
   font-size: 14.5px;
 }
}

/* 125% scaling (~120dpi) */
@media screen and (min-resolution: 120dpi) and (max-resolution: 143dpi) {
 html {
   font-size: 12.8px;
 }
}

/* 150% scaling (~144dpi) */
@media screen and (min-resolution: 144dpi) and (max-resolution: 159dpi) {
 html {
   font-size: 10.67px;
 }
}

/* 175% scaling (~168dpi) */
@media screen and (min-resolution: 160dpi) and (max-resolution: 191dpi) {
 html {
   font-size: 9.14px;
 }
}

/* 200% scaling (~192dpi) */
@media screen and (min-resolution: 192dpi) and (max-resolution: 239dpi) {
 html {
   font-size: 8px;
 }
}

/* 225% scaling (~216dpi) */
@media screen and (min-resolution: 240dpi) and (max-resolution: 263dpi) {
 html {
   font-size: 7.11px;
 }
}

/* 250% scaling (~240dpi) */
@media screen and (min-resolution: 264dpi) and (max-resolution: 287dpi) {
 html {
   font-size: 6.4px;
 }
}

/* 300% scaling (~288dpi) */
@media screen and (min-resolution: 288dpi) and (max-resolution: 335dpi) {
 html {
   font-size: 5.33px;
 }
}

/* 350% scaling (~336dpi) */
@media screen and (min-resolution: 336dpi) and (max-resolution: 383dpi) {
 html {
   font-size: 4.57px;
 }
}

/* 400% scaling (~384dpi) */
@media screen and (min-resolution: 384dpi) and (max-resolution: 431dpi) {
 html {
   font-size: 4px;
 }
}

/* 450% scaling (~432dpi) */
@media screen and (min-resolution: 432dpi) and (max-resolution: 479dpi) {
 html {
   font-size: 3.56px;
 }
}

/* 500% scaling (~480dpi) */
@media screen and (min-resolution: 480dpi) {
 html {
   font-size: 3.2px;
 }
}

/* Mobile adjustments */
@media screen and (max-width: 768px) {
 html {
   font-size: 14px; /* Ajuste para dispositivos móviles estándar */
 }
}

@media screen and (max-width: 480px) {
 html {
   font-size: 12px; /* Ajuste adicional para pantallas pequeñas */
 }

}

 
